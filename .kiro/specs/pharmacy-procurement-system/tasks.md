# 實作計劃

- [x] 1. 建立專案結構和核心介面
  - 建立 Rust 專案目錄結構，包含 models、services、repositories 和 API 元件
  - 定義系統邊界的核心介面和 trait
  - 設定 Cargo.toml 依賴項目
  - _需求: 6.1, 6.2_

- [-] 2. 實作資料模型和驗證
- [x] 2.1 建立核心資料模型介面和類型
  - 撰寫所有資料模型的 Rust 結構體和列舉
  - 實作資料驗證函數確保資料完整性
  - 建立單元測試驗證資料模型
  - _需求: 1.1, 2.1, 4.1_

- [x] 2.2 實作使用者模型和驗證
  - 撰寫 User 結構體和相關驗證方法
  - 建立密碼雜湊和驗證功能
  - 撰寫使用者模型的單元測試
  - _需求: 4.1, 4.2_

- [x] 2.3 實作產品模型和關聯
  - 撰寫 Product 結構體和庫存管理方法
  - 實作健保代碼驗證邏輯
  - 撰寫產品模型的單元測試
  - _需求: 1.1, 1.2, 2.5_

- [x] 2.4 實作訂單模型和項目關聯
  - 撰寫 Order 和 OrderItem 結構體
  - 實作訂單狀態管理和計算邏輯
  - 撰寫訂單模型的單元測試
  - _需求: 2.1, 2.3, 2.4_

- [ ] 3. 建立資料庫層
- [x] 3.1 實作資料庫連線工具
  - 撰寫 PostgreSQL 連線管理程式碼
  - 建立資料庫錯誤處理工具
  - 實作資料庫遷移系統
  - _需求: 6.1, 6.3_

- [x] 3.2 實作 Repository 模式進行資料存取
  - 撰寫基礎 Repository trait
  - 實作具體的 Repository 與 CRUD 操作
  - 撰寫 Repository 操作的單元測試
  - _需求: 1.1, 2.1, 4.1_

- [x] 3.3 建立資料庫遷移和初始化
  - 撰寫 SQL 遷移檔案建立所有表格
  - 實作資料庫初始化邏輯
  - 建立測試資料 fixtures
  - _需求: 6.1_

- [x] 4. 實作認證和授權系統
- [x] 4.1 建立 JWT 認證服務
  - 實作 JWT token 產生和驗證
  - 撰寫登入和註冊邏輯
  - 建立認證中介軟體
  - _需求: 4.1, 4.2, 4.3_

- [x] 4.2 實作使用者會話管理
  - 撰寫會話建立和驗證邏輯
  - 實作 token 刷新機制
  - 撰寫認證服務的單元測試
  - _需求: 4.2, 4.4_

- [ ] 5. 建立檔案處理服務
- [x] 5.1 實作 Excel 檔案解析
  - 使用 calamine crate 解析 Excel 檔案
  - 實作健保藥品資料格式驗證
  - 撰寫 Excel 處理的單元測試
  - _需求: 1.1, 1.3_

- [x] 5.2 實作 CSV 檔案解析
  - 使用 csv crate 解析 CSV 檔案
  - 實作資料格式轉換和驗證
  - 撰寫 CSV 處理的單元測試
  - _需求: 1.2, 1.3_

- [x] 5.3 建立檔案上傳和匯入服務
  - 實作檔案上傳 API 端點
  - 撰寫資料匯入邏輯和錯誤處理
  - 建立匯入結果統計功能
  - _需求: 1.1, 1.2, 1.4_

- [x] 6. 實作產品管理服務
- [x] 6.1 建立產品查詢和篩選功能
  - 實作產品清單 API 與分頁功能
  - 撰寫產品搜尋和篩選邏輯
  - 建立產品服務的單元測試
  - _需求: 2.1_

- [x] 6.2 實作庫存管理功能
  - 撰寫庫存更新和檢查邏輯
  - 實作庫存不足警告機制
  - 撰寫庫存管理的單元測試
  - _需求: 2.5_

- [-] 7. 建立訂單處理服務
- [x] 7.1 實作購物車功能
  - 撰寫購物車項目管理邏輯
  - 實作購物車狀態持久化
  - 建立購物車的單元測試
  - _需求: 2.2_

- [x] 7.2 實作訂單建立和管理
  - 撰寫訂單建立邏輯和驗證
  - 實作唯一訂單編號產生
  - 撰寫訂單狀態更新功能
  - _需求: 2.3, 2.4_

- [x] 7.3 建立訂單查詢和歷史記錄
  - 實作訂單查詢 API 與篩選功能
  - 撰寫訂單詳情檢視邏輯
  - 建立訂單服務的單元測試
  - _需求: 2.1_

- [x] 8. 實作通知服務
- [x] 8.1 建立 Email 通知功能
  - 使用 lettre crate 實作 Email 發送
  - 撰寫 Email 模板和內容產生
  - 實作 Email 發送失敗重試機制
  - _需求: 3.1, 3.3_

- [x] 8.2 建立 Line 通知功能
  - 整合 Line Bot API 進行訊息發送
  - 實作 Line 通知模板和格式化
  - 撰寫 Line 通知失敗處理邏輯
  - _需求: 3.2, 3.4_

- [x] 8.3 實作通知偏好設定和整合
  - 撰寫使用者通知偏好管理
  - 實作訂單確認通知觸發邏輯
  - 建立通知服務的單元測試
  - _需求: 3.5_

- [ ] 9. 建立備份系統
- [x] 9.1 實作資料庫備份功能
  - 撰寫 PostgreSQL 資料庫備份邏輯
  - 實作定時備份排程機制
  - 建立備份檔案壓縮和命名
  - _需求: 5.1_

- [x] 9.2 整合 GCP Cloud Storage
  - 實作備份檔案上傳到 Cloud Storage
  - 撰寫 GCP 認證和權限管理
  - 建立備份上傳的錯誤處理
  - _需求: 5.2_

- [x] 9.3 實作備份恢復和清理
  - 撰寫從備份檔案恢復資料庫功能
  - 實作舊備份檔案自動清理邏輯
  - 建立備份服務的單元測試
  - _需求: 5.4, 5.5_

- [-] 10. 建立 Web API 層
- [x] 10.1 設定 Axum 路由和中介軟體
  - 建立 API 路由結構和端點定義
  - 實作 CORS、日誌和錯誤處理中介軟體
  - 撰寫 API 回應格式標準化
  - _需求: 6.1, 6.2_

- [x] 10.2 實作認證相關 API 端點
  - 撰寫登入、註冊和 token 刷新 API
  - 實作使用者資訊查詢和更新端點
  - 建立認證 API 的整合測試
  - _需求: 4.1, 4.2, 4.3, 4.4_

- [x] 10.3 實作產品管理 API 端點
  - 撰寫產品查詢、搜尋和篩選 API
  - 實作檔案上傳和資料匯入端點
  - 建立產品 API 的整合測試
  - _需求: 1.1, 1.2, 1.3, 1.4, 2.1_

- [x] 10.4 實作訂單管理 API 端點
  - 撰寫購物車和訂單建立 API
  - 實作訂單查詢和狀態更新端點
  - 建立訂單 API 的整合測試
  - _需求: 2.1, 2.2, 2.3, 2.4, 2.5_

- [x] 11. 實作錯誤處理和日誌系統
- [x] 11.1 建立統一錯誤處理機制
  - 定義應用程式錯誤類型和處理邏輯
  - 實作錯誤回應格式標準化
  - 撰寫錯誤處理的單元測試
  - _需求: 6.3_

- [x] 11.2 整合日誌和監控系統
  - 使用 tracing crate 實作結構化日誌
  - 整合 GCP Cloud Logging 進行日誌收集
  - 建立關鍵操作的日誌記錄
  - _需求: 6.3_

- [x] 12. 建立測試套件
- [x] 12.1 撰寫整合測試
  - 建立完整的 API 端點整合測試
  - 實作資料庫操作的整合測試
  - 撰寫外部服務整合的模擬測試
  - _需求: 所有需求_

- [x] 12.2 建立端到端測試
  - 撰寫完整使用者流程的端到端測試
  - 實作檔案上傳和處理流程測試
  - 建立通知發送流程的端到端測試
  - _需求: 1.1-1.4, 2.1-2.5, 3.1-3.5_

- [ ] 13. 準備部署配置
- [ ] 13.1 建立 Docker 容器化配置
  - 撰寫 Dockerfile 和 docker-compose 檔案
  - 實作多階段建置優化映像大小
  - 建立容器健康檢查機制
  - _需求: 6.1_

- [ ] 13.2 設定 GCP 部署配置
  - 建立 Cloud Run 服務配置檔案
  - 實作環境變數和 Secret 管理
  - 撰寫 CI/CD 部署腳本
  - _需求: 6.1, 6.2, 6.4_

- [ ] 13.3 建立監控和告警設定
  - 設定 GCP Cloud Monitoring 指標
  - 實作系統健康檢查端點
  - 建立關鍵錯誤的告警機制
  - _需求: 6.2, 6.3_