#!/bin/bash

echo "🚀 開始執行資料庫遷移..."

# 檢查是否有.env檔案
if [ ! -f .env ]; then
    echo "❌ 找不到 .env 檔案，請先創建並設置 DATABASE_URL"
    exit 1
fi

# 載入環境變數
source .env

# 檢查DATABASE_URL是否設置
if [ -z "$DATABASE_URL" ]; then
    echo "❌ DATABASE_URL 未設置，請在 .env 檔案中設置"
    exit 1
fi

echo "✅ 環境變數載入完成"
echo "📊 資料庫URL: ${DATABASE_URL:0:20}..."

# 編譯並執行遷移
echo "🔨 編譯遷移工具..."
cargo build --bin simple_create_favorites

if [ $? -eq 0 ]; then
    echo "✅ 編譯成功"
    echo "🚀 執行遷移..."
    cargo run --bin simple_create_favorites
    
    if [ $? -eq 0 ]; then
        echo "🎉 遷移執行成功！"
        echo "✅ favorites 表已創建，現在可以使用我的最愛功能了"
    else
        echo "❌ 遷移執行失敗"
        exit 1
    fi
else
    echo "❌ 編譯失敗"
    exit 1
fi