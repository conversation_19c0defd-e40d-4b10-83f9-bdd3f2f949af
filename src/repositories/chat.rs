use crate::models::chat::{ChatMessage, ChatStats, ChatConversation};
use crate::error::AppError;
use sqlx::{MySqlPool, Row};
use chrono::Utc;

pub struct ChatRepository {
    pool: MySqlPool,
}

impl ChatRepository {
    pub fn new(pool: MySqlPool) -> Self {
        Self { pool }
    }

    /// 儲存用戶聊天訊息
    pub async fn save_user_message(
        &self,
        user_id: &str,
        user_name: &str,
        message: &str,
    ) -> Result<ChatMessage, AppError> {
        let now = Utc::now();
        
        let result = sqlx::query!(
            r#"
            INSERT INTO chat_messages (user_id, user_name, message, message_type, replied, created_at, updated_at, status)
            VALUES (?, ?, ?, 'user', FALSE, ?, ?, 'sent')
            "#,
            user_id,
            user_name,
            message,
            now,
            now
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("儲存聊天訊息失敗: {}", e)))?;

        let chat_message = ChatMessage {
            id: result.last_insert_id() as i64,
            user_id: user_id.to_string(),
            user_name: user_name.to_string(),
            admin_id: None,
            admin_name: None,
            message: message.to_string(),
            message_type: "user".to_string(),
            replied: false,
            created_at: now,
            updated_at: now,
            status: "sent".to_string(),
        };

        Ok(chat_message)
    }

    /// 儲存管理員回覆
    pub async fn save_admin_reply(
        &self,
        user_id: &str,
        admin_id: &str,
        admin_name: &str,
        message: &str,
    ) -> Result<ChatMessage, AppError> {
        let now = Utc::now();
        
        // 開始事務
        let mut tx = self.pool.begin().await
            .map_err(|e| AppError::DatabaseError(format!("開始事務失敗: {}", e)))?;

        // 插入管理員回覆
        let result = sqlx::query!(
            r#"
            INSERT INTO chat_messages (user_id, admin_id, admin_name, message, message_type, replied, created_at, updated_at, status)
            VALUES (?, ?, ?, ?, 'admin', FALSE, ?, ?, 'sent')
            "#,
            user_id,
            admin_id,
            admin_name,
            message,
            now,
            now
        )
        .execute(&mut *tx)
        .await
        .map_err(|e| AppError::DatabaseError(format!("儲存管理員回覆失敗: {}", e)))?;

        // 標記用戶訊息為已回覆
        sqlx::query!(
            r#"
            UPDATE chat_messages 
            SET replied = TRUE, updated_at = ?
            WHERE user_id = ? AND message_type = 'user' AND replied = FALSE
            "#,
            now,
            user_id
        )
        .execute(&mut *tx)
        .await
        .map_err(|e| AppError::DatabaseError(format!("更新訊息狀態失敗: {}", e)))?;

        // 提交事務
        tx.commit().await
            .map_err(|e| AppError::DatabaseError(format!("提交事務失敗: {}", e)))?;

        let chat_message = ChatMessage {
            id: result.last_insert_id() as i64,
            user_id: user_id.to_string(),
            user_name: "".to_string(), // 會在查詢時填入
            admin_id: Some(admin_id.to_string()),
            admin_name: Some(admin_name.to_string()),
            message: message.to_string(),
            message_type: "admin".to_string(),
            replied: false,
            created_at: now,
            updated_at: now,
            status: "sent".to_string(),
        };

        Ok(chat_message)
    }

    /// 獲取用戶的聊天記錄
    pub async fn get_user_messages(&self, user_id: &str) -> Result<Vec<ChatMessage>, AppError> {
        let messages = sqlx::query_as!(
            ChatMessage,
            r#"
            SELECT id, user_id, user_name, admin_id, admin_name, message, 
                   message_type, replied, created_at, updated_at, status
            FROM chat_messages 
            WHERE user_id = ? 
            ORDER BY created_at ASC
            "#,
            user_id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("載入用戶聊天記錄失敗: {}", e)))?;

        Ok(messages)
    }

    /// 獲取所有聊天記錄（管理員用）
    pub async fn get_all_messages(&self) -> Result<Vec<ChatMessage>, AppError> {
        let messages = sqlx::query_as!(
            ChatMessage,
            r#"
            SELECT id, user_id, user_name, admin_id, admin_name, message, 
                   message_type, replied, created_at, updated_at, status
            FROM chat_messages 
            ORDER BY created_at DESC
            "#
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("載入所有聊天記錄失敗: {}", e)))?;

        Ok(messages)
    }

    /// 獲取聊天統計信息
    pub async fn get_chat_stats(&self) -> Result<ChatStats, AppError> {
        let stats = sqlx::query!(
            r#"
            SELECT 
                COUNT(DISTINCT user_id) as total_conversations,
                COUNT(CASE WHEN message_type = 'user' AND replied = FALSE THEN 1 END) as unread_messages,
                COUNT(CASE WHEN message_type = 'user' AND replied = FALSE THEN 1 END) as pending_replies
            FROM chat_messages
            "#
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("獲取聊天統計失敗: {}", e)))?;

        Ok(ChatStats {
            total_conversations: stats.total_conversations.unwrap_or(0),
            unread_messages: stats.unread_messages.unwrap_or(0),
            pending_replies: stats.pending_replies.unwrap_or(0),
        })
    }

    /// 獲取聊天對話列表（管理員用）
    pub async fn get_chat_conversations(&self) -> Result<Vec<ChatConversation>, AppError> {
        let conversations = sqlx::query!(
            r#"
            SELECT 
                user_id,
                user_name,
                message as last_message,
                created_at as last_message_time,
                COUNT(CASE WHEN message_type = 'user' AND replied = FALSE THEN 1 END) as unread_count,
                CASE 
                    WHEN COUNT(CASE WHEN message_type = 'user' AND replied = FALSE THEN 1 END) > 0 THEN 'unread'
                    ELSE 'read'
                END as status
            FROM chat_messages cm1
            WHERE created_at = (
                SELECT MAX(created_at) 
                FROM chat_messages cm2 
                WHERE cm2.user_id = cm1.user_id
            )
            GROUP BY user_id, user_name, message, created_at
            ORDER BY created_at DESC
            "#
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("獲取聊天對話列表失敗: {}", e)))?;

        let result = conversations
            .into_iter()
            .map(|row| ChatConversation {
                user_id: row.user_id,
                user_name: row.user_name,
                last_message: row.last_message,
                last_message_time: row.last_message_time,
                unread_count: row.unread_count.unwrap_or(0),
                status: row.status.unwrap_or_else(|| "read".to_string()),
            })
            .collect();

        Ok(result)
    }

    /// 標記訊息狀態
    pub async fn update_message_status(
        &self,
        message_id: i64,
        status: &str,
    ) -> Result<(), AppError> {
        let now = Utc::now();
        
        sqlx::query!(
            r#"
            UPDATE chat_messages 
            SET status = ?, updated_at = ?
            WHERE id = ?
            "#,
            status,
            now,
            message_id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("更新訊息狀態失敗: {}", e)))?;

        Ok(())
    }

    /// 標記用戶所有訊息為已解決
    pub async fn mark_user_messages_resolved(&self, user_id: &str) -> Result<(), AppError> {
        let now = Utc::now();
        
        sqlx::query!(
            r#"
            UPDATE chat_messages 
            SET status = 'resolved', replied = TRUE, updated_at = ?
            WHERE user_id = ? AND status != 'resolved'
            "#,
            now,
            user_id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("標記用戶訊息為已解決失敗: {}", e)))?;

        Ok(())
    }
}