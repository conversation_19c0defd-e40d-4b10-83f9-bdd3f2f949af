use async_trait::async_trait;
use crate::database::{DatabaseResult, DatabaseError, DbPool};
use sqlx::{Transaction, Postgres};
use std::fmt::Debug;

/// 基礎 Repository trait，定義通用的 CRUD 操作
#[async_trait]
pub trait BaseRepository<T, ID>: Send + Sync + Debug
where
    T: Send + Sync,
    ID: Send + Sync,
{
    /// 建立新記錄
    #[allow(dead_code)]
    async fn create(&self, entity: T) -> DatabaseResult<T>;
    
    /// 根據 ID 查找記錄
    async fn find_by_id(&self, id: ID) -> DatabaseResult<Option<T>>;
    
    /// 更新記錄
    #[allow(dead_code)]
    async fn update(&self, id: ID, entity: T) -> DatabaseResult<T>;
    
    /// 刪除記錄
    #[allow(dead_code)]
    async fn delete(&self, id: ID) -> DatabaseResult<bool>;
    
    /// 列出記錄（分頁）
    #[allow(dead_code)]
    async fn list(&self, limit: Option<i32>, offset: Option<i32>) -> DatabaseResult<Vec<T>>;
}

/// 可查詢的 Repository trait
#[async_trait]
pub trait QueryableRepository<T, Filter>: Send + Sync
where
    T: Send + Sync,
    Filter: Send + Sync,
{
    /// 根據條件查詢記錄
    #[allow(dead_code)]
    async fn find_by_filter(&self, filter: Filter) -> DatabaseResult<Vec<T>>;
    
    /// 計算符合條件的記錄數量
    #[allow(dead_code)]
    async fn count_by_filter(&self, filter: Filter) -> DatabaseResult<i64>;
}

/// 支援交易的 Repository trait
#[async_trait]
pub trait TransactionalRepository: Send + Sync {
    /// 在交易中執行操作
    #[allow(dead_code)]
    async fn with_transaction<F, R>(&self, f: F) -> DatabaseResult<R>
    where
        F: for<'c> FnOnce(&mut Transaction<'c, Postgres>) -> std::pin::Pin<Box<dyn std::future::Future<Output = DatabaseResult<R>> + Send + 'c>> + Send,
        R: Send;
}

/// Repository 基礎實作結構
#[derive(Debug, Clone)]
pub struct RepositoryBase {
    pool: DbPool,
}

impl RepositoryBase {
    /// 建立新的 Repository 基礎實作
    pub fn new(pool: DbPool) -> Self {
        Self { pool }
    }

    /// 取得資料庫連線池
    pub fn pool(&self) -> &DbPool {
        &self.pool
    }

    /// 開始交易
    pub async fn begin_transaction(&self) -> DatabaseResult<Transaction<'_, Postgres>> {
        self.pool.begin()
            .await
            .map_err(|e| DatabaseError::Transaction(format!("Failed to begin transaction: {}", e)))
    }

    /// 執行查詢並處理錯誤
    pub async fn execute_query<F, R>(&self, operation: &str, f: F) -> DatabaseResult<R>
    where
        F: std::future::Future<Output = Result<R, sqlx::Error>>,
    {
        f.await.map_err(|e| {
            tracing::error!("Database operation '{}' failed: {}", operation, e);
            DatabaseError::from_sqlx_error(e, operation)
        })
    }

    /// 檢查記錄是否存在
    pub async fn exists_by_id(&self, table: &str, id: i64) -> DatabaseResult<bool> {
        let query = format!("SELECT COUNT(*) as count FROM {} WHERE id = $1", table);
        let count: i64 = self.execute_query(
            &format!("check existence in {}", table),
            sqlx::query_scalar(&query).bind(id).fetch_one(self.pool.as_ref())
        ).await?;
        
        Ok(count > 0)
    }

    /// 檢查唯一約束
    pub async fn check_unique_constraint(&self, table: &str, column: &str, value: &str, exclude_id: Option<i64>) -> DatabaseResult<bool> {
        let mut query = format!("SELECT COUNT(*) as count FROM {} WHERE {} = $1", table, column);
        let mut query_builder = sqlx::query_scalar(&query).bind(value);
        
        if let Some(id) = exclude_id {
            query = format!("SELECT COUNT(*) as count FROM {} WHERE {} = $1 AND id != $2", table, column);
            query_builder = sqlx::query_scalar(&query).bind(value).bind(id);
        }
        
        let count: i64 = self.execute_query(
            &format!("check unique constraint for {}.{}", table, column),
            query_builder.fetch_one(self.pool.as_ref())
        ).await?;
        
        Ok(count == 0)
    }
}

#[async_trait]
impl TransactionalRepository for RepositoryBase {
    async fn with_transaction<F, R>(&self, f: F) -> DatabaseResult<R>
    where
        F: for<'c> FnOnce(&mut Transaction<'c, Postgres>) -> std::pin::Pin<Box<dyn std::future::Future<Output = DatabaseResult<R>> + Send + 'c>> + Send,
        R: Send,
    {
        let mut tx = self.begin_transaction().await?;
        
        match f(&mut tx).await {
            Ok(result) => {
                tx.commit().await.map_err(|e| {
                    DatabaseError::Transaction(format!("Failed to commit transaction: {}", e))
                })?;
                Ok(result)
            }
            Err(e) => {
                if let Err(rollback_err) = tx.rollback().await {
                    tracing::error!("Failed to rollback transaction: {}", rollback_err);
                }
                Err(e)
            }
        }
    }
}

/// Repository 工廠，用於建立各種 Repository 實例
#[derive(Debug, Clone)]
#[allow(dead_code)]
pub struct RepositoryFactory {
    base: RepositoryBase,
}

#[allow(dead_code)]
impl RepositoryFactory {
    /// 建立新的 Repository 工廠
    pub fn new(pool: DbPool) -> Self {
        Self {
            base: RepositoryBase::new(pool),
        }
    }

    /// 取得基礎 Repository
    pub fn base(&self) -> &RepositoryBase {
        &self.base
    }

    /// 取得資料庫連線池
    pub fn pool(&self) -> &DbPool {
        self.base.pool()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;
    use crate::database::Database;

    async fn create_test_repository() -> RepositoryBase {
        // Note: This test requires a running PostgreSQL instance
        let database_url = "postgresql://postgres:password@localhost:5432/test_db";
        let database = Database::new(database_url).await.unwrap();
        RepositoryBase::new(database.pool().clone())
    }

    #[tokio::test]
    async fn test_repository_base_creation() {
        let repo = create_test_repository().await;
        assert!(repo.pool().size() > 0);
    }

    #[tokio::test]
    async fn test_exists_by_id() {
        let repo = create_test_repository().await;
        
        // 測試不存在的記錄
        let exists = repo.exists_by_id("users", 999).await.unwrap();
        assert!(!exists);
    }

    #[tokio::test]
    async fn test_check_unique_constraint() {
        let repo = create_test_repository().await;
        
        // 測試唯一約束檢查
        let is_unique = repo.check_unique_constraint("users", "username", "nonexistent_user", None).await.unwrap();
        assert!(is_unique);
    }

    #[tokio::test]
    async fn test_transaction() {
        let repo = create_test_repository().await;
        
        let result = repo.with_transaction(|_tx| {
            Box::pin(async move {
                // 模擬交易操作
                Ok("transaction_result".to_string())
            })
        }).await;
        
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "transaction_result");
    }
}