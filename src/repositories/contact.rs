use crate::{
    database::DbPool,
    error::AppError,
};
use async_trait::async_trait;
use sqlx::Row;

#[async_trait]
pub trait ContactRepository: Send + Sync {
    async fn get_user_messages(&self, user_id: i64) -> Result<Vec<serde_json::Value>, AppError>;
    async fn get_all_messages(&self) -> Result<Vec<serde_json::Value>, AppError>;
    async fn create_message(&self, user_id: i64, subject: String, message: String) -> Result<serde_json::Value, AppError>;
    async fn create_admin_reply(&self, message_id: i64, admin_id: i64, reply_message: String) -> Result<serde_json::Value, AppError>;
    async fn ensure_table_exists(&self) -> Result<(), AppError>;
}

pub struct PostgresContactRepository {
    pool: DbPool,
}

impl PostgresContactRepository {
    pub fn new(pool: DbPool) -> Self {
        Self { pool }
    }

}

#[async_trait]
impl ContactRepository for PostgresContactRepository {
    async fn ensure_table_exists(&self) -> Result<(), AppError> {
        // 創建表格
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS messageboard (
                id BIGSERIAL PRIMARY KEY,
                user_id BIGINT NOT NULL,
                subject VARCHAR(200) NOT NULL,
                message TEXT NOT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'pending',
                admin_id BIGINT,
                admin_reply TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (admin_id) REFERENCES users(id)
            )
            "#
        )
        .execute(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to create messageboard table: {}", e);
            AppError::Database(e)
        })?;

        // 分別創建索引
        let indices = vec![
            "CREATE INDEX IF NOT EXISTS idx_messageboard_user_id ON messageboard(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_messageboard_status ON messageboard(status)",
            "CREATE INDEX IF NOT EXISTS idx_messageboard_created_at ON messageboard(created_at)",
        ];

        for index_sql in indices {
            sqlx::query(index_sql)
                .execute(&*self.pool)
                .await
                .map_err(|e| {
                    tracing::error!("Failed to create index: {}", e);
                    AppError::Database(e)
                })?;
        }

        tracing::info!("Messageboard table and indices ensured to exist");
        Ok(())
    }

    async fn get_user_messages(&self, user_id: i64) -> Result<Vec<serde_json::Value>, AppError> {
        let rows = sqlx::query(
            r#"
            SELECT 
                id,
                subject,
                message,
                status,
                admin_id,
                admin_reply,
                created_at,
                updated_at
            FROM messageboard
            WHERE user_id = $1
            ORDER BY created_at DESC
            "#
        )
        .bind(user_id)
        .fetch_all(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to fetch contact messages: {}", e);
            AppError::Database(e)
        })?;

        let messages = rows.into_iter().map(|row| {
            serde_json::json!({
                "id": row.get::<i64, _>("id"),
                "subject": row.get::<String, _>("subject"),
                "message": row.get::<String, _>("message"),
                "status": row.get::<String, _>("status"),
                "admin_id": row.try_get::<Option<i64>, _>("admin_id").unwrap_or(None),
                "admin_reply": row.try_get::<Option<String>, _>("admin_reply").unwrap_or(None),
                "created_at": row.get::<chrono::DateTime<chrono::Utc>, _>("created_at"),
                "updated_at": row.get::<chrono::DateTime<chrono::Utc>, _>("updated_at")
            })
        }).collect();

        Ok(messages)
    }

    async fn create_message(&self, user_id: i64, subject: String, message: String) -> Result<serde_json::Value, AppError> {
        let row = sqlx::query(
            r#"
            INSERT INTO messageboard (user_id, subject, message, status, created_at, updated_at)
            VALUES ($1, $2, $3, 'pending', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            RETURNING id, subject, message, status, created_at, updated_at
            "#
        )
        .bind(user_id)
        .bind(subject)
        .bind(message)
        .fetch_one(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to create contact message: {}", e);
            AppError::Database(e)
        })?;

        Ok(serde_json::json!({
            "id": row.get::<i64, _>("id"),
            "subject": row.get::<String, _>("subject"),
            "message": row.get::<String, _>("message"),
            "status": row.get::<String, _>("status"),
            "created_at": row.get::<chrono::DateTime<chrono::Utc>, _>("created_at"),
            "updated_at": row.get::<chrono::DateTime<chrono::Utc>, _>("updated_at")
        }))
    }

    async fn get_all_messages(&self) -> Result<Vec<serde_json::Value>, AppError> {
        // 首先確保表格存在
        self.ensure_table_exists().await?;
        
        let rows = sqlx::query(
            r#"
            SELECT 
                m.id,
                m.user_id,
                m.subject,
                m.message,
                m.status,
                m.admin_id,
                m.admin_reply,
                m.created_at,
                m.updated_at,
                u.username,
                u.email,
                u.pharmacy_name
            FROM messageboard m
            LEFT JOIN users u ON m.user_id = u.id
            ORDER BY m.created_at DESC
            "#
        )
        .fetch_all(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to fetch all contact messages: {}", e);
            AppError::Database(e)
        })?;

        let messages = rows.into_iter().map(|row| {
            let username = row.try_get::<Option<String>, _>("username").unwrap_or(None);
            let email = row.try_get::<Option<String>, _>("email").unwrap_or(None);
            let pharmacy_name = row.try_get::<Option<String>, _>("pharmacy_name").unwrap_or(None);
            let user_id = row.get::<i64, _>("user_id");
            
            // 決定顯示名稱的優先順序：藥局名稱 > 用戶名 > 電子郵件 > 用戶ID
            let display_name = pharmacy_name.as_ref()
                .or(username.as_ref())
                .or(email.as_ref())
                .cloned()
                .unwrap_or_else(|| format!("用戶 {}", user_id));
            
            serde_json::json!({
                "id": row.get::<i64, _>("id"),
                "user_id": user_id,
                "user_name": display_name,
                "user_email": email.unwrap_or_else(|| format!("user{}@example.com", user_id)),
                "pharmacy_name": pharmacy_name,
                "subject": row.get::<String, _>("subject"),
                "message": row.get::<String, _>("message"),
                "status": row.get::<String, _>("status"),
                "admin_id": row.try_get::<Option<i64>, _>("admin_id").unwrap_or(None),
                "admin_reply": row.try_get::<Option<String>, _>("admin_reply").unwrap_or(None),
                "created_at": row.get::<chrono::DateTime<chrono::Utc>, _>("created_at"),
                "updated_at": row.get::<chrono::DateTime<chrono::Utc>, _>("updated_at")
            })
        }).collect();

        Ok(messages)
    }

    async fn create_admin_reply(&self, message_id: i64, admin_id: i64, reply_message: String) -> Result<serde_json::Value, AppError> {
        let row = sqlx::query(
            r#"
            UPDATE messageboard 
            SET admin_id = $1, admin_reply = $2, status = 'replied', updated_at = CURRENT_TIMESTAMP
            WHERE id = $3
            RETURNING id, user_id, subject, message, status, admin_id, admin_reply, created_at, updated_at
            "#
        )
        .bind(admin_id)
        .bind(reply_message)
        .bind(message_id)
        .fetch_one(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to create admin reply: {}", e);
            AppError::Database(e)
        })?;

        Ok(serde_json::json!({
            "id": row.get::<i64, _>("id"),
            "user_id": row.get::<i64, _>("user_id"),
            "subject": row.get::<String, _>("subject"),
            "message": row.get::<String, _>("message"),
            "status": row.get::<String, _>("status"),
            "admin_id": row.get::<i64, _>("admin_id"),
            "admin_reply": row.get::<String, _>("admin_reply"),
            "created_at": row.get::<chrono::DateTime<chrono::Utc>, _>("created_at"),
            "updated_at": row.get::<chrono::DateTime<chrono::Utc>, _>("updated_at")
        }))
    }
}