use crate::models::chat::{ChatMessage, ChatStats, ChatConversation};
use crate::error::AppError;
use sqlx::PgPool;
use chrono::Utc;

pub struct ChatRepository {
    pool: PgPool,
}

impl ChatRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 儲存用戶聊天訊息
    pub async fn save_user_message(
        &self,
        user_id: &str,
        user_name: &str,
        message: &str,
    ) -> Result<ChatMessage, AppError> {
        let now = Utc::now();
        
        let result = sqlx::query!(
            "INSERT INTO chat_messages (user_id, user_name, message, message_type, replied, created_at, updated_at, status) VALUES ($1, $2, $3, 'user', FALSE, $4, $5, 'sent') RETURNING id",
            user_id,
            user_name,
            message,
            now,
            now
        )
        .fetch_one(&self.pool)
        .await?;

        let chat_message = ChatMessage {
            id: result.id,
            user_id: user_id.to_string(),
            user_name: user_name.to_string(),
            admin_id: None,
            admin_name: None,
            message: message.to_string(),
            message_type: "user".to_string(),
            replied: false,
            created_at: now,
            updated_at: now,
            status: "sent".to_string(),
        };

        Ok(chat_message)
    }

    /// 儲存管理員回覆
    pub async fn save_admin_reply(
        &self,
        user_id: &str,
        admin_id: &str,
        admin_name: &str,
        message: &str,
    ) -> Result<ChatMessage, AppError> {
        let now = Utc::now();
        
        // 開始事務
        let mut tx = self.pool.begin().await?;

        // 插入管理員回覆
        let result = sqlx::query!(
            "INSERT INTO chat_messages (user_id, admin_id, admin_name, message, message_type, replied, created_at, updated_at, status) VALUES ($1, $2, $3, $4, 'admin', FALSE, $5, $6, 'sent') RETURNING id",
            user_id,
            admin_id,
            admin_name,
            message,
            now,
            now
        )
        .fetch_one(&mut *tx)
        .await?;

        // 標記用戶訊息為已回覆
        sqlx::query!(
            "UPDATE chat_messages SET replied = TRUE, updated_at = $1 WHERE user_id = $2 AND message_type = 'user' AND replied = FALSE",
            now,
            user_id
        )
        .execute(&mut *tx)
        .await?;

        // 提交事務
        tx.commit().await?;

        let chat_message = ChatMessage {
            id: result.id,
            user_id: user_id.to_string(),
            user_name: "".to_string(), // 會在查詢時填入
            admin_id: Some(admin_id.to_string()),
            admin_name: Some(admin_name.to_string()),
            message: message.to_string(),
            message_type: "admin".to_string(),
            replied: false,
            created_at: now,
            updated_at: now,
            status: "sent".to_string(),
        };

        Ok(chat_message)
    }

    /// 獲取用戶的聊天記錄
    pub async fn get_user_messages(&self, user_id: &str) -> Result<Vec<ChatMessage>, AppError> {
        let messages = sqlx::query_as!(
            ChatMessage,
            "SELECT id, user_id, user_name, admin_id, admin_name, message, message_type, replied, created_at, updated_at, status FROM chat_messages WHERE user_id = $1 ORDER BY created_at ASC",
            user_id
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(messages)
    }

    /// 獲取所有聊天記錄（管理員用）
    pub async fn get_all_messages(&self) -> Result<Vec<ChatMessage>, AppError> {
        let messages = sqlx::query_as!(
            ChatMessage,
            "SELECT id, user_id, user_name, admin_id, admin_name, message, message_type, replied, created_at, updated_at, status FROM chat_messages ORDER BY created_at DESC"
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(messages)
    }

    /// 獲取聊天統計信息
    pub async fn get_chat_stats(&self) -> Result<ChatStats, AppError> {
        let stats = sqlx::query!(
            r#"
            SELECT 
                COUNT(DISTINCT user_id) as total_conversations,
                COUNT(CASE WHEN message_type = 'user' AND replied = FALSE THEN 1 END) as unread_messages,
                COUNT(CASE WHEN message_type = 'user' AND replied = FALSE THEN 1 END) as pending_replies
            FROM chat_messages
            "#
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(ChatStats {
            total_conversations: stats.total_conversations.unwrap_or(0),
            unread_messages: stats.unread_messages.unwrap_or(0),
            pending_replies: stats.pending_replies.unwrap_or(0),
        })
    }

    /// 獲取聊天對話列表
    pub async fn get_chat_conversations(&self) -> Result<Vec<ChatConversation>, AppError> {
        let conversations = sqlx::query!(
            r#"
            SELECT 
                user_id,
                user_name,
                message as last_message,
                created_at as last_message_time,
                COUNT(CASE WHEN message_type = 'user' AND replied = FALSE THEN 1 END) as unread_count,
                CASE 
                    WHEN COUNT(CASE WHEN message_type = 'user' AND replied = FALSE THEN 1 END) > 0 THEN 'unread'
                    ELSE 'read'
                END as status
            FROM (
                SELECT DISTINCT ON (user_id) 
                    user_id, user_name, message, message_type, replied, created_at
                FROM chat_messages 
                ORDER BY user_id, created_at DESC
            ) latest_messages
            GROUP BY user_id, user_name, message, created_at
            ORDER BY created_at DESC
            "#
        )
        .fetch_all(&self.pool)
        .await?;

        let result = conversations
            .into_iter()
            .map(|row| ChatConversation {
                user_id: row.user_id,
                user_name: row.user_name,
                last_message: row.last_message,
                last_message_time: row.last_message_time,
                unread_count: row.unread_count.unwrap_or(0),
                status: row.status.unwrap_or("read".to_string()),
            })
            .collect();

        Ok(result)
    }

    /// 標記訊息狀態
    pub async fn update_message_status(
        &self,
        message_id: i64,
        status: &str,
    ) -> Result<(), AppError> {
        let now = Utc::now();
        
        sqlx::query!(
            "UPDATE chat_messages SET status = $1, updated_at = $2 WHERE id = $3",
            status,
            now,
            message_id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// 標記用戶所有訊息為已解決
    pub async fn mark_user_messages_resolved(&self, user_id: &str) -> Result<(), AppError> {
        let now = Utc::now();
        
        sqlx::query!(
            "UPDATE chat_messages SET status = 'resolved', replied = TRUE, updated_at = $1 WHERE user_id = $2 AND status != 'resolved'",
            now,
            user_id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }
}