use crate::{
    models::{Cart, CartItem, CartDetails, CartItemDetails},
    database::DbPool,
    error::AppError,
};
use async_trait::async_trait;
use sqlx::Row;
use rust_decimal::Decimal;


#[async_trait]
pub trait CartRepository: Send + Sync {
    async fn create_cart(&self, user_id: i64) -> Result<Cart, AppError>;
    async fn find_cart_by_user_id(&self, user_id: i64) -> Result<Option<Cart>, AppError>;
    async fn get_or_create_cart(&self, user_id: i64) -> Result<Cart, AppError>;
    async fn add_item(&self, cart_id: i64, product_id: i64, quantity: i32, unit_price: Decimal) -> Result<CartItem, AppError>;
    async fn update_item_quantity(&self, cart_item_id: i64, quantity: i32) -> Result<CartItem, AppError>;
    async fn remove_item(&self, cart_item_id: i64) -> Result<bool, AppError>;
    async fn find_cart_item(&self, cart_id: i64, product_id: i64) -> Result<Option<CartItem>, AppError>;
    async fn get_cart_details(&self, user_id: i64) -> Result<Option<CartDetails>, AppError>;
    async fn clear_cart(&self, cart_id: i64) -> Result<bool, AppError>;
    async fn get_cart_items(&self, cart_id: i64) -> Result<Vec<CartItem>, AppError>;
    async fn update_cart_timestamp(&self, cart_id: i64) -> Result<(), AppError>;
}

pub struct PostgresCartRepository {
    pool: DbPool,
}

impl PostgresCartRepository {
    pub fn new(pool: DbPool) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl CartRepository for PostgresCartRepository {
    async fn create_cart(&self, user_id: i64) -> Result<Cart, AppError> {
        let id: i64 = sqlx::query_scalar(
            r#"
            INSERT INTO carts (user_id, created_at, updated_at)
            VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            RETURNING id
            "#
        )
        .bind(user_id)
        .fetch_one(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to create cart: {}", e);
            AppError::Database(e)
        })?;

        self.find_cart_by_id(id).await?.ok_or_else(|| {
            AppError::NotFound("Cart not found after creation".to_string())
        })
    }

    async fn find_cart_by_user_id(&self, user_id: i64) -> Result<Option<Cart>, AppError> {
        let row = sqlx::query(
            r#"
            SELECT id, user_id, created_at, updated_at
            FROM carts WHERE user_id = $1
            "#
        )
        .bind(user_id)
        .fetch_optional(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to find cart by user_id: {}", e);
            AppError::Database(e)
        })?;

        Ok(row.map(|row| Cart {
            id: row.get("id"),
            user_id: row.get("user_id"),
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
        }))
    }

    async fn get_or_create_cart(&self, user_id: i64) -> Result<Cart, AppError> {
        if let Some(cart) = self.find_cart_by_user_id(user_id).await? {
            Ok(cart)
        } else {
            self.create_cart(user_id).await
        }
    }

    async fn add_item(&self, cart_id: i64, product_id: i64, quantity: i32, unit_price: Decimal) -> Result<CartItem, AppError> {
        let subtotal = (unit_price * Decimal::from(quantity)).round_dp(0);
        
        // 檢查是否已存在相同產品的項目
        if let Some(existing_item) = self.find_cart_item(cart_id, product_id).await? {
            // 如果存在，更新數量
            let new_quantity = existing_item.quantity + quantity;
            return self.update_item_quantity(existing_item.id, new_quantity).await;
        }

        let id: i64 = sqlx::query_scalar(
            r#"
            INSERT INTO cart_items (cart_id, product_id, quantity, unit_price, subtotal, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            RETURNING id
            "#
        )
        .bind(cart_id)
        .bind(product_id)
        .bind(quantity)
        .bind(unit_price)
        .bind(subtotal)
        .fetch_one(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to add cart item: {}", e);
            AppError::Database(e)
        })?;

        // 更新購物車時間戳
        self.update_cart_timestamp(cart_id).await?;

        self.find_cart_item_by_id(id).await?.ok_or_else(|| {
            AppError::NotFound("Cart item not found after creation".to_string())
        })
    }

    async fn update_item_quantity(&self, cart_item_id: i64, quantity: i32) -> Result<CartItem, AppError> {
        // 先獲取現有項目以計算新的小計
        let existing_item = self.find_cart_item_by_id(cart_item_id).await?
            .ok_or_else(|| AppError::NotFound(format!("Cart item with id {} not found", cart_item_id)))?;

        let subtotal = (existing_item.unit_price * Decimal::from(quantity)).round_dp(0);

        sqlx::query(
            r#"
            UPDATE cart_items 
            SET quantity = $1, subtotal = $2, updated_at = CURRENT_TIMESTAMP
            WHERE id = $3
            "#
        )
        .bind(quantity)
        .bind(subtotal)
        .bind(cart_item_id)
        .execute(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to update cart item quantity: {}", e);
            AppError::Database(e)
        })?;

        // 更新購物車時間戳
        self.update_cart_timestamp(existing_item.cart_id).await?;

        self.find_cart_item_by_id(cart_item_id).await?.ok_or_else(|| {
            AppError::NotFound("Cart item not found after update".to_string())
        })
    }

    async fn remove_item(&self, cart_item_id: i64) -> Result<bool, AppError> {
        // 先獲取項目以獲得 cart_id
        let existing_item = self.find_cart_item_by_id(cart_item_id).await?;
        
        let result = sqlx::query("DELETE FROM cart_items WHERE id = $1")
            .bind(cart_item_id)
            .execute(&*self.pool)
            .await
            .map_err(|e| {
                tracing::error!("Failed to remove cart item: {}", e);
                AppError::Database(e)
            })?;

        let removed = result.rows_affected() > 0;
        
        // 如果成功刪除且有 cart_id，更新購物車時間戳
        if removed {
            if let Some(item) = existing_item {
                self.update_cart_timestamp(item.cart_id).await?;
            }
        }

        Ok(removed)
    }

    async fn find_cart_item(&self, cart_id: i64, product_id: i64) -> Result<Option<CartItem>, AppError> {
        let row = sqlx::query(
            r#"
            SELECT id, cart_id, product_id, quantity, unit_price, subtotal, created_at, updated_at
            FROM cart_items WHERE cart_id = $1 AND product_id = $2
            "#
        )
        .bind(cart_id)
        .bind(product_id)
        .fetch_optional(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to find cart item: {}", e);
            AppError::Database(e)
        })?;

        Ok(row.map(|row| {
            CartItem {
                id: row.get("id"),
                cart_id: row.get("cart_id"),
                product_id: row.get("product_id"),
                quantity: row.get("quantity"),
                unit_price: row.get("unit_price"),
                subtotal: row.get("subtotal"),
                created_at: row.get("created_at"),
                updated_at: row.get("updated_at"),
            }
        }))
    }

    async fn get_cart_details(&self, user_id: i64) -> Result<Option<CartDetails>, AppError> {
        let cart = match self.find_cart_by_user_id(user_id).await? {
            Some(cart) => cart,
            None => return Ok(None),
        };

        let rows = sqlx::query(
            r#"
            SELECT 
                ci.id, ci.cart_id, ci.product_id, ci.quantity, ci.unit_price, ci.subtotal, 
                ci.created_at, ci.updated_at,
                p.name as product_name, p.nhi_code as product_nhi_code, p.stock_quantity as available_stock
            FROM cart_items ci
            JOIN products p ON ci.product_id = p.id
            WHERE ci.cart_id = $1
            ORDER BY ci.created_at ASC
            "#
        )
        .bind(cart.id)
        .fetch_all(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to get cart details: {}", e);
            AppError::Database(e)
        })?;

        let items: Vec<CartItemDetails> = rows
            .into_iter()
            .map(|row| {
                CartItemDetails {
                    item: CartItem {
                        id: row.get("id"),
                        cart_id: row.get("cart_id"),
                        product_id: row.get("product_id"),
                        quantity: row.get("quantity"),
                        unit_price: row.get("unit_price"),
                        subtotal: row.get("subtotal"),
                        created_at: row.get("created_at"),
                        updated_at: row.get("updated_at"),
                    },
                    product_name: row.get("product_name"),
                    product_nhi_code: row.get("product_nhi_code"),
                    available_stock: row.get("available_stock"),
                }
            })
            .collect();

        let mut cart_details = CartDetails {
            cart,
            items,
            total_amount: Decimal::ZERO,
            total_items: 0,
        };

        cart_details.calculate_totals();
        Ok(Some(cart_details))
    }

    async fn clear_cart(&self, cart_id: i64) -> Result<bool, AppError> {
        let result = sqlx::query("DELETE FROM cart_items WHERE cart_id = $1")
            .bind(cart_id)
            .execute(&*self.pool)
            .await
            .map_err(|e| {
                tracing::error!("Failed to clear cart: {}", e);
                AppError::Database(e)
            })?;

        // 更新購物車時間戳
        self.update_cart_timestamp(cart_id).await?;

        Ok(result.rows_affected() > 0)
    }

    async fn get_cart_items(&self, cart_id: i64) -> Result<Vec<CartItem>, AppError> {
        let rows = sqlx::query(
            r#"
            SELECT id, cart_id, product_id, quantity, unit_price, subtotal, created_at, updated_at
            FROM cart_items WHERE cart_id = $1
            ORDER BY created_at ASC
            "#
        )
        .bind(cart_id)
        .fetch_all(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to get cart items: {}", e);
            AppError::Database(e)
        })?;

        Ok(rows
            .into_iter()
            .map(|row| {
                CartItem {
                    id: row.get("id"),
                    cart_id: row.get("cart_id"),
                    product_id: row.get("product_id"),
                    quantity: row.get("quantity"),
                    unit_price: row.get("unit_price"),
                    subtotal: row.get("subtotal"),
                    created_at: row.get("created_at"),
                    updated_at: row.get("updated_at"),
                }
            })
            .collect())
    }

    async fn update_cart_timestamp(&self, cart_id: i64) -> Result<(), AppError> {
        sqlx::query("UPDATE carts SET updated_at = CURRENT_TIMESTAMP WHERE id = $1")
            .bind(cart_id)
            .execute(&*self.pool)
            .await
            .map_err(|e| {
                tracing::error!("Failed to update cart timestamp: {}", e);
                AppError::Database(e)
            })?;

        Ok(())
    }
}

impl PostgresCartRepository {
    async fn find_cart_by_id(&self, id: i64) -> Result<Option<Cart>, AppError> {
        let row = sqlx::query(
            r#"
            SELECT id, user_id, created_at, updated_at
            FROM carts WHERE id = $1
            "#
        )
        .bind(id)
        .fetch_optional(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to find cart by id: {}", e);
            AppError::Database(e)
        })?;

        Ok(row.map(|row| Cart {
            id: row.get("id"),
            user_id: row.get("user_id"),
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
        }))
    }

    async fn find_cart_item_by_id(&self, id: i64) -> Result<Option<CartItem>, AppError> {
        let row = sqlx::query(
            r#"
            SELECT id, cart_id, product_id, quantity, unit_price, subtotal, created_at, updated_at
            FROM cart_items WHERE id = $1
            "#
        )
        .bind(id)
        .fetch_optional(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to find cart item by id: {}", e);
            AppError::Database(e)
        })?;

        Ok(row.map(|row| {
            CartItem {
                id: row.get("id"),
                cart_id: row.get("cart_id"),
                product_id: row.get("product_id"),
                quantity: row.get("quantity"),
                unit_price: row.get("unit_price"),
                subtotal: row.get("subtotal"),
                created_at: row.get("created_at"),
                updated_at: row.get("updated_at"),
            }
        }))
    }
}