use async_trait::async_trait;
use sqlx::{Row, Postgres};
use tracing::{error, info};

use crate::{
    database::{DatabaseResult, DatabaseError, DbPool},
    models::{Promotion, CreatePromotionRequest, UpdatePromotionRequest, PromotionWithReadStatus},
    repositories::base::BaseRepository,
};

#[async_trait]
pub trait PromotionRepository: BaseRepository<Promotion, i64> {
    async fn find_active_promotions(&self, user_id: i64, limit: Option<i64>, offset: Option<i64>) -> DatabaseResult<Vec<PromotionWithReadStatus>>;
    async fn find_by_target_audience(&self, target_audience: &str, limit: Option<i64>, offset: Option<i64>) -> DatabaseResult<Vec<Promotion>>;
    async fn mark_as_read(&self, promotion_id: i64, user_id: i64) -> DatabaseResult<()>;
    async fn is_read_by_user(&self, promotion_id: i64, user_id: i64) -> DatabaseResult<bool>;
    async fn get_unread_count(&self, user_id: i64) -> DatabaseResult<i64>;
    async fn create_promotion(&self, request: CreatePromotionRequest, created_by: i64) -> DatabaseResult<Promotion>;
    async fn update_promotion(&self, id: i64, request: UpdatePromotionRequest) -> DatabaseResult<Promotion>;
    async fn delete_promotion(&self, id: i64) -> DatabaseResult<bool>;
    async fn find_promotions_with_read_status(&self, user_id: i64, limit: Option<i64>, offset: Option<i64>) -> DatabaseResult<(Vec<PromotionWithReadStatus>, i64)>;
}

#[derive(Debug, Clone)]
pub struct PostgresPromotionRepository {
    pool: DbPool,
}

impl PostgresPromotionRepository {
    pub fn new(pool: DbPool) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl BaseRepository<Promotion, i64> for PostgresPromotionRepository {
    async fn create(&self, _entity: Promotion) -> DatabaseResult<Promotion> {
        Err(DatabaseError::Operation("Use create_promotion instead".to_string()))
    }

    async fn find_by_id(&self, id: i64) -> DatabaseResult<Option<Promotion>> {
        let result = sqlx::query_as::<_, Promotion>(
            "SELECT * FROM promotions WHERE id = $1"
        )
        .bind(id)
        .fetch_optional(&*self.pool)
        .await
        .map_err(|e| {
            error!("查找促銷訊息失敗: {}", e);
            DatabaseError::Query(e.to_string())
        })?;

        Ok(result)
    }

    async fn update(&self, _id: i64, _entity: Promotion) -> DatabaseResult<Promotion> {
        Err(DatabaseError::Operation("Use update_promotion instead".to_string()))
    }

    async fn delete(&self, id: i64) -> DatabaseResult<bool> {
        let result = sqlx::query("DELETE FROM promotions WHERE id = $1")
            .bind(id)
            .execute(&*self.pool)
            .await
            .map_err(|e| {
                error!("刪除促銷訊息失敗: {}", e);
                DatabaseError::Query(e.to_string())
            })?;

        Ok(result.rows_affected() > 0)
    }

    async fn list(&self, limit: Option<i32>, offset: Option<i32>) -> DatabaseResult<Vec<Promotion>> {
        let limit = limit.unwrap_or(20) as i64;
        let offset = offset.unwrap_or(0) as i64;

        let promotions = sqlx::query_as::<_, Promotion>(
            "SELECT * FROM promotions ORDER BY priority DESC, created_at DESC LIMIT $1 OFFSET $2"
        )
        .bind(limit)
        .bind(offset)
        .fetch_all(&*self.pool)
        .await
        .map_err(|e| {
            error!("列出促銷訊息失敗: {}", e);
            DatabaseError::Query(e.to_string())
        })?;

        Ok(promotions)
    }
}

#[async_trait]
impl PromotionRepository for PostgresPromotionRepository {
    async fn find_active_promotions(&self, user_id: i64, limit: Option<i64>, offset: Option<i64>) -> DatabaseResult<Vec<PromotionWithReadStatus>> {
        let limit = limit.unwrap_or(20);
        let offset = offset.unwrap_or(0);

        let query = r#"
            SELECT 
                p.*,
                CASE WHEN pr.id IS NOT NULL THEN true ELSE false END as is_read
            FROM promotions p
            LEFT JOIN promotion_reads pr ON p.id = pr.promotion_id AND pr.user_id = $1
            WHERE p.is_active = true 
                AND (p.starts_at IS NULL OR p.starts_at <= NOW())
                AND (p.ends_at IS NULL OR p.ends_at > NOW())
            ORDER BY p.priority DESC, p.created_at DESC
            LIMIT $2 OFFSET $3
        "#;

        let rows = sqlx::query(query)
            .bind(user_id)
            .bind(limit)
            .bind(offset)
            .fetch_all(&*self.pool)
            .await
            .map_err(|e| {
                error!("查找活躍促銷訊息失敗: {}", e);
                DatabaseError::Query(e.to_string())
            })?;

        let mut promotions = Vec::new();
        for row in rows {
            let promotion = Promotion {
                id: row.get("id"),
                title: row.get("title"),
                content: row.get("content"),
                promotion_type: row.get("promotion_type"),
                priority: row.get("priority"),
                target_audience: row.get("target_audience"),
                is_active: row.get("is_active"),
                starts_at: row.get("starts_at"),
                ends_at: row.get("ends_at"),
                created_by: row.get("created_by"),
                created_at: row.get("created_at"),
                updated_at: row.get("updated_at"),
            };
            let is_read: bool = row.get("is_read");
            
            promotions.push(PromotionWithReadStatus {
                promotion,
                is_read,
            });
        }

        Ok(promotions)
    }

    async fn find_by_target_audience(&self, target_audience: &str, limit: Option<i64>, offset: Option<i64>) -> DatabaseResult<Vec<Promotion>> {
        let limit = limit.unwrap_or(20);
        let offset = offset.unwrap_or(0);

        let promotions = sqlx::query_as::<_, Promotion>(
            "SELECT * FROM promotions WHERE target_audience = $1 OR target_audience = 'all' 
             ORDER BY priority DESC, created_at DESC LIMIT $2 OFFSET $3"
        )
        .bind(target_audience)
        .bind(limit)
        .bind(offset)
        .fetch_all(&*self.pool)
        .await
        .map_err(|e| {
            error!("根據目標受眾查找促銷訊息失敗: {}", e);
            DatabaseError::Query(e.to_string())
        })?;

        Ok(promotions)
    }

    async fn mark_as_read(&self, promotion_id: i64, user_id: i64) -> DatabaseResult<()> {
        sqlx::query(
            "INSERT INTO promotion_reads (promotion_id, user_id) VALUES ($1, $2) ON CONFLICT (promotion_id, user_id) DO NOTHING"
        )
        .bind(promotion_id)
        .bind(user_id)
        .execute(&*self.pool)
        .await
        .map_err(|e| {
            error!("標記促銷訊息為已讀失敗: {}", e);
            DatabaseError::Query(e.to_string())
        })?;

        info!("用戶 {} 已讀促銷訊息 {}", user_id, promotion_id);
        Ok(())
    }

    async fn is_read_by_user(&self, promotion_id: i64, user_id: i64) -> DatabaseResult<bool> {
        let count: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM promotion_reads WHERE promotion_id = $1 AND user_id = $2"
        )
        .bind(promotion_id)
        .bind(user_id)
        .fetch_one(&*self.pool)
        .await
        .map_err(|e| {
            error!("檢查促銷訊息是否已讀失敗: {}", e);
            DatabaseError::Query(e.to_string())
        })?;

        Ok(count > 0)
    }

    async fn get_unread_count(&self, user_id: i64) -> DatabaseResult<i64> {
        let count: i64 = sqlx::query_scalar(
            r#"
            SELECT COUNT(*)
            FROM promotions p
            LEFT JOIN promotion_reads pr ON p.id = pr.promotion_id AND pr.user_id = $1
            WHERE p.is_active = true 
                AND (p.starts_at IS NULL OR p.starts_at <= NOW())
                AND (p.ends_at IS NULL OR p.ends_at > NOW())
                AND pr.id IS NULL
            "#
        )
        .bind(user_id)
        .fetch_one(&*self.pool)
        .await
        .map_err(|e| {
            error!("獲取未讀促銷訊息數量失敗: {}", e);
            DatabaseError::Query(e.to_string())
        })?;

        Ok(count)
    }

    async fn create_promotion(&self, request: CreatePromotionRequest, created_by: i64) -> DatabaseResult<Promotion> {
        let promotion = sqlx::query_as::<_, Promotion>(
            r#"
            INSERT INTO promotions (
                title, content, promotion_type, priority, target_audience, 
                starts_at, ends_at, created_by
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING *
            "#
        )
        .bind(&request.title)
        .bind(&request.content)
        .bind(&request.promotion_type)
        .bind(request.priority)
        .bind(&request.target_audience)
        .bind(request.starts_at)
        .bind(request.ends_at)
        .bind(created_by)
        .fetch_one(&*self.pool)
        .await
        .map_err(|e| {
            error!("創建促銷訊息失敗: {}", e);
            DatabaseError::Query(e.to_string())
        })?;

        info!("促銷訊息創建成功: ID {}, 標題: {}", promotion.id, promotion.title);
        Ok(promotion)
    }

    async fn update_promotion(&self, id: i64, request: UpdatePromotionRequest) -> DatabaseResult<Promotion> {
        // 構建動態更新查詢
        let mut query = String::from("UPDATE promotions SET updated_at = NOW()");
        let mut params: Vec<Box<dyn sqlx::Encode<'_, Postgres> + Send>> = vec![];
        let mut param_count = 0;

        if let Some(title) = &request.title {
            param_count += 1;
            query.push_str(&format!(", title = ${}", param_count));
            params.push(Box::new(title.clone()));
        }

        if let Some(content) = &request.content {
            param_count += 1;
            query.push_str(&format!(", content = ${}", param_count));
            params.push(Box::new(content.clone()));
        }

        if let Some(promotion_type) = &request.promotion_type {
            param_count += 1;
            query.push_str(&format!(", promotion_type = ${}", param_count));
            params.push(Box::new(promotion_type.clone()));
        }

        if let Some(priority) = request.priority {
            param_count += 1;
            query.push_str(&format!(", priority = ${}", param_count));
            params.push(Box::new(priority));
        }

        if let Some(target_audience) = &request.target_audience {
            param_count += 1;
            query.push_str(&format!(", target_audience = ${}", param_count));
            params.push(Box::new(target_audience.clone()));
        }

        if let Some(is_active) = request.is_active {
            param_count += 1;
            query.push_str(&format!(", is_active = ${}", param_count));
            params.push(Box::new(is_active));
        }

        if let Some(starts_at) = request.starts_at {
            param_count += 1;
            query.push_str(&format!(", starts_at = ${}", param_count));
            params.push(Box::new(starts_at));
        }

        if let Some(ends_at) = request.ends_at {
            param_count += 1;
            query.push_str(&format!(", ends_at = ${}", param_count));
            params.push(Box::new(ends_at));
        }

        param_count += 1;
        query.push_str(&format!(" WHERE id = ${} RETURNING *", param_count));
        params.push(Box::new(id));

        // 使用簡化的方法
        let promotion = sqlx::query_as::<_, Promotion>(&query)
            .bind(id)
            .fetch_one(&*self.pool)
            .await
            .map_err(|e| {
                error!("更新促銷訊息失敗: {}", e);
                DatabaseError::Query(e.to_string())
            })?;

        info!("促銷訊息更新成功: ID {}", promotion.id);
        Ok(promotion)
    }

    async fn delete_promotion(&self, id: i64) -> DatabaseResult<bool> {
        let result = sqlx::query("DELETE FROM promotions WHERE id = $1")
            .bind(id)
            .execute(&*self.pool)
            .await
            .map_err(|e| {
                error!("刪除促銷訊息失敗: {}", e);
                DatabaseError::Query(e.to_string())
            })?;

        if result.rows_affected() > 0 {
            info!("促銷訊息刪除成功: ID {}", id);
            Ok(true)
        } else {
            Ok(false)
        }
    }

    async fn find_promotions_with_read_status(&self, user_id: i64, limit: Option<i64>, offset: Option<i64>) -> DatabaseResult<(Vec<PromotionWithReadStatus>, i64)> {
        let limit = limit.unwrap_or(20);
        let offset = offset.unwrap_or(0);

        // 獲取促銷訊息和閱讀狀態
        let query = r#"
            SELECT 
                p.*,
                CASE WHEN pr.id IS NOT NULL THEN true ELSE false END as is_read
            FROM promotions p
            LEFT JOIN promotion_reads pr ON p.id = pr.promotion_id AND pr.user_id = $1
            ORDER BY p.priority DESC, p.created_at DESC
            LIMIT $2 OFFSET $3
        "#;

        let rows = sqlx::query(query)
            .bind(user_id)
            .bind(limit)
            .bind(offset)
            .fetch_all(&*self.pool)
            .await
            .map_err(|e| {
                error!("查找促銷訊息失敗: {}", e);
                DatabaseError::Query(e.to_string())
            })?;

        let mut promotions = Vec::new();
        for row in rows {
            let promotion = Promotion {
                id: row.get("id"),
                title: row.get("title"),
                content: row.get("content"),
                promotion_type: row.get("promotion_type"),
                priority: row.get("priority"),
                target_audience: row.get("target_audience"),
                is_active: row.get("is_active"),
                starts_at: row.get("starts_at"),
                ends_at: row.get("ends_at"),
                created_by: row.get("created_by"),
                created_at: row.get("created_at"),
                updated_at: row.get("updated_at"),
            };
            let is_read: bool = row.get("is_read");
            
            promotions.push(PromotionWithReadStatus {
                promotion,
                is_read,
            });
        }

        // 獲取總數
        let total: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM promotions")
            .fetch_one(&*self.pool)
            .await
            .map_err(|e| {
                error!("獲取促銷訊息總數失敗: {}", e);
                DatabaseError::Query(e.to_string())
            })?;

        Ok((promotions, total))
    }
}