use crate::database::DbPool;
use crate::models::Favorite;
use sqlx::Row;

pub struct FavoriteRepository {
    pool: DbPool,
}

impl FavoriteRepository {
    pub fn new(pool: DbPool) -> Self {
        Self { pool }
    }

    /// 添加產品到我的最愛
    pub async fn add_favorite(&self, user_id: i64, product_id: i64) -> Result<Favorite, sqlx::Error> {
        let favorite = sqlx::query_as::<_, Favorite>(
            r#"
            INSERT INTO favorites (user_id, product_id, created_at)
            VALUES ($1, $2, NOW())
            RETURNING id, user_id, product_id, created_at
            "#
        )
        .bind(user_id)
        .bind(product_id)
        .fetch_one(&*self.pool)
        .await?;

        Ok(favorite)
    }

    /// 從我的最愛移除產品
    pub async fn remove_favorite(&self, user_id: i64, product_id: i64) -> Result<bool, sqlx::Error> {
        let result = sqlx::query(
            "DELETE FROM favorites WHERE user_id = $1 AND product_id = $2"
        )
        .bind(user_id)
        .bind(product_id)
        .execute(&*self.pool)
        .await?;

        Ok(result.rows_affected() > 0)
    }

    /// 檢查產品是否在我的最愛中
    pub async fn is_favorite(&self, user_id: i64, product_id: i64) -> Result<bool, sqlx::Error> {
        let count: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM favorites WHERE user_id = $1 AND product_id = $2"
        )
        .bind(user_id)
        .bind(product_id)
        .fetch_one(&*self.pool)
        .await?;

        Ok(count > 0)
    }

    /// 獲取用戶的所有最愛產品（簡化版）
    pub async fn get_user_favorites(&self, user_id: i64) -> Result<Vec<serde_json::Value>, sqlx::Error> {
        let favorites = sqlx::query(
            r#"
            SELECT 
                p.id,
                p.name,
                p.ingredients,
                p.dosage_form,
                p.unit as strength,
                p.selling_price as price,
                p.nhi_code as insurance_code,
                p.manufacturer,
                f.created_at as favorited_at
            FROM favorites f
            JOIN products p ON f.product_id = p.id
            WHERE f.user_id = $1
            ORDER BY f.created_at DESC
            "#
        )
        .bind(user_id)
        .fetch_all(&*self.pool)
        .await?;

        let result: Vec<serde_json::Value> = favorites
            .into_iter()
            .map(|row| {
                serde_json::json!({
                    "id": row.get::<i64, _>("id"),
                    "name": row.get::<String, _>("name"),
                    "ingredients": row.get::<Option<String>, _>("ingredients"),
                    "dosage_form": row.get::<Option<String>, _>("dosage_form"),
                    "strength": row.get::<Option<String>, _>("strength"),
                    "price": row.get::<Option<rust_decimal::Decimal>, _>("price"),
                    "insurance_code": row.get::<Option<String>, _>("insurance_code"),
                    "manufacturer": row.get::<Option<String>, _>("manufacturer"),
                    "favorited_at": row.get::<chrono::DateTime<chrono::Utc>, _>("favorited_at"),
                })
            })
            .collect();

        Ok(result)
    }

    /// 清空用戶的所有最愛
    pub async fn clear_user_favorites(&self, user_id: i64) -> Result<i64, sqlx::Error> {
        let result = sqlx::query(
            "DELETE FROM favorites WHERE user_id = $1"
        )
        .bind(user_id)
        .execute(&*self.pool)
        .await?;

        Ok(result.rows_affected() as i64)
    }

    /// 獲取最愛統計
    pub async fn get_favorite_stats(&self, user_id: i64) -> Result<i64, sqlx::Error> {
        let count: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM favorites WHERE user_id = $1"
        )
        .bind(user_id)
        .fetch_one(&*self.pool)
        .await?;

        Ok(count)
    }

    /// 獲取最受歡迎的產品
    pub async fn get_popular_products(&self, limit: i64) -> Result<Vec<(i64, String, i64)>, sqlx::Error> {
        let products = sqlx::query(
            r#"
            SELECT 
                p.id,
                p.name,
                COUNT(f.id)::bigint as favorite_count
            FROM products p
            LEFT JOIN favorites f ON p.id = f.product_id
            GROUP BY p.id, p.name
            HAVING COUNT(f.id) > 0
            ORDER BY favorite_count DESC
            LIMIT $1
            "#
        )
        .bind(limit)
        .fetch_all(&*self.pool)
        .await?;

        let result = products
            .into_iter()
            .map(|row| {
                let id: i64 = row.get("id");
                let name: String = row.get("name");
                let count: i64 = row.get("favorite_count");
                (id, name, count)
            })
            .collect();

        Ok(result)
    }
}