use sqlx::PgPool;
use std::env;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 載入環境變數
    dotenvy::dotenv().ok();
    
    let database_url = env::var("DATABASE_URL")
        .expect("DATABASE_URL must be set");
    
    let pool = PgPool::connect(&database_url).await?;
    
    let sql = r#"
        DO $$
        BEGIN
            -- 添加 target_audience 欄位
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                           WHERE table_name = 'messages' AND column_name = 'target_audience') THEN
                ALTER TABLE messages ADD COLUMN target_audience VARCHAR(50) DEFAULT 'all';
            END IF;
            
            -- 添加 priority 欄位
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                           WHERE table_name = 'messages' AND column_name = 'priority') THEN
                ALTER TABLE messages ADD COLUMN priority INTEGER DEFAULT 1;
            END IF;
            
            -- 添加 starts_at 欄位
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                           WHERE table_name = 'messages' AND column_name = 'starts_at') THEN
                ALTER TABLE messages ADD COLUMN starts_at TIMESTAMP WITH TIME ZONE;
            END IF;
            
            -- 添加 ends_at 欄位
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                           WHERE table_name = 'messages' AND column_name = 'ends_at') THEN
                ALTER TABLE messages ADD COLUMN ends_at TIMESTAMP WITH TIME ZONE;
            END IF;
        END $$;
    "#;
    
    sqlx::query(sql).execute(&pool).await?;
    
    println!("Successfully added new fields to messages table");
    
    Ok(())
}
