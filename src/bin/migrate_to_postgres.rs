use std::env;
use sqlx::postgres::PgPoolOptions;
use sqlx::{Pool, Postgres};
use dotenvy::dotenv;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenv().ok();
    
    let database_url = env::var("DATABASE_URL")
        .unwrap_or_else(|_| "postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require".to_string());
    
    println!("正在連接到 PostgreSQL 資料庫...");
    
    let pool: Pool<Postgres> = PgPoolOptions::new()
        .max_connections(5)
        .connect(&database_url)
        .await?;
    
    println!("✅ 成功連接到資料庫");
    
    // 執行遷移
    println!("開始執行資料庫遷移...");
    
    // 創建favorites表
    create_favorites_table(&pool).await?;
    
    println!("✅ 資料庫遷移完成！");
    
    // 檢查表格是否建立成功
    let tables: Vec<String> = sqlx::query_scalar(
        "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'"
    )
    .fetch_all(&pool)
    .await?;
    
    println!("資料庫中的表格:");
    for table in tables {
        println!("  📋 {}", table);
    }
    
    Ok(())
}
async fn
 create_favorites_table(pool: &Pool<Postgres>) -> Result<(), sqlx::Error> {
    println!("🔄 創建 favorites 表...");
    
    // 檢查表是否已存在
    let table_exists: bool = sqlx::query_scalar(
        "SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'favorites'
        )"
    )
    .fetch_one(pool)
    .await?;
    
    if table_exists {
        println!("  ℹ️  favorites 表已存在，跳過創建");
        return Ok(());
    }
    
    // 創建favorites表
    sqlx::query(
        r#"
        CREATE TABLE favorites (
            id BIGSERIAL PRIMARY KEY,
            user_id BIGINT NOT NULL,
            product_id BIGINT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            
            -- 確保同一用戶不能重複收藏同一產品
            UNIQUE(user_id, product_id)
        )
        "#
    )
    .execute(pool)
    .await?;
    
    println!("  ✅ favorites 表創建成功");
    
    // 創建索引
    println!("🔄 創建索引...");
    
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_favorites_user_id ON favorites(user_id)")
        .execute(pool)
        .await?;
    
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_favorites_product_id ON favorites(product_id)")
        .execute(pool)
        .await?;
    
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_favorites_created_at ON favorites(created_at)")
        .execute(pool)
        .await?;
    
    println!("  ✅ 索引創建成功");
    
    // 添加註釋
    println!("🔄 添加表註釋...");
    
    sqlx::query("COMMENT ON TABLE favorites IS '用戶最愛產品表'")
        .execute(pool)
        .await?;
    
    sqlx::query("COMMENT ON COLUMN favorites.id IS '主鍵ID'")
        .execute(pool)
        .await?;
    
    sqlx::query("COMMENT ON COLUMN favorites.user_id IS '用戶ID，關聯users表'")
        .execute(pool)
        .await?;
    
    sqlx::query("COMMENT ON COLUMN favorites.product_id IS '產品ID，關聯products表'")
        .execute(pool)
        .await?;
    
    sqlx::query("COMMENT ON COLUMN favorites.created_at IS '收藏時間'")
        .execute(pool)
        .await?;
    
    println!("  ✅ 註釋添加成功");
    
    // 檢查是否有users和products表，如果有則添加外鍵約束
    let users_exists: bool = sqlx::query_scalar(
        "SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'users'
        )"
    )
    .fetch_one(pool)
    .await?;
    
    let products_exists: bool = sqlx::query_scalar(
        "SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'products'
        )"
    )
    .fetch_one(pool)
    .await?;
    
    if users_exists {
        println!("🔄 添加用戶外鍵約束...");
        sqlx::query(
            "ALTER TABLE favorites 
             ADD CONSTRAINT fk_favorites_user_id 
             FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE"
        )
        .execute(pool)
        .await
        .unwrap_or_else(|e| {
            println!("  ⚠️  添加用戶外鍵約束失敗（可能已存在）: {}", e);
            sqlx::postgres::PgQueryResult::default()
        });
        println!("  ✅ 用戶外鍵約束處理完成");
    } else {
        println!("  ⚠️  users 表不存在，跳過外鍵約束");
    }
    
    if products_exists {
        println!("🔄 添加產品外鍵約束...");
        sqlx::query(
            "ALTER TABLE favorites 
             ADD CONSTRAINT fk_favorites_product_id 
             FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE"
        )
        .execute(pool)
        .await
        .unwrap_or_else(|e| {
            println!("  ⚠️  添加產品外鍵約束失敗（可能已存在）: {}", e);
            sqlx::postgres::PgQueryResult::default()
        });
        println!("  ✅ 產品外鍵約束處理完成");
    } else {
        println!("  ⚠️  products 表不存在，跳過外鍵約束");
    }
    
    println!("🎉 favorites 表及相關結構創建完成！");
    
    Ok(())
}