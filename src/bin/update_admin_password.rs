use std::env;
use sqlx::postgres::PgPoolOptions;
use dotenvy::dotenv;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenv().ok();
    
    let database_url = env::var("DATABASE_URL")
        .expect("DATABASE_URL must be set in .env file");
    
    println!("🔗 連接到雲端資料庫...");
    
    let pool = PgPoolOptions::new()
        .max_connections(5)
        .connect(&database_url)
        .await?;
    
    println!("✅ 成功連接到雲端資料庫!");
    
    // 使用正確的密碼哈希更新 admin 用戶
    let correct_hash = "$2b$12$Xzgl227IJggIPvYEuN2/ge0/EjPw.Gn.01lySGE2Ux7RkvtoIzrv6"; // password
    
    println!("🔄 更新 admin 用戶密碼...");
    let result = sqlx::query("UPDATE users SET password_hash = $1 WHERE username = 'admin'")
        .bind(correct_hash)
        .execute(&pool)
        .await?;
    
    if result.rows_affected() > 0 {
        println!("✅ Admin 用戶密碼已成功更新！");
        println!("   用戶名: admin");
        println!("   密碼: password");
    } else {
        println!("❌ 沒有找到 admin 用戶");
    }
    
    Ok(())
}
