use std::env;
use sqlx::postgres::PgPoolOptions;
use dotenvy::dotenv;
use sqlx::Row;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenv().ok();
    
    let database_url = env::var("DATABASE_URL")
        .expect("DATABASE_URL must be set in .env file");
    
    println!("🔗 連接到雲端資料庫...");
    
    let pool = PgPoolOptions::new()
        .max_connections(5)
        .connect(&database_url)
        .await?;
    
    println!("✅ 成功連接到雲端資料庫!");
    
    // 檢查是否有 admin 用戶
    println!("\n📋 檢查現有用戶...");
    let users = sqlx::query("SELECT id, username, email, pharmacy_name FROM users ORDER BY id LIMIT 10")
        .fetch_all(&pool)
        .await?;
    
    for user in &users {
        let id: i64 = user.get("id");
        let username: String = user.get("username");
        let email: String = user.get("email");
        let pharmacy_name: String = user.get("pharmacy_name");
        println!("  ID: {}, 用戶名: {}, 信箱: {}, 藥局: {}", id, username, email, pharmacy_name);
    }
    
    // 檢查是否有 admin 用戶
    let admin_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM users WHERE username = 'admin'")
        .fetch_one(&pool)
        .await?;
    
    if admin_count > 0 {
        println!("\n⚠️  發現 admin 用戶已存在");
        
        // 檢查 admin 用戶詳情
        let admin_user = sqlx::query("SELECT id, username, email, password_hash FROM users WHERE username = 'admin'")
            .fetch_one(&pool)
            .await?;
        
        let id: i64 = admin_user.get("id");
        let password_hash: String = admin_user.get("password_hash");
        println!("  Admin ID: {}", id);
        println!("  密碼哈希: {}", &password_hash[..50]);
        
        // 更新 admin 用戶的密碼為 admin123
        let new_password_hash = "$2b$12$LQv3c1yqBwuvHpS7TbuOCOFCXDaa8rjDM.6zSaOOgAgF6aLK6nEUq"; // admin123
        
        println!("\n🔄 更新 admin 用戶密碼...");
        sqlx::query("UPDATE users SET password_hash = $1, is_active = true, status = 'approved' WHERE username = 'admin'")
            .bind(new_password_hash)
            .execute(&pool)
            .await?;
        
        println!("✅ Admin 用戶密碼已更新為 'admin123'");
        
    } else {
        println!("\n🆕 創建 admin 用戶...");
        
        // 確保有 admin 角色
        let admin_role_id: Option<i64> = sqlx::query_scalar("SELECT id FROM roles WHERE name = 'admin'")
            .fetch_optional(&pool)
            .await?;
        
        let role_id = if let Some(id) = admin_role_id {
            println!("  找到 admin 角色，ID: {}", id);
            id
        } else {
            println!("  創建 admin 角色...");
            let new_role_id: i64 = sqlx::query_scalar(
                "INSERT INTO roles (name, description) VALUES ('admin', '系統管理員') RETURNING id"
            )
            .fetch_one(&pool)
            .await?;
            println!("  Admin 角色已創建，ID: {}", new_role_id);
            new_role_id
        };
        
        // 創建 admin 用戶
        let password_hash = "$2b$12$LQv3c1yqBwuvHpS7TbuOCOFCXDaa8rjDM.6zSaOOgAgF6aLK6nEUq"; // admin123
        
        let admin_id: i64 = sqlx::query_scalar(
            "INSERT INTO users (username, email, password_hash, pharmacy_name, role_id, is_active, status, created_at, updated_at) 
             VALUES ('admin', '<EMAIL>', $1, 'Admin Pharmacy', $2, true, 'approved', NOW(), NOW()) 
             RETURNING id"
        )
        .bind(password_hash)
        .bind(role_id)
        .fetch_one(&pool)
        .await?;
        
        println!("✅ Admin 用戶已創建，ID: {}, 密碼: admin123", admin_id);
    }
    
    println!("\n🎉 Admin 用戶設定完成！");
    println!("   用戶名: admin");
    println!("   密碼: admin123");
    
    Ok(())
}
