use std::env;
use sqlx::postgres::PgPoolOptions;
use sqlx::{Pool, Postgres};
use dotenvy::dotenv;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenv().ok();
    
    let database_url = env::var("DATABASE_URL")
        .unwrap_or_else(|_| "postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require".to_string());
    
    println!("🚀 開始創建 favorites 表...");
    println!("正在連接到 PostgreSQL 資料庫...");
    
    let pool: Pool<Postgres> = PgPoolOptions::new()
        .max_connections(5)
        .connect(&database_url)
        .await?;
    
    println!("✅ 成功連接到資料庫");
    
    // 創建favorites表
    create_favorites_table(&pool).await?;
    
    // 驗證表創建成功
    verify_table_creation(&pool).await?;
    
    println!("🎉 favorites 表創建完成！現在可以使用我的最愛功能了。");
    
    Ok(())
}

async fn create_favorites_table(pool: &Pool<Postgres>) -> Result<(), sqlx::Error> {
    println!("🔄 檢查 favorites 表是否已存在...");
    
    // 檢查表是否已存在
    let table_exists: bool = sqlx::query_scalar(
        "SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'favorites'
        )"
    )
    .fetch_one(pool)
    .await?;
    
    if table_exists {
        println!("  ℹ️  favorites 表已存在");
        
        // 檢查表結構是否正確
        let columns: Vec<String> = sqlx::query_scalar(
            "SELECT column_name FROM information_schema.columns 
             WHERE table_schema = 'public' AND table_name = 'favorites'
             ORDER BY ordinal_position"
        )
        .fetch_all(pool)
        .await?;
        
        println!("  📋 現有欄位: {:?}", columns);
        
        let expected_columns = vec!["id", "user_id", "product_id", "created_at"];
        let has_all_columns = expected_columns.iter().all(|col| columns.contains(&col.to_string()));
        
        if has_all_columns {
            println!("  ✅ 表結構正確，無需重新創建");
            return Ok(());
        } else {
            println!("  ⚠️  表結構不完整，需要更新");
        }
    }
    
    if !table_exists {
        println!("🔄 創建 favorites 表...");
        
        // 創建favorites表
        sqlx::query(
            r#"
            CREATE TABLE favorites (
                id BIGSERIAL PRIMARY KEY,
                user_id BIGINT NOT NULL,
                product_id BIGINT NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                
                -- 確保同一用戶不能重複收藏同一產品
                UNIQUE(user_id, product_id)
            )
            "#
        )
        .execute(pool)
        .await?;
        
        println!("  ✅ favorites 表創建成功");
    }
    
    // 創建索引
    println!("🔄 創建索引...");
    
    let indexes = vec![
        ("idx_favorites_user_id", "CREATE INDEX IF NOT EXISTS idx_favorites_user_id ON favorites(user_id)"),
        ("idx_favorites_product_id", "CREATE INDEX IF NOT EXISTS idx_favorites_product_id ON favorites(product_id)"),
        ("idx_favorites_created_at", "CREATE INDEX IF NOT EXISTS idx_favorites_created_at ON favorites(created_at)"),
    ];
    
    for (index_name, sql) in indexes {
        sqlx::query(sql).execute(pool).await?;
        println!("  ✅ 索引 {} 創建成功", index_name);
    }
    
    // 添加註釋
    println!("🔄 添加表註釋...");
    
    let comments = vec![
        ("TABLE favorites", "用戶最愛產品表"),
        ("COLUMN favorites.id", "主鍵ID"),
        ("COLUMN favorites.user_id", "用戶ID，關聯users表"),
        ("COLUMN favorites.product_id", "產品ID，關聯products表"),
        ("COLUMN favorites.created_at", "收藏時間"),
    ];
    
    for (target, comment) in comments {
        let sql = format!("COMMENT ON {} IS '{}'", target, comment);
        sqlx::query(&sql).execute(pool).await?;
    }
    
    println!("  ✅ 註釋添加成功");
    
    // 檢查並添加外鍵約束
    add_foreign_key_constraints(pool).await?;
    
    Ok(())
}

async fn add_foreign_key_constraints(pool: &Pool<Postgres>) -> Result<(), sqlx::Error> {
    // 檢查users表是否存在
    let users_exists: bool = sqlx::query_scalar(
        "SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'users'
        )"
    )
    .fetch_one(pool)
    .await?;
    
    // 檢查products表是否存在
    let products_exists: bool = sqlx::query_scalar(
        "SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'products'
        )"
    )
    .fetch_one(pool)
    .await?;
    
    if users_exists {
        println!("🔄 添加用戶外鍵約束...");
        
        // 檢查外鍵約束是否已存在
        let constraint_exists: bool = sqlx::query_scalar(
            "SELECT EXISTS (
                SELECT FROM information_schema.table_constraints 
                WHERE table_schema = 'public' 
                AND table_name = 'favorites'
                AND constraint_name = 'fk_favorites_user_id'
            )"
        )
        .fetch_one(pool)
        .await?;
        
        if !constraint_exists {
            match sqlx::query(
                "ALTER TABLE favorites 
                 ADD CONSTRAINT fk_favorites_user_id 
                 FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE"
            )
            .execute(pool)
            .await {
                Ok(_) => println!("  ✅ 用戶外鍵約束添加成功"),
                Err(e) => println!("  ⚠️  添加用戶外鍵約束失敗: {}", e),
            }
        } else {
            println!("  ℹ️  用戶外鍵約束已存在");
        }
    } else {
        println!("  ⚠️  users 表不存在，跳過用戶外鍵約束");
    }
    
    if products_exists {
        println!("🔄 添加產品外鍵約束...");
        
        // 檢查外鍵約束是否已存在
        let constraint_exists: bool = sqlx::query_scalar(
            "SELECT EXISTS (
                SELECT FROM information_schema.table_constraints 
                WHERE table_schema = 'public' 
                AND table_name = 'favorites'
                AND constraint_name = 'fk_favorites_product_id'
            )"
        )
        .fetch_one(pool)
        .await?;
        
        if !constraint_exists {
            match sqlx::query(
                "ALTER TABLE favorites 
                 ADD CONSTRAINT fk_favorites_product_id 
                 FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE"
            )
            .execute(pool)
            .await {
                Ok(_) => println!("  ✅ 產品外鍵約束添加成功"),
                Err(e) => println!("  ⚠️  添加產品外鍵約束失敗: {}", e),
            }
        } else {
            println!("  ℹ️  產品外鍵約束已存在");
        }
    } else {
        println!("  ⚠️  products 表不存在，跳過產品外鍵約束");
    }
    
    Ok(())
}

async fn verify_table_creation(pool: &Pool<Postgres>) -> Result<(), sqlx::Error> {
    println!("🔍 驗證表創建結果...");
    
    // 檢查表是否存在
    let table_exists: bool = sqlx::query_scalar(
        "SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'favorites'
        )"
    )
    .fetch_one(pool)
    .await?;
    
    if !table_exists {
        return Err(sqlx::Error::RowNotFound);
    }
    
    // 檢查欄位
    let columns: Vec<String> = sqlx::query_scalar(
        "SELECT column_name FROM information_schema.columns 
         WHERE table_schema = 'public' AND table_name = 'favorites'
         ORDER BY ordinal_position"
    )
    .fetch_all(pool)
    .await?;
    
    println!("  📋 表欄位: {:?}", columns);
    
    // 檢查索引
    let indexes: Vec<String> = sqlx::query_scalar(
        "SELECT indexname FROM pg_indexes 
         WHERE tablename = 'favorites' AND schemaname = 'public'"
    )
    .fetch_all(pool)
    .await?;
    
    println!("  🔍 索引: {:?}", indexes);
    
    // 檢查約束
    let constraints: Vec<String> = sqlx::query_scalar(
        "SELECT constraint_name FROM information_schema.table_constraints 
         WHERE table_schema = 'public' AND table_name = 'favorites'"
    )
    .fetch_all(pool)
    .await?;
    
    println!("  🔒 約束: {:?}", constraints);
    
    // 測試插入和查詢（如果有測試數據的話）
    let count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM favorites")
        .fetch_one(pool)
        .await?;
    
    println!("  📊 當前記錄數: {}", count);
    
    println!("✅ 表驗證完成");
    
    Ok(())
}