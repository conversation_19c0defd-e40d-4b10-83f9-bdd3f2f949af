use sqlx::PgPool;
use std::env;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 載入環境變數
    dotenvy::dotenv().ok();
    
    let database_url = env::var("DATABASE_URL")
        .expect("DATABASE_URL must be set");
    
    println!("連接到資料庫...");
    
    let pool = PgPool::connect(&database_url).await?;
    
    // 逐個添加欄位
    let statements = vec![
        "ALTER TABLE users ADD COLUMN status VARCHAR(20) DEFAULT 'approved'",
        "UPDATE users SET submitted_at = created_at WHERE submitted_at IS NULL",
        "INSERT INTO users (username, email, password_hash, pharmacy_name, status, submitted_at) VALUES ('pending_user1', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LQ4YCOIdkpxeNVvuKrpC6FMRCIrxMiAkKq/Zq', '待審核藥局1', 'pending', CURRENT_TIMESTAMP) ON CONFLICT (username) DO NOTHING",
        "INSERT INTO users (username, email, password_hash, pharmacy_name, status, submitted_at) VALUES ('pending_user2', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LQ4YCOIdkpxeNVvuKrpC6FMRCIrxMiAkKq/Zq', '待審核藥局2', 'pending', CURRENT_TIMESTAMP) ON CONFLICT (username) DO NOTHING",
        "INSERT INTO users (username, email, password_hash, pharmacy_name, status, submitted_at) VALUES ('pending_user3', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LQ4YCOIdkpxeNVvuKrpC6FMRCIrxMiAkKq/Zq', '待審核藥局3', 'pending', CURRENT_TIMESTAMP) ON CONFLICT (username) DO NOTHING",
        "CREATE TABLE IF NOT EXISTS user_approval_logs (id SERIAL PRIMARY KEY, user_id INTEGER NOT NULL, action VARCHAR(20) NOT NULL, reason TEXT, performed_by INTEGER NOT NULL, performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, old_status VARCHAR(20), new_status VARCHAR(20))",
        "CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)",
    ];
    
    for (i, statement) in statements.iter().enumerate() {
        println!("執行語句 {}: {}", i + 1, &statement[..std::cmp::min(60, statement.len())]);
        
        match sqlx::query(statement).execute(&pool).await {
            Ok(_) => println!("✅ 成功"),
            Err(e) => println!("⚠️ 失敗: {} (可能已存在)", e),
        }
    }
    
    // 檢查結果
    match sqlx::query_as::<_, (i64,)>("SELECT COUNT(*) FROM users WHERE status = 'pending'")
        .fetch_one(&pool)
        .await {
        Ok((count,)) => println!("📊 待審核用戶數量: {}", count),
        Err(e) => println!("❌ 無法查詢待審核用戶: {}", e),
    }
    
    println!("✅ 完成！");
    
    Ok(())
}