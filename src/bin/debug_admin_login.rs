use std::env;
use sqlx::postgres::PgPoolOptions;
use dotenvy::dotenv;
use sqlx::Row;
use bcrypt::verify;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenv().ok();
    
    let database_url = env::var("DATABASE_URL")
        .expect("DATABASE_URL must be set in .env file");
    
    println!("🔗 連接到雲端資料庫...");
    
    let pool = PgPoolOptions::new()
        .max_connections(5)
        .connect(&database_url)
        .await?;
    
    println!("✅ 成功連接到雲端資料庫!");
    
    // 查詢 admin 用戶的完整資訊
    println!("\n🔍 查詢 admin 用戶資訊...");
    let admin_query = "
        SELECT u.id, u.username, u.email, u.password_hash, u.pharmacy_name, 
               u.role_id, u.is_active, u.status, r.name as role_name
        FROM users u 
        LEFT JOIN roles r ON u.role_id = r.id 
        WHERE u.username = 'admin'
    ";
    
    match sqlx::query(admin_query).fetch_optional(&pool).await? {
        Some(row) => {
            let id: i64 = row.get("id");
            let username: String = row.get("username");
            let email: String = row.get("email");
            let password_hash: String = row.get("password_hash");
            let pharmacy_name: String = row.get("pharmacy_name");
            let role_id: Option<i64> = row.get("role_id");
            let is_active: Option<bool> = row.get("is_active");
            let status: Option<String> = row.get("status");
            let role_name: Option<String> = row.get("role_name");
            
            println!("📋 Admin 用戶資訊:");
            println!("  ID: {}", id);
            println!("  用戶名: {}", username);
            println!("  信箱: {}", email);
            println!("  藥局: {}", pharmacy_name);
            println!("  角色ID: {:?}", role_id);
            println!("  角色名稱: {:?}", role_name);
            println!("  是否啟用: {:?}", is_active);
            println!("  狀態: {:?}", status);
            println!("  密碼哈希: {}...", &password_hash[..50]);
            
            // 測試密碼驗證
            println!("\n🔐 測試密碼驗證...");
            let test_password = "admin123";
            match verify(test_password, &password_hash) {
                Ok(is_valid) => {
                    if is_valid {
                        println!("✅ 密碼 '{}' 驗證成功！", test_password);
                    } else {
                        println!("❌ 密碼 '{}' 驗證失敗！", test_password);
                    }
                }
                Err(e) => {
                    println!("❌ 密碼驗證出錯: {}", e);
                }
            }
            
            // 檢查角色和權限
            if let Some(role_id) = role_id {
                println!("\n🔑 檢查角色權限...");
                let permissions = sqlx::query("
                    SELECT p.name, p.description 
                    FROM role_permissions rp 
                    JOIN permissions p ON rp.permission_id = p.id 
                    WHERE rp.role_id = $1
                ")
                .bind(role_id)
                .fetch_all(&pool)
                .await?;
                
                if permissions.is_empty() {
                    println!("⚠️  該角色沒有任何權限！");
                } else {
                    println!("  權限列表:");
                    for perm in permissions {
                        let name: String = perm.get("name");
                        let desc: Option<String> = perm.get("description");
                        println!("    - {}: {:?}", name, desc);
                    }
                }
            }
            
        }
        None => {
            println!("❌ 找不到 admin 用戶！");
        }
    }
    
    Ok(())
}
