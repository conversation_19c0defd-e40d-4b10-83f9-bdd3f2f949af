use sqlx::PgPool;
use std::env;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 載入環境變數
    dotenvy::dotenv().ok();
    
    let database_url = env::var("DATABASE_URL")
        .expect("DATABASE_URL must be set");
    
    let pool = PgPool::connect(&database_url).await?;
    
    // 建立訊息資料表
    let create_messages_sql = r#"
        CREATE TABLE IF NOT EXISTS messages (
            id SERIAL PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            content TEXT NOT NULL,
            message_type VARCHAR(50) NOT NULL, -- 'promotion', 'system', 'announcement'
            status VARCHAR(20) NOT NULL DEFAULT 'active', -- 'active', 'inactive'
            target_audience VARCHAR(50) DEFAULT 'all',
            priority INTEGER DEFAULT 1,
            starts_at TIMESTAMP WITH TIME ZONE,
            ends_at TIMESTAMP WITH TIME ZONE,
            created_by INTEGER NOT NULL REFERENCES users(id),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
    "#;
    
    sqlx::query(create_messages_sql).execute(&pool).await?;
    println!("Created messages table");
    
    // 建立訊息閱讀狀態資料表
    let create_message_reads_sql = r#"
        CREATE TABLE IF NOT EXISTS message_reads (
            id SERIAL PRIMARY KEY,
            message_id INTEGER NOT NULL REFERENCES messages(id),
            user_id INTEGER NOT NULL REFERENCES users(id),
            read_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(message_id, user_id)
        );
    "#;
    
    sqlx::query(create_message_reads_sql).execute(&pool).await?;
    println!("Created message_reads table");
    
    // 建立索引
    let indexes = vec![
        "CREATE INDEX IF NOT EXISTS idx_messages_status ON messages(status);",
        "CREATE INDEX IF NOT EXISTS idx_messages_type ON messages(message_type);",
        "CREATE INDEX IF NOT EXISTS idx_messages_target_audience ON messages(target_audience);",
        "CREATE INDEX IF NOT EXISTS idx_messages_priority ON messages(priority);",
        "CREATE INDEX IF NOT EXISTS idx_messages_starts_at ON messages(starts_at);",
        "CREATE INDEX IF NOT EXISTS idx_messages_ends_at ON messages(ends_at);",
        "CREATE INDEX IF NOT EXISTS idx_message_reads_user ON message_reads(user_id);",
    ];

    for index_sql in indexes {
        sqlx::query(index_sql).execute(&pool).await?;
    }
    println!("Created indexes");
    
    println!("Successfully created messages tables and indexes");
    
    Ok(())
}
