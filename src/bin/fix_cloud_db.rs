use std::env;
use sqlx::postgres::PgPoolOptions;
use dotenvy::dotenv;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenv().ok();
    
    let database_url = env::var("DATABASE_URL")
        .expect("DATABASE_URL must be set in .env file");
    
    println!("🔗 連接到雲端資料庫...");
    println!("使用連接字串: {}", database_url.split('@').next().unwrap_or("***"));
    
    let pool = PgPoolOptions::new()
        .max_connections(5)
        .connect(&database_url)
        .await?;
    
    println!("✅ 成功連接到雲端資料庫!");
    
    // 讀取並執行 SQL 腳本
    let sql_content = std::fs::read_to_string("fix_cloud_db_structure.sql")?;
    
    println!("📝 執行資料庫結構修復...");
    
    // 執行完整的 SQL 腳本
    println!("執行 SQL 腳本內容:");
    match sqlx::query(&sql_content).execute(&pool).await {
        Ok(result) => {
            println!("✅ SQL 腳本執行成功，影響 {} 行", result.rows_affected());
        }
        Err(e) => {
            println!("❌ 執行失敗: {}", e);
            
            // 嘗試逐行執行
            println!("🔄 嘗試逐行執行...");
            for line in sql_content.lines() {
                let line = line.trim();
                if !line.is_empty() && !line.starts_with("--") && line.contains("ALTER TABLE") {
                    println!("執行: {}", line);
                    match sqlx::query(line).execute(&pool).await {
                        Ok(_) => println!("✅ 執行成功"),
                        Err(e) => {
                            if e.to_string().contains("already exists") {
                                println!("⚠️  欄位已存在，跳過");
                            } else {
                                println!("❌ 執行失敗: {}", e);
                            }
                        }
                    }
                }
            }
        }
    }
    
    println!("\n🎉 資料庫結構修復完成！");
    
    Ok(())
}
