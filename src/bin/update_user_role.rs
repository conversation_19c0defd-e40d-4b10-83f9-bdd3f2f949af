use std::env;
use pharmacy_system::{
    database::Database,
    repositories::{
        user::{UserRepository, PostgresUserRepository},
        role::{RoleRepository, PostgresRoleRepository},
    },
};
use sqlx::Row;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 從命令行參數獲取用戶名和角色
    let args: Vec<String> = env::args().collect();
    
    let (username, role_name) = if args.len() == 1 {
        // 如果沒有參數，默認設置 admin 用戶為管理員
        ("admin".to_string(), "admin".to_string())
    } else if args.len() == 3 {
        (args[1].clone(), args[2].clone())
    } else {
        println!("使用方法: {} [<username> <role_name>]", args[0]);
        println!("無參數時默認將 'admin' 用戶設為管理員角色");
        println!("角色選項: admin, pharmacy");
        println!("說明:");
        println!("  admin    - 管理員，擁有所有權限");
        println!("  pharmacy - 藥局用戶，可以下訂單和管理自己的資料");
        return Ok(());
    };
    
    // 驗證角色名稱 (簡化為兩個角色)
    if !matches!(role_name.as_str(), "admin" | "pharmacy") {
        println!("錯誤: 無效的角色名稱。可用角色: admin, pharmacy");
        return Ok(());
    }

    // 從環境變量獲取數據庫連接
    let database_url = env::var("DATABASE_URL")
        .unwrap_or_else(|_| "postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require".to_string());
    
    println!("連接到數據庫: {}", database_url);
    
    // 創建數據庫連接
    let database = Database::new(&database_url).await?;
    let pool = database.pool();
    
    // 創建倉庫實例
    let user_repo = PostgresUserRepository::new(pool.clone());
    let role_repo = PostgresRoleRepository::new(pool.clone());
    
    // 查找用戶
    let user = user_repo.find_by_username(&username).await?;
    let user = match user {
        Some(user) => user,
        None => {
            println!("錯誤: 找不到用戶 '{}'", username);
            return Ok(());
        }
    };
    
    println!("找到用戶: {} (ID: {})", user.username, user.id);
    
    // 查找角色
    let role = role_repo.find_role_by_name(&role_name).await?;
    let role = match role {
        Some(role) => role,
        None => {
            println!("錯誤: 找不到角色 '{}'", role_name);
            return Ok(());
        }
    };
    
    println!("找到角色: {} (ID: {})", role.name, role.id);
    
    // 更新用戶角色
    let query = "UPDATE users SET role_id = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2";
    sqlx::query(query)
        .bind(role.id)
        .bind(user.id)
        .execute(&**pool)
        .await?;
    
    println!("✅ 成功將用戶 '{}' 的角色更新為 '{}'", username, role_name);
    
    // 驗證更新結果
    let verification_query = r#"
        SELECT u.username, r.name as role_name, 
               ARRAY_AGG(p.name ORDER BY p.name) as permissions
        FROM users u 
        INNER JOIN roles r ON u.role_id = r.id
        LEFT JOIN role_permissions rp ON r.id = rp.role_id
        LEFT JOIN permissions p ON rp.permission_id = p.id
        WHERE u.id = $1
        GROUP BY u.username, r.name
    "#;
    
    let row = sqlx::query(verification_query)
        .bind(user.id)
        .fetch_one(&**pool)
        .await?;
    
    let permissions: Vec<String> = row.try_get::<Vec<Option<String>>, _>("permissions")?
        .into_iter()
        .filter_map(|p| p)
        .collect();
    
    println!("\n📋 用戶權限驗證:");
    println!("用戶名: {}", row.try_get::<String, _>("username")?);
    println!("角色: {}", row.try_get::<String, _>("role_name")?);
    println!("權限: {:?}", permissions);
    
    Ok(())
}