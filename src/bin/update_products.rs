use sqlx::PgPool;
use tracing::{info, error};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    tracing_subscriber::fmt::init();

    let database_url = std::env::var("DATABASE_URL")
        .expect("DATABASE_URL must be set");

    let pool = PgPool::connect(&database_url).await?;
    info!("Connected to database");

    // 為現有產品添加 dosage_form 和 ingredients 值
    let update_query = r#"
        UPDATE products 
        SET 
            dosage_form = CASE 
                WHEN name ILIKE '%錠%' OR name ILIKE '%tablet%' THEN '錠劑'
                WHEN name ILIKE '%膠囊%' OR name ILIKE '%capsule%' THEN '膠囊'
                WHEN name ILIKE '%糖漿%' OR name ILIKE '%syrup%' THEN '糖漿'
                WHEN name ILIKE '%注射%' OR name ILIKE '%injection%' THEN '注射劑'
                WHEN name ILIKE '%軟膏%' OR name ILIKE '%ointment%' THEN '軟膏'
                WHEN name ILIKE '%滴劑%' OR name ILIKE '%drop%' THEN '滴劑'
                WHEN name ILIKE '%粉%' OR name ILIKE '%powder%' THEN '散劑'
                WHEN name ILIKE '%液%' OR name ILIKE '%solution%' THEN '溶液'
                ELSE '錠劑'
            END,
            ingredients = CASE 
                WHEN name ILIKE '%500mg%' THEN REGEXP_REPLACE(name, '.*?(\w+\s*\d+mg).*', '\1', 'i')
                WHEN name ILIKE '%400mg%' THEN REGEXP_REPLACE(name, '.*?(\w+\s*\d+mg).*', '\1', 'i')
                WHEN name ILIKE '%250mg%' THEN REGEXP_REPLACE(name, '.*?(\w+\s*\d+mg).*', '\1', 'i')
                WHEN name ILIKE '%100mg%' THEN REGEXP_REPLACE(name, '.*?(\w+\s*\d+mg).*', '\1', 'i')
                WHEN name ILIKE '%普拿疼%' THEN 'Paracetamol 500mg'
                WHEN name ILIKE '%阿斯匹靈%' OR name ILIKE '%阿司匹林%' THEN 'Acetylsalicylic Acid 100mg'
                WHEN name ILIKE '%布洛芬%' THEN 'Ibuprofen 400mg'
                ELSE '主要成分 100mg'
            END
        WHERE (dosage_form IS NULL OR dosage_form = '') OR (ingredients IS NULL OR ingredients = '')
    "#;

    match sqlx::query(update_query).execute(&pool).await {
        Ok(result) => {
            info!("Updated {} products with dosage_form and ingredients", result.rows_affected());
        }
        Err(e) => {
            error!("Failed to update products: {}", e);
            return Err(e.into());
        }
    }

    // 檢查更新結果
    let products = sqlx::query_as::<_, (String, String, Option<String>, Option<String>)>(
        "SELECT nhi_code, name, ingredients, dosage_form FROM products LIMIT 10"
    )
    .fetch_all(&pool)
    .await?;

    info!("Sample updated products:");
    for product in products {
        info!("  {} - {} | {} | {}", 
            product.0, 
            product.1, 
            product.2.unwrap_or_else(|| "None".to_string()),
            product.3.unwrap_or_else(|| "None".to_string())
        );
    }

    pool.close().await;
    info!("Database connection closed");

    Ok(())
}