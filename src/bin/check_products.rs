use sqlx::{PgPool, Row};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let database_url = std::env::var("DATABASE_URL")
        .unwrap_or_else(|_| "postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require".to_string());
    
    let pool = PgPool::connect(&database_url).await?;
    
    // 檢查產品數量
    let count_row = sqlx::query("SELECT COUNT(*) as count FROM products")
        .fetch_one(&pool)
        .await?;
    
    let count: i64 = count_row.get("count");
    println!("Total products: {}", count);
    
    // 檢查有 ingredients 的產品
    let ingredients_row = sqlx::query(
        "SELECT COUNT(*) as count FROM products WHERE ingredients IS NOT NULL AND ingredients != ''"
    )
    .fetch_one(&pool)
    .await?;
    
    let ingredients_count: i64 = ingredients_row.get("count");
    println!("Products with ingredients: {}", ingredients_count);
    
    // 列出一些產品範例
    let samples = sqlx::query(
        "SELECT name, ingredients FROM products WHERE ingredients IS NOT NULL AND ingredients != '' LIMIT 10"
    )
    .fetch_all(&pool)
    .await?;
    
    println!("\nSample products with ingredients:");
    for row in &samples {
        let name: String = row.get("name");
        let ingredients: Option<String> = row.get("ingredients");
        println!("- {}: {}", name, ingredients.unwrap_or_else(|| "N/A".to_string()));
    }
    
    // 檢查是否有包含 "fibrate" 的產品
    let fibrate_products = sqlx::query(
        "SELECT name, ingredients FROM products WHERE ingredients ILIKE '%fibrate%' LIMIT 5"
    )
    .fetch_all(&pool)
    .await?;
    
    println!("\nProducts containing 'fibrate':");
    for row in &fibrate_products {
        let name: String = row.get("name");
        let ingredients: Option<String> = row.get("ingredients");
        println!("- {}: {}", name, ingredients.unwrap_or_else(|| "N/A".to_string()));
    }
    
    Ok(())
}