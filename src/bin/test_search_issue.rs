use sqlx::{PgPool, Row};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let database_url = std::env::var("DATABASE_URL")
        .unwrap_or_else(|_| "postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require".to_string());
    
    let pool = PgPool::connect(&database_url).await?;
    
    println!("Testing search patterns for norv issue:");
    
    // 測試 norv
    let norv_result = sqlx::query(
        "SELECT name, ingredients FROM products WHERE 
         name ILIKE '%norv%' OR 
         english_name ILIKE '%norv%' OR 
         nhi_code ILIKE '%norv%' OR 
         manufacturer ILIKE '%norv%' OR 
         ingredients ILIKE '%norv%' 
         LIMIT 10"
    )
    .fetch_all(&pool)
    .await?;
    
    println!("\nSearching for 'norv' (found {} results):", norv_result.len());
    for row in &norv_result {
        let name: String = row.get("name");
        let ingredients: Option<String> = row.get("ingredients");
        println!("- {}: {}", name, ingredients.unwrap_or_else(|| "N/A".to_string()));
    }
    
    // 檢查是否有包含 norv 的產品
    let norv_check = sqlx::query(
        "SELECT name, ingredients FROM products WHERE ingredients ILIKE '%norv%'"
    )
    .fetch_all(&pool)
    .await?;
    
    println!("\nAll products with ingredients containing 'norv': {}", norv_check.len());
    
    // 測試 feno
    let result1 = sqlx::query(
        "SELECT name, ingredients FROM products WHERE ingredients ILIKE '%feno%' LIMIT 5"
    )
    .fetch_all(&pool)
    .await?;
    
    println!("\nSearching for 'feno' (found {} results):", result1.len());
    for row in &result1 {
        let name: String = row.get("name");
        let ingredients: Option<String> = row.get("ingredients");
        println!("- {}: {}", name, ingredients.unwrap_or_else(|| "N/A".to_string()));
    }
    
    // 測試 fenof
    let result2 = sqlx::query(
        "SELECT name, ingredients FROM products WHERE ingredients ILIKE '%fenof%' LIMIT 5"
    )
    .fetch_all(&pool)
    .await?;
    
    println!("\nSearching for 'fenof' (found {} results):", result2.len());
    for row in &result2 {
        let name: String = row.get("name");
        let ingredients: Option<String> = row.get("ingredients");
        println!("- {}: {}", name, ingredients.unwrap_or_else(|| "N/A".to_string()));
    }
    
    // 測試完整的 fenofibrate
    let result3 = sqlx::query(
        "SELECT name, ingredients FROM products WHERE ingredients ILIKE '%fenofibrate%' LIMIT 5"
    )
    .fetch_all(&pool)
    .await?;
    
    println!("\nSearching for 'fenofibrate' (found {} results):", result3.len());
    for row in &result3 {
        let name: String = row.get("name");
        let ingredients: Option<String> = row.get("ingredients");
        println!("- {}: {}", name, ingredients.unwrap_or_else(|| "N/A".to_string()));
    }
    
    // 檢查實際的 ingredients 欄位內容
    let all_ingredients = sqlx::query(
        "SELECT name, ingredients FROM products WHERE ingredients IS NOT NULL AND ingredients ILIKE '%feno%'"
    )
    .fetch_all(&pool)
    .await?;
    
    println!("\nAll products with ingredients containing 'feno':");
    for row in &all_ingredients {
        let name: String = row.get("name");
        let ingredients: Option<String> = row.get("ingredients");
        println!("- Product: '{}', Ingredients: '{}'", 
                name, 
                ingredients.unwrap_or_else(|| "N/A".to_string()));
    }
    
    // 檢查字符編碼或特殊字符
    let check_chars = sqlx::query(
        "SELECT name, ingredients, length(ingredients) as len FROM products WHERE ingredients ILIKE '%fenof%'"
    )
    .fetch_all(&pool)
    .await?;
    
    println!("\nDetailed character analysis:");
    for row in &check_chars {
        let name: String = row.get("name");
        let ingredients: Option<String> = row.get("ingredients");
        let len: Option<i32> = row.get("len");
        
        if let Some(ingredients) = ingredients {
            println!("- Product: '{}'", name);
            println!("  Ingredients: '{}' (length: {})", ingredients, len.unwrap_or(0));
            println!("  Bytes: {:?}", ingredients.as_bytes());
        }
    }
    
    Ok(())
}