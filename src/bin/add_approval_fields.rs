use sqlx::PgPool;
use std::env;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 載入環境變數
    dotenvy::dotenv().ok();
    
    let database_url = env::var("DATABASE_URL")
        .expect("DATABASE_URL must be set");
    
    println!("連接到資料庫: {}", database_url.split('@').last().unwrap_or("hidden"));
    
    let pool = PgPool::connect(&database_url).await?;
    
    println!("開始添加用戶審核欄位...");
    
    // 讀取 SQL 文件
    let sql_content = std::fs::read_to_string("add_user_approval_fields.sql")?;
    
    // 分割 SQL 語句並執行
    let statements: Vec<&str> = sql_content
        .split(';')
        .map(|s| s.trim())
        .filter(|s| !s.is_empty() && !s.starts_with("--"))
        .collect();
    
    for (i, statement) in statements.iter().enumerate() {
        if statement.trim().is_empty() {
            continue;
        }
        
        println!("執行語句 {}: {}", i + 1, &statement[..std::cmp::min(50, statement.len())]);
        
        match sqlx::query(statement).execute(&pool).await {
            Ok(_) => println!("✅ 語句 {} 執行成功", i + 1),
            Err(e) => {
                println!("⚠️ 語句 {} 執行失敗: {} (可能是因為欄位已存在)", i + 1, e);
                // 繼續執行其他語句，因為有些錯誤是預期的（如欄位已存在）
            }
        }
    }
    
    println!("✅ 用戶審核欄位添加完成！");
    
    // 檢查結果
    let count: (i64,) = sqlx::query_as("SELECT COUNT(*) FROM users WHERE status = 'pending'")
        .fetch_one(&pool)
        .await?;
    
    println!("📊 待審核用戶數量: {}", count.0);
    
    Ok(())
}