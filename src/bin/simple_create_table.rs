use std::env;
use dotenvy::dotenv;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenv().ok();
    
    let database_url = env::var("DATABASE_URL")
        .expect("DATABASE_URL must be set");
    
    println!("🚀 連接資料庫並創建 favorites 表...");
    
    // 使用tokio_postgres而不是sqlx來避免編譯時檢查
    let (client, connection) = tokio_postgres::connect(&database_url, tokio_postgres::NoTls).await?;
    
    // 在背景執行連接
    tokio::spawn(async move {
        if let Err(e) = connection.await {
            eprintln!("connection error: {}", e);
        }
    });
    
    println!("✅ 資料庫連接成功");
    
    // 檢查表是否已存在
    let rows = client.query(
        "SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'favorites'
        )", 
        &[]
    ).await?;
    
    let table_exists: bool = rows[0].get(0);
    
    if table_exists {
        println!("ℹ️  favorites 表已存在，跳過創建");
        return Ok(());
    }
    
    // 創建表
    client.execute(
        r#"
        CREATE TABLE favorites (
            id BIGSERIAL PRIMARY KEY,
            user_id BIGINT NOT NULL,
            product_id BIGINT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(user_id, product_id)
        )
        "#,
        &[]
    ).await?;
    
    println!("✅ favorites 表創建成功");
    
    // 創建索引
    client.execute("CREATE INDEX idx_favorites_user_id ON favorites(user_id)", &[]).await?;
    client.execute("CREATE INDEX idx_favorites_product_id ON favorites(product_id)", &[]).await?;
    client.execute("CREATE INDEX idx_favorites_created_at ON favorites(created_at)", &[]).await?;
    
    println!("✅ 索引創建成功");
    
    // 添加註釋
    client.execute("COMMENT ON TABLE favorites IS '用戶最愛產品表'", &[]).await?;
    client.execute("COMMENT ON COLUMN favorites.id IS '主鍵ID'", &[]).await?;
    client.execute("COMMENT ON COLUMN favorites.user_id IS '用戶ID'", &[]).await?;
    client.execute("COMMENT ON COLUMN favorites.product_id IS '產品ID'", &[]).await?;
    client.execute("COMMENT ON COLUMN favorites.created_at IS '收藏時間'", &[]).await?;
    
    println!("✅ 註釋添加成功");
    
    // 驗證表創建
    let rows = client.query(
        "SELECT column_name FROM information_schema.columns 
         WHERE table_schema = 'public' AND table_name = 'favorites'
         ORDER BY ordinal_position",
        &[]
    ).await?;
    
    println!("📋 表欄位:");
    for row in rows {
        let column_name: String = row.get(0);
        println!("  - {}", column_name);
    }
    
    println!("🎉 favorites 表創建完成！");
    
    Ok(())
}