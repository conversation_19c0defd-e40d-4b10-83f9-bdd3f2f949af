use reqwest;
use serde_json::Value;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    
    // 測試搜尋 norv
    println!("Testing API search for 'norv'...");
    let response = client
        .get("http://127.0.0.1:8080/api/products?search=norv&page=1&limit=10")
        .send()
        .await?;
    
    let status = response.status();
    let text = response.text().await?;
    
    println!("Status: {}", status);
    println!("Response: {}", text);
    
    if let Ok(json) = serde_json::from_str::<Value>(&text) {
        if let Some(data) = json.get("data") {
            if let Some(products) = data.as_array() {
                println!("\nFound {} products:", products.len());
                for product in products {
                    if let (Some(name), Some(ingredients)) = (product.get("name"), product.get("ingredients")) {
                        println!("- {}: {}", name.as_str().unwrap_or("N/A"), ingredients.as_str().unwrap_or("N/A"));
                    }
                }
            }
        }
    }
    
    // 測試搜尋 fenof
    println!("\n\nTesting API search for 'fenof'...");
    let response2 = client
        .get("http://127.0.0.1:8080/api/products?search=fenof&page=1&limit=10")
        .send()
        .await?;
    
    let status2 = response2.status();
    let text2 = response2.text().await?;
    
    println!("Status: {}", status2);
    println!("Response length: {}", text2.len());
    
    if let Ok(json) = serde_json::from_str::<Value>(&text2) {
        if let Some(data) = json.get("data") {
            if let Some(products) = data.as_array() {
                println!("\nFound {} products:", products.len());
                for product in products {
                    if let (Some(name), Some(ingredients)) = (product.get("name"), product.get("ingredients")) {
                        println!("- {}: {}", name.as_str().unwrap_or("N/A"), ingredients.as_str().unwrap_or("N/A"));
                    }
                }
            }
        }
    }
    
    Ok(())
}