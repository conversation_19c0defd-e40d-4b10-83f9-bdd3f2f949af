use axum::{
    extract::{State, Json as JsonExtractor},
    response::Json,
    routing::{post},
    Router,
};
use serde_json::{json, Value};
use crate::error::AppResult;
use crate::api::auth::AppState;
use crate::services::notification::{NotificationService, NotificationServiceImpl};
use crate::models::{EmailNotification, LineNotification};

pub fn notification_routes() -> Router<AppState> {
    Router::new()
        .route("/email", post(send_email))
        .route("/line", post(send_line))
        .route("/test", post(test_notification))
}

async fn send_email(
    State(state): State<AppState>,
    JsonExtractor(notification): JsonExtractor<EmailNotification>,
) -> AppResult<Json<Value>> {
    let notification_service = NotificationServiceImpl::new(state.config);
    notification_service.send_email(notification).await?;
    
    Ok(Json(json!({
        "message": "Email sent successfully"
    })))
}

async fn send_line(
    State(state): State<AppState>,
    JsonExtractor(notification): JsonExtractor<LineNotification>,
) -> AppResult<Json<Value>> {
    let notification_service = NotificationServiceImpl::new(state.config);
    notification_service.send_line_message(notification).await?;
    
    Ok(Json(json!({
        "message": "Line message sent successfully"
    })))
}

async fn test_notification(
    State(state): State<AppState>,
) -> AppResult<Json<Value>> {
    let notification_service = NotificationServiceImpl::new(state.config);
    
    // 測試 Email
    let test_email = EmailNotification {
        to: "<EMAIL>".to_string(),
        subject: "測試通知".to_string(),
        body: "這是一封測試郵件".to_string(),
        html_body: None,
    };
    
    match notification_service.send_email(test_email).await {
        Ok(_) => tracing::info!("Test email sent successfully"),
        Err(e) => tracing::error!("Test email failed: {}", e),
    }
    
    // 測試 Line
    let test_line = LineNotification {
        user_id: "test-user-id".to_string(),
        message: "測試 Line 通知".to_string(),
    };
    
    match notification_service.send_line_message(test_line).await {
        Ok(_) => tracing::info!("Test line message sent successfully"),
        Err(e) => tracing::error!("Test line message failed: {}", e),
    }
    
    Ok(Json(json!({
        "message": "Test notifications sent"
    })))
} 