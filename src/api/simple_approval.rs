use axum::{
    extract::{State},
    response::J<PERSON>,
    routing::post,
    Router,
};
use serde::{Deserialize, Serialize};
use tracing::{error, info};
use sqlx::Row;

use crate::{
    api::auth::AppState,
    error::AppResult,
};

#[derive(Debug, Serialize, Deserialize)]
pub struct SimpleApprovalRequest {
    pub user_id: i64,
    pub action: String, // "approve" or "reject"
    pub reason: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SimpleApprovalResponse {
    pub success: bool,
    pub message: String,
    pub user_id: i64,
    pub new_status: String,
}

/// 簡單的用戶審核處理
pub async fn handle_user_approval(
    State(state): State<AppState>,
    Json(request): Json<SimpleApprovalRequest>,
) -> AppResult<Json<SimpleApprovalResponse>> {
    info!("處理用戶審核: 用戶 {} 操作 {}", request.user_id, request.action);

    // 驗證操作類型
    if request.action != "approve" && request.action != "reject" {
        return Err(crate::error::AppError::Validation("無效的操作類型".to_string()));
    }

    // 獲取用戶信息
    let user_query = "SELECT username FROM users WHERE id = $1";
    let user_row = sqlx::query(user_query)
        .bind(request.user_id)
        .fetch_optional(&*state.db)
        .await
        .map_err(|e| {
            error!("查找用戶失敗: {}", e);
            crate::error::AppError::DatabaseError(e.into())
        })?
        .ok_or_else(|| crate::error::AppError::NotFound("用戶不存在".to_string()))?;

    let username: String = user_row.get("username");

    // 執行審核操作
    let new_status = match request.action.as_str() {
        "approve" => {
            let update_query = "UPDATE users SET status = 'approved' WHERE id = $1";
            sqlx::query(update_query)
                .bind(request.user_id)
                .execute(&*state.db)
                .await
                .map_err(|e| {
                    error!("批准用戶失敗: {}", e);
                    crate::error::AppError::DatabaseError(e.into())
                })?;
            "approved"
        }
        "reject" => {
            let _reason = request.reason.clone().unwrap_or_else(|| "未提供原因".to_string());
            let update_query = "UPDATE users SET status = 'rejected' WHERE id = $1";
            sqlx::query(update_query)
                .bind(request.user_id)
                .execute(&*state.db)
                .await
                .map_err(|e| {
                    error!("拒絕用戶失敗: {}", e);
                    crate::error::AppError::DatabaseError(e.into())
                })?;
            "rejected"
        }
        _ => return Err(crate::error::AppError::Validation("無效的操作".to_string())),
    };

    // 記錄審核日誌
    let log_query = "INSERT INTO user_approval_logs (user_id, action, reason, performed_by, old_status, new_status) VALUES ($1, $2, $3, $4, $5, $6)";
    if let Err(e) = sqlx::query(log_query)
        .bind(request.user_id)
        .bind(&request.action)
        .bind(&request.reason)
        .bind(1) // 暫時使用 1 作為操作者 ID，實際應該從認證中獲取
        .bind("pending")
        .bind(&new_status)
        .execute(&*state.db)
        .await
    {
        error!("創建審核日誌失敗: {}", e);
        // 不返回錯誤，因為主要操作已成功
    }

    let message = match request.action.as_str() {
        "approve" => format!("用戶 {} 已成功批准", username),
        "reject" => format!("用戶 {} 已被拒絕", username),
        _ => "操作完成".to_string(),
    };

    info!("審核完成: {}", message);

    Ok(Json(SimpleApprovalResponse {
        success: true,
        message,
        user_id: request.user_id,
        new_status: new_status.to_string(),
    }))
}

/// 簡單審核路由
pub fn simple_approval_routes() -> Router<AppState> {
    Router::new()
        .route("/simple-approve", post(handle_user_approval))
}