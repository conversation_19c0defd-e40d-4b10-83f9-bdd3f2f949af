use axum::{
    extract::State,
    response::Json,
    routing::get,
    Router,
};
use serde::{Deserialize, Serialize};
use tracing::{error, info};
use sqlx::Row;

use crate::{
    api::auth::AppState,
    error::AppResult,
};

#[derive(Debug, Serialize, Deserialize)]
pub struct DebugUser {
    pub id: i64,
    pub username: String,
    pub email: String,
    pub pharmacy_name: String,
    pub status: Option<String>,
    pub created_at: String,
    pub submitted_at: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DebugUsersResponse {
    pub success: bool,
    pub data: Vec<DebugUser>,
    pub total: usize,
}

/// 獲取所有用戶資料（調試用）
pub async fn get_all_users_debug(
    State(state): State<AppState>,
) -> AppResult<Json<DebugUsersResponse>> {
    info!("調試：獲取所有用戶資料");

    let query = r#"
        SELECT 
            id, username, email, pharmacy_name, 
            status, created_at, submitted_at
        FROM users 
        WHERE username NOT IN ('admin', 'super_admin', 'root', 'administrator', 'system')
        ORDER BY created_at DESC
        LIMIT 50
    "#;

    let rows = sqlx::query(query)
        .fetch_all(&*state.db)
        .await
        .map_err(|e| {
            error!("獲取用戶資料失敗: {}", e);
            crate::error::AppError::DatabaseError(e.into())
        })?;

    let users: Vec<DebugUser> = rows
        .into_iter()
        .map(|row| DebugUser {
            id: row.get("id"),
            username: row.get("username"),
            email: row.get("email"),
            pharmacy_name: row.get("pharmacy_name"),
            status: row.try_get("status").ok(),
            created_at: row.get::<chrono::DateTime<chrono::Utc>, _>("created_at").to_rfc3339(),
            submitted_at: row.try_get::<chrono::DateTime<chrono::Utc>, _>("submitted_at")
                .ok()
                .map(|dt| dt.to_rfc3339()),
        })
        .collect();

    let total = users.len();

    Ok(Json(DebugUsersResponse {
        success: true,
        data: users,
        total,
    }))
}

/// 調試用戶路由
pub fn debug_user_routes() -> Router<AppState> {
    Router::new()
        .route("/debug/users", get(get_all_users_debug))
}