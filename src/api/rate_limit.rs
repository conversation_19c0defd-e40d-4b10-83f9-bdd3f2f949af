use axum::{
    extract::{Request, State},
    http::StatusCode,
    middleware::Next,
    response::Response,
    Json,
};
use governor::{
    clock::DefaultClock,
    state::{InMemoryState, NotKeyed},
    Quota, RateLimiter,
};
use std::num::NonZeroU32;
use std::net::IpAddr;
use std::sync::Arc;
use crate::api::response::ApiResponse;

/// 限流器類型
#[allow(dead_code)]
type GlobalRateLimiter = RateLimiter<NotKeyed, InMemoryState, DefaultClock>;

/// 限流配置
#[derive(Debug, Clone)]
#[allow(dead_code)]
pub struct RateLimitConfig {
    /// 全局限流：每分鐘請求數
    pub global_requests_per_minute: u32,
    /// IP 限流：每分鐘請求數
    pub ip_requests_per_minute: u32,
    /// 用戶限流：每分鐘請求數
    pub user_requests_per_minute: u32,
    /// 敏感操作限流：每小時請求數
    pub sensitive_requests_per_hour: u32,
}

impl Default for RateLimitConfig {
    fn default() -> Self {
        Self {
            global_requests_per_minute: 10000,  // 全局每分鐘 10k 請求
            ip_requests_per_minute: 100,        // 每個 IP 每分鐘 100 請求
            user_requests_per_minute: 300,      // 每個用戶每分鐘 300 請求
            sensitive_requests_per_hour: 20,    // 敏感操作每小時 20 請求
        }
    }
}

/// 限流狀態管理
#[allow(dead_code)]
pub struct RateLimitState {
    pub config: RateLimitConfig,
    pub global_limiter: GlobalRateLimiter,
}

impl RateLimitState {
    #[allow(dead_code)]
    pub fn new(config: RateLimitConfig) -> Self {
        let global_quota = Quota::per_minute(NonZeroU32::new(config.global_requests_per_minute).unwrap());
        let global_limiter = RateLimiter::direct(global_quota);

        Self {
            config,
            global_limiter,
        }
    }

    /// 檢查全局限流
    #[allow(dead_code)]
    pub async fn check_global_limit(&self) -> bool {
        self.global_limiter.check().is_ok()
    }

    /// 檢查 IP 限流（簡化版本，實際應用中可以使用更複雜的實現）
    #[allow(dead_code)]
    pub async fn check_ip_limit(&self, _ip: IpAddr) -> bool {
        // 暫時返回 true，在生產環境中應該實現真正的 IP 限流
        true
    }

    /// 檢查用戶限流（簡化版本）
    #[allow(dead_code)]
    pub async fn check_user_limit(&self, _user_id: i64) -> bool {
        // 暫時返回 true，在生產環境中應該實現真正的用戶限流
        true
    }

    /// 檢查敏感操作限流（簡化版本）
    #[allow(dead_code)]
    pub async fn check_sensitive_limit(&self, _key: &str) -> bool {
        // 暫時返回 true，在生產環境中應該實現真正的敏感操作限流
        true
    }
}

/// 基礎限流中間件
#[allow(dead_code)]
pub async fn rate_limit_middleware(
    State(rate_limit_state): State<Arc<RateLimitState>>,
    request: Request,
    next: Next,
) -> Result<Response, (StatusCode, Json<ApiResponse<()>>)> {
    // 檢查全局限流
    if !rate_limit_state.check_global_limit().await {
        tracing::warn!("Global rate limit exceeded");
        return Err(create_rate_limit_error("全局請求頻率過高，請稍後再試"));
    }

    // 獲取客戶端 IP
    let client_ip = extract_client_ip(&request);
    
    // 檢查 IP 限流
    if !rate_limit_state.check_ip_limit(client_ip).await {
        tracing::warn!("IP rate limit exceeded for: {}", client_ip);
        return Err(create_rate_limit_error("IP 請求頻率過高，請稍後再試"));
    }

    // 繼續處理請求
    Ok(next.run(request).await)
}

/// 用戶限流中間件（需要認證）
#[allow(dead_code)]
pub async fn user_rate_limit_middleware(
    State(rate_limit_state): State<Arc<RateLimitState>>,
    request: Request,
    next: Next,
) -> Result<Response, (StatusCode, Json<ApiResponse<()>>)> {
    // 從請求中提取用戶 ID（假設已通過認證中間件）
    if let Some(user_id) = extract_user_id(&request) {
        if !rate_limit_state.check_user_limit(user_id).await {
            tracing::warn!("User rate limit exceeded for user: {}", user_id);
            return Err(create_rate_limit_error("用戶請求頻率過高，請稍後再試"));
        }
    }

    Ok(next.run(request).await)
}

/// 敏感操作限流中間件
#[allow(dead_code)]
pub async fn sensitive_rate_limit_middleware(
    State(rate_limit_state): State<Arc<RateLimitState>>,
    request: Request,
    next: Next,
) -> Result<Response, (StatusCode, Json<ApiResponse<()>>)> {
    // 獲取客戶端 IP 和用戶 ID 組合作為限流鍵
    let client_ip = extract_client_ip(&request);
    let user_id = extract_user_id(&request).unwrap_or(0);
    let sensitive_key = format!("{}:{}", client_ip, user_id);

    if !rate_limit_state.check_sensitive_limit(&sensitive_key).await {
        tracing::warn!("Sensitive operation rate limit exceeded for: {}", sensitive_key);
        return Err(create_rate_limit_error("敏感操作頻率過高，請稍後再試"));
    }

    Ok(next.run(request).await)
}

/// 提取客戶端 IP 地址
#[allow(dead_code)]
fn extract_client_ip(request: &Request) -> IpAddr {
    // 嘗試從各種標頭中提取真實 IP
    let headers = request.headers();
    
    // 1. 檢查 X-Forwarded-For
    if let Some(forwarded_for) = headers.get("x-forwarded-for") {
        if let Ok(header_str) = forwarded_for.to_str() {
            if let Some(first_ip) = header_str.split(',').next() {
                if let Ok(ip) = first_ip.trim().parse::<IpAddr>() {
                    return ip;
                }
            }
        }
    }

    // 2. 檢查 X-Real-IP
    if let Some(real_ip) = headers.get("x-real-ip") {
        if let Ok(ip_str) = real_ip.to_str() {
            if let Ok(ip) = ip_str.parse::<IpAddr>() {
                return ip;
            }
        }
    }

    // 3. 檢查 CF-Connecting-IP (Cloudflare)
    if let Some(cf_ip) = headers.get("cf-connecting-ip") {
        if let Ok(ip_str) = cf_ip.to_str() {
            if let Ok(ip) = ip_str.parse::<IpAddr>() {
                return ip;
            }
        }
    }

    // 4. 默認回退到 localhost
    IpAddr::from([127, 0, 0, 1])
}

/// 從請求中提取用戶 ID
#[allow(dead_code)]
fn extract_user_id(request: &Request) -> Option<i64> {
    // 從請求擴展中獲取用戶 ID（由認證中間件設置）
    request.extensions().get::<i64>().copied()
}

/// 創建限流錯誤響應
#[allow(dead_code)]
fn create_rate_limit_error(message: &str) -> (StatusCode, Json<ApiResponse<()>>) {
    let response = ApiResponse {
        success: false,
        data: None,
        message: Some(message.to_string()),
        error: Some("RATE_LIMIT_EXCEEDED".to_string()),
    };
    
    (StatusCode::TOO_MANY_REQUESTS, Json(response))
}

/// 限流監控和統計
#[allow(dead_code)]
pub struct RateLimitMetrics {
    pub total_requests: u64,
    pub global_limited: u64,
    pub ip_limited: u64,
    pub user_limited: u64,
    pub sensitive_limited: u64,
}

impl RateLimitMetrics {
    #[allow(dead_code)]
    pub fn new() -> Self {
        Self {
            total_requests: 0,
            global_limited: 0,
            ip_limited: 0,
            user_limited: 0,
            sensitive_limited: 0,
        }
    }

    #[allow(dead_code)]
    pub fn record_request(&mut self) {
        self.total_requests += 1;
    }

    #[allow(dead_code)]
    pub fn record_global_limit(&mut self) {
        self.global_limited += 1;
    }

    #[allow(dead_code)]
    pub fn record_ip_limit(&mut self) {
        self.ip_limited += 1;
    }

    #[allow(dead_code)]
    pub fn record_user_limit(&mut self) {
        self.user_limited += 1;
    }

    #[allow(dead_code)]
    pub fn record_sensitive_limit(&mut self) {
        self.sensitive_limited += 1;
    }

    #[allow(dead_code)]
    pub fn get_limit_rate(&self) -> f64 {
        if self.total_requests == 0 {
            0.0
        } else {
            let total_limited = self.global_limited + self.ip_limited + self.user_limited + self.sensitive_limited;
            (total_limited as f64) / (self.total_requests as f64) * 100.0
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::{body::Body, http::Method};
    use std::time::Duration;
    use tokio::time::sleep;

    #[tokio::test]
    async fn test_rate_limit_config_default() {
        let config = RateLimitConfig::default();
        assert_eq!(config.global_requests_per_minute, 10000);
        assert_eq!(config.ip_requests_per_minute, 100);
        assert_eq!(config.user_requests_per_minute, 300);
        assert_eq!(config.sensitive_requests_per_hour, 20);
    }

    #[tokio::test]
    async fn test_global_rate_limit() {
        let config = RateLimitConfig {
            global_requests_per_minute: 2,
            ..Default::default()
        };
        let state = RateLimitState::new(config);

        // 前兩個請求應該通過
        assert!(state.check_global_limit().await);
        assert!(state.check_global_limit().await);
        
        // 第三個請求應該被限制
        assert!(!state.check_global_limit().await);
    }

    #[tokio::test]
    async fn test_ip_rate_limit() {
        let config = RateLimitConfig {
            ip_requests_per_minute: 2,
            ..Default::default()
        };
        let state = RateLimitState::new(config);
        let test_ip = IpAddr::from([192, 168, 1, 1]);

        // 前兩個請求應該通過
        assert!(state.check_ip_limit(test_ip).await);
        assert!(state.check_ip_limit(test_ip).await);
        
        // 第三個請求應該被限制
        assert!(!state.check_ip_limit(test_ip).await);
    }

    #[test]
    fn test_extract_client_ip() {
        let request = Request::builder()
            .method(Method::GET)
            .uri("/")
            .header("x-forwarded-for", "*************, **********, ***************")
            .body(Body::empty())
            .unwrap();

        let ip = extract_client_ip(&request);
        assert_eq!(ip, IpAddr::from([203, 0, 113, 195]));
    }

    #[test]
    fn test_rate_limit_metrics() {
        let mut metrics = RateLimitMetrics::new();
        
        metrics.record_request();
        metrics.record_request();
        metrics.record_global_limit();
        
        assert_eq!(metrics.total_requests, 2);
        assert_eq!(metrics.global_limited, 1);
        assert_eq!(metrics.get_limit_rate(), 50.0);
    }
}