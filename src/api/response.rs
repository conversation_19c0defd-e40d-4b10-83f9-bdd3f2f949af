use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde::{Deserialize, Serialize};


/// 標準化的 API 回應格式
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub message: Option<String>,
    pub error: Option<String>,
}

#[allow(dead_code)]
impl<T> ApiResponse<T>
where
    T: Serialize,
{
    /// 建立成功回應
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: None,
            error: None,
        }
    }

    /// 建立成功回應並附帶訊息
    pub fn success_with_message(data: T, message: String) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: Some(message),
            error: None,
        }
    }
}

#[allow(dead_code)]
impl ApiResponse<()> {
    /// 建立錯誤回應
    pub fn error(error: String) -> Self {
        Self {
            success: false,
            data: None,
            message: None,
            error: Some(error),
        }
    }

    /// 建立簡單成功回應（無資料）
    pub fn success_empty() -> Self {
        Self {
            success: true,
            data: None,
            message: None,
            error: None,
        }
    }

    /// 建立簡單成功回應並附帶訊息
    pub fn success_message(message: String) -> Self {
        Self {
            success: true,
            data: None,
            message: Some(message),
            error: None,
        }
    }
}

impl<T> IntoResponse for ApiResponse<T>
where
    T: Serialize,
{
    fn into_response(self) -> Response {
        let status = if self.success {
            StatusCode::OK
        } else {
            StatusCode::BAD_REQUEST
        };

        (status, Json(self)).into_response()
    }
}

/// 分頁回應資料結構
#[derive(Debug, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub items: Vec<T>,
    pub total: i64,
    pub page: i64,
    pub per_page: i64,
    pub total_pages: i64,
}

#[allow(dead_code)]
impl<T> PaginatedResponse<T> {
    pub fn new(items: Vec<T>, total: i64, page: i64, per_page: i64) -> Self {
        let total_pages = (total + per_page - 1) / per_page;
        Self {
            items,
            total,
            page,
            per_page,
            total_pages,
        }
    }
}

/// 分頁查詢參數
#[derive(Debug, Deserialize)]
pub struct PaginationParams {
    #[serde(default = "default_page")]
    pub page: i64,
    #[serde(default = "default_per_page")]
    pub per_page: i64,
}

fn default_page() -> i64 {
    1
}

fn default_per_page() -> i64 {
    20
}

#[allow(dead_code)]
impl PaginationParams {
    pub fn offset(&self) -> i64 {
        (self.page - 1) * self.per_page
    }

    pub fn limit(&self) -> i64 {
        self.per_page
    }
}

/// 檔案上傳回應
#[derive(Debug, Serialize, Deserialize)]
pub struct FileUploadResponse {
    pub filename: String,
    pub size: u64,
    pub content_type: Option<String>,
    pub uploaded_at: chrono::DateTime<chrono::Utc>,
}

/// 匯入結果回應
#[derive(Debug, Serialize, Deserialize)]
pub struct ImportResponse {
    pub total_records: usize,
    pub successful_imports: usize,
    pub failed_imports: usize,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
}