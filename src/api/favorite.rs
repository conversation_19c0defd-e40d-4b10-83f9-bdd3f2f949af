use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::Json,
    Extension,
    routing::{get, post, delete},
    Router,
};
use serde_json::{json, Value};
use crate::{
    models::FavoriteRequest,
    repositories::favorite::FavoriteRepository,
    api::{auth::AppState, middleware::Claims},
};

/// 切換產品的收藏狀態
pub async fn toggle_favorite(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
    Json(request): Json<FavoriteRequest>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    let favorite_repo = FavoriteRepository::new(state.db.clone());
    
    // 檢查產品是否已經在最愛中
    let is_favorite = favorite_repo
        .is_favorite(claims.user_id, request.product_id)
        .await
        .map_err(|e| {
            eprintln!("檢查最愛狀態失敗: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(json!({
                    "success": false,
                    "message": "檢查最愛狀態失敗"
                })),
            )
        })?;

    let (success, message, new_is_favorite) = if is_favorite {
        // 從最愛中移除
        let removed = favorite_repo
            .remove_favorite(claims.user_id, request.product_id)
            .await
            .map_err(|e| {
                eprintln!("移除最愛失敗: {}", e);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(json!({
                        "success": false,
                        "message": "移除最愛失敗"
                    })),
                )
            })?;

        if removed {
            (true, "已從最愛移除".to_string(), false)
        } else {
            (false, "移除最愛失敗".to_string(), true)
        }
    } else {
        // 添加到最愛
        favorite_repo
            .add_favorite(claims.user_id, request.product_id)
            .await
            .map_err(|e| {
                eprintln!("添加最愛失敗: {}", e);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(json!({
                        "success": false,
                        "message": "添加最愛失敗"
                    })),
                )
            })?;

        (true, "已加入最愛".to_string(), true)
    };

    Ok(Json(json!({
        "success": success,
        "message": message,
        "is_favorite": new_is_favorite
    })))
}

/// 獲取用戶的所有最愛產品
pub async fn get_favorites(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    let favorite_repo = FavoriteRepository::new(state.db.clone());
    
    let favorites = favorite_repo
        .get_user_favorites(claims.user_id)
        .await
        .map_err(|e| {
            eprintln!("獲取最愛產品失敗: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(json!({
                    "success": false,
                    "message": "獲取最愛產品失敗"
                })),
            )
        })?;

    Ok(Json(json!({
        "success": true,
        "data": favorites,
        "count": favorites.len()
    })))
}

/// 檢查產品是否在最愛中
pub async fn check_favorite(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
    Path(product_id): Path<i64>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    let favorite_repo = FavoriteRepository::new(state.db.clone());
    
    let is_favorite = favorite_repo
        .is_favorite(claims.user_id, product_id)
        .await
        .map_err(|e| {
            eprintln!("檢查最愛狀態失敗: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(json!({
                    "success": false,
                    "message": "檢查最愛狀態失敗"
                })),
            )
        })?;

    Ok(Json(json!({
        "success": true,
        "is_favorite": is_favorite
    })))
}

/// 清空所有最愛
pub async fn clear_favorites(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    let favorite_repo = FavoriteRepository::new(state.db.clone());
    
    let deleted_count = favorite_repo
        .clear_user_favorites(claims.user_id)
        .await
        .map_err(|e| {
            eprintln!("清空最愛失敗: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(json!({
                    "success": false,
                    "message": "清空最愛失敗"
                })),
            )
        })?;

    Ok(Json(json!({
        "success": true,
        "message": format!("已清空 {} 個最愛產品", deleted_count),
        "deleted_count": deleted_count
    })))
}

/// 獲取最愛統計信息
pub async fn get_favorite_stats(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    let favorite_repo = FavoriteRepository::new(state.db.clone());
    
    let count = favorite_repo
        .get_favorite_stats(claims.user_id)
        .await
        .map_err(|e| {
            eprintln!("獲取最愛統計失敗: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(json!({
                    "success": false,
                    "message": "獲取最愛統計失敗"
                })),
            )
        })?;

    Ok(Json(json!({
        "success": true,
        "favorite_count": count
    })))
}

/// 獲取最受歡迎的產品
pub async fn get_popular_products(
    State(state): State<AppState>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    let favorite_repo = FavoriteRepository::new(state.db.clone());
    
    let popular_products = favorite_repo
        .get_popular_products(10) // 獲取前10個最受歡迎的產品
        .await
        .map_err(|e| {
            eprintln!("獲取熱門產品失敗: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(json!({
                    "success": false,
                    "message": "獲取熱門產品失敗"
                })),
            )
        })?;

    let products: Vec<_> = popular_products
        .into_iter()
        .map(|(id, name, count)| json!({
            "id": id,
            "name": name,
            "favorite_count": count
        }))
        .collect();

    Ok(Json(json!({
        "success": true,
        "data": products
    })))
}



/// 我的最愛路由
pub fn favorite_routes() -> Router<AppState> {
    Router::new()
        .route("/toggle", post(toggle_favorite))
        .route("/", get(get_favorites))
        .route("/check/:product_id", get(check_favorite))
        .route("/clear", delete(clear_favorites))
        .route("/stats", get(get_favorite_stats))
        .route("/popular", get(get_popular_products))
}