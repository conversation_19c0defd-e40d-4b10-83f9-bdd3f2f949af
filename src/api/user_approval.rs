use axum::{
    extract::{Path, State},
    response::<PERSON><PERSON>,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use tracing::{error, info};
use sqlx::Row;

use crate::{
    api::middleware::AuthContext,
    api::auth::AppState,
    error::AppResult,
};

#[derive(Debug, Serialize, Deserialize)]
pub struct PendingUser {
    pub id: i64,
    pub username: String,
    pub email: String,
    pub pharmacy_name: String,
    pub contact_person: Option<String>,
    pub phone: Option<String>,
    pub mobile: Option<String>,
    pub institution_code: Option<String>,
    pub address: Option<String>,
    pub submitted_at: String,
    pub status: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PendingUsersResponse {
    pub success: bool,
    pub data: Vec<PendingUser>,
    pub total: usize,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ApprovalRequest {
    pub user_id: i64,
    pub action: String, // "approve" or "reject"
    pub reason: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ApprovalResponse {
    pub success: bool,
    pub message: String,
    pub user_id: i64,
    pub new_status: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserApprovalLog {
    pub id: i64,
    pub user_id: i64,
    pub action: String,
    pub reason: Option<String>,
    pub performed_by: i64,
    pub performed_at: String,
    pub old_status: Option<String>,
    pub new_status: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ApprovalLogsResponse {
    pub success: bool,
    pub data: Vec<UserApprovalLog>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BatchApprovalRequest {
    pub user_ids: Vec<i64>,
    pub action: String, // "approve" or "reject"
    pub reason: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BatchApprovalResponse {
    pub success: bool,
    pub message: String,
    pub processed: usize,
    pub failed: Vec<i64>,
}

/// 獲取待審核用戶列表
pub async fn get_pending_users(
    State(state): State<AppState>,
    auth: AuthContext,
) -> AppResult<Json<PendingUsersResponse>> {
    info!("管理員 {} 查看待審核用戶", auth.claims.user_id);

    // 檢查權限 - 暫時跳過權限檢查，直接允許管理員操作
    // TODO: 實現完整的權限檢查系統

    let query = r#"
        SELECT 
            id, username, email, pharmacy_name, contact_person, 
            phone, mobile, institution_code, address, 
            COALESCE(submitted_at, created_at) as submitted_at,
            COALESCE(status, 'pending') as status
        FROM users 
        WHERE COALESCE(status, 'pending') = 'pending'
        AND username NOT IN ('admin', 'super_admin', 'root', 'administrator', 'system')
        ORDER BY COALESCE(submitted_at, created_at) DESC
    "#;

    let rows = sqlx::query(query)
        .fetch_all(&*state.db)
        .await
        .map_err(|e| {
            error!("獲取待審核用戶失敗: {}", e);
            crate::error::AppError::DatabaseError(e.into())
        })?;

    let pending_users: Vec<PendingUser> = rows
        .into_iter()
        .map(|row| PendingUser {
            id: row.get("id"),
            username: row.get("username"),
            email: row.get("email"),
            pharmacy_name: row.get("pharmacy_name"),
            contact_person: row.try_get("contact_person").ok(),
            phone: row.try_get("phone").ok(),
            mobile: row.try_get("mobile").ok(),
            institution_code: row.try_get("institution_code").ok(),
            address: row.try_get("address").ok(),
            submitted_at: row.get::<chrono::DateTime<chrono::Utc>, _>("submitted_at").to_rfc3339(),
            status: row.get("status"),
        })
        .collect();

    let total = pending_users.len();

    Ok(Json(PendingUsersResponse {
        success: true,
        data: pending_users,
        total,
    }))
}

/// 審核用戶（批准或拒絕）
pub async fn approve_user(
    State(state): State<AppState>,
    auth: AuthContext,
    Json(request): Json<ApprovalRequest>,
) -> AppResult<Json<ApprovalResponse>> {
    info!("管理員 {} 審核用戶 {}: {}", auth.claims.user_id, request.user_id, request.action);

    // 檢查操作是否有效
    if request.action != "approve" && request.action != "reject" {
        return Err(crate::error::AppError::Validation("無效的操作".to_string()));
    }

    // 獲取原始用戶狀態
    let user_query = "SELECT status, username FROM users WHERE id = $1";
    let user_row = sqlx::query(user_query)
        .bind(request.user_id)
        .fetch_optional(&*state.db)
        .await
        .map_err(|e| {
            error!("查找用戶失敗: {}", e);
            crate::error::AppError::DatabaseError(e.into())
        })?
        .ok_or_else(|| crate::error::AppError::NotFound("用戶不存在".to_string()))?;

    let old_status: String = user_row.try_get("status").unwrap_or_else(|_| "pending".to_string());
    let username: String = user_row.get("username");

    // 執行審核操作
    let new_status = match request.action.as_str() {
        "approve" => {
            let update_query = r#"
                UPDATE users 
                SET status = 'approved', 
                    approved_at = CURRENT_TIMESTAMP, 
                    approved_by = $1 
                WHERE id = $2
            "#;
            sqlx::query(update_query)
                .bind(auth.claims.user_id)
                .bind(request.user_id)
                .execute(&*state.db)
                .await
                .map_err(|e| {
                    error!("批准用戶失敗: {}", e);
                    crate::error::AppError::DatabaseError(e.into())
                })?;
            "approved"
        }
        "reject" => {
            let reason = request.reason.clone().unwrap_or_else(|| "未提供原因".to_string());
            let update_query = r#"
                UPDATE users 
                SET status = 'rejected', 
                    rejection_reason = $1,
                    approved_by = $2
                WHERE id = $3
            "#;
            sqlx::query(update_query)
                .bind(&reason)
                .bind(auth.claims.user_id)
                .bind(request.user_id)
                .execute(&*state.db)
                .await
                .map_err(|e| {
                    error!("拒絕用戶失敗: {}", e);
                    crate::error::AppError::DatabaseError(e.into())
                })?;
            "rejected"
        }
        _ => return Err(crate::error::AppError::Validation("無效的操作".to_string())),
    };

    // 記錄審核日誌
    let log_query = r#"
        INSERT INTO user_approval_logs 
        (user_id, action, reason, performed_by, old_status, new_status) 
        VALUES ($1, $2, $3, $4, $5, $6)
    "#;
    
    if let Err(e) = sqlx::query(log_query)
        .bind(request.user_id)
        .bind(&request.action)
        .bind(&request.reason)
        .bind(auth.claims.user_id)
        .bind(&old_status)
        .bind(&new_status)
        .execute(&*state.db)
        .await
    {
        error!("創建審核日誌失敗: {}", e);
        // 不返回錯誤，因為主要操作已成功
    }

    let message = match request.action.as_str() {
        "approve" => format!("用戶 {} 已成功批准", username),
        "reject" => format!("用戶 {} 已被拒絕", username),
        _ => "操作完成".to_string(),
    };

    Ok(Json(ApprovalResponse {
        success: true,
        message,
        user_id: request.user_id,
        new_status: new_status.to_string(),
    }))
}

/// 獲取用戶審核日誌
pub async fn get_approval_logs(
    State(state): State<AppState>,
    auth: AuthContext,
    Path(user_id): Path<i64>,
) -> AppResult<Json<ApprovalLogsResponse>> {
    info!("管理員 {} 查看用戶 {} 的審核日誌", auth.claims.user_id, user_id);

    let query = r#"
        SELECT 
            id, user_id, action, reason, performed_by, 
            performed_at, old_status, new_status
        FROM user_approval_logs 
        WHERE user_id = $1 
        ORDER BY performed_at DESC
    "#;

    let rows = sqlx::query(query)
        .bind(user_id)
        .fetch_all(&*state.db)
        .await
        .map_err(|e| {
            error!("獲取審核日誌失敗: {}", e);
            crate::error::AppError::DatabaseError(e.into())
        })?;

    let logs: Vec<UserApprovalLog> = rows
        .into_iter()
        .map(|row| UserApprovalLog {
            id: row.get("id"),
            user_id: row.get("user_id"),
            action: row.get("action"),
            reason: row.try_get("reason").ok(),
            performed_by: row.get("performed_by"),
            performed_at: row.get::<chrono::DateTime<chrono::Utc>, _>("performed_at").to_rfc3339(),
            old_status: row.try_get("old_status").ok(),
            new_status: row.try_get("new_status").ok(),
        })
        .collect();

    Ok(Json(ApprovalLogsResponse {
        success: true,
        data: logs,
    }))
}

/// 批量審核用戶
pub async fn batch_approve_users(
    State(state): State<AppState>,
    auth: AuthContext,
    Json(request): Json<BatchApprovalRequest>,
) -> AppResult<Json<BatchApprovalResponse>> {
    info!("管理員 {} 批量審核 {} 個用戶", auth.claims.user_id, request.user_ids.len());

    if request.user_ids.is_empty() {
        return Err(crate::error::AppError::Validation("用戶ID列表不能為空".to_string()));
    }

    if request.action != "approve" && request.action != "reject" {
        return Err(crate::error::AppError::Validation("無效的操作".to_string()));
    }

    let mut processed = 0;
    let mut failed = Vec::new();

    for user_id in &request.user_ids {
        let approval_request = ApprovalRequest {
            user_id: *user_id,
            action: request.action.clone(),
            reason: request.reason.clone(),
        };

        match approve_user(
            State(state.clone()),
            auth.clone(),
            Json(approval_request),
        ).await {
            Ok(_) => processed += 1,
            Err(e) => {
                error!("批量審核用戶 {} 失敗: {}", user_id, e);
                failed.push(*user_id);
            }
        }
    }

    let message = if failed.is_empty() {
        format!("成功{}了 {} 個用戶", 
            if request.action == "approve" { "批准" } else { "拒絕" }, 
            processed)
    } else {
        format!("成功{}了 {} 個用戶，{} 個失敗", 
            if request.action == "approve" { "批准" } else { "拒絕" }, 
            processed, 
            failed.len())
    };

    Ok(Json(BatchApprovalResponse {
        success: failed.is_empty(),
        message,
        processed,
        failed,
    }))
}

/// 獲取用戶統計數據
async fn get_user_statistics(
    State(state): State<AppState>,
    Extension(claims): Extension<crate::api::middleware::Claims>,
) -> Result<Json<ApiResponse<serde_json::Value>>, (StatusCode, Json<ApiResponse<()>>)> {
    // 檢查管理員權限
    let permissions = match state.role_service.get_user_permissions(claims.user_id).await {
        Ok(perms) => perms,
        Err(e) => {
            tracing::error!("Failed to get user permissions: {}", e);
            return Err((
                StatusCode::FORBIDDEN,
                Json(ApiResponse {
                    success: false,
                    data: None,
                    message: Some("權限不足".to_string()),
                    error: None,
                }),
            ));
        }
    };

    if !permissions.can_approve_users {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ApiResponse {
                success: false,
                data: None,
                message: Some("權限不足：無法查看用戶統計".to_string()),
                error: None,
            }),
        ));
    }

    // 查詢統計數據
    match get_user_approval_statistics(&state.user_service).await {
        Ok(stats) => Ok(Json(ApiResponse {
            success: true,
            data: Some(stats),
            message: None,
            error: None,
        })),
        Err(e) => {
            tracing::error!("Failed to get user statistics: {}", e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse {
                    success: false,
                    data: None,
                    message: Some("獲取統計數據失敗".to_string()),
                    error: Some(e.to_string()),
                }),
            ))
        }
    }
}

/// 獲取用戶審核統計
async fn get_user_approval_statistics(
    user_service: &Arc<dyn crate::services::user::UserService>,
) -> Result<serde_json::Value, crate::error::AppError> {
    // 獲取所有用戶數據進行統計
    let all_users = user_service.get_all_users_with_roles().await?;
    
    let now = chrono::Utc::now().date_naive();
    let mut approved_today = 0;
    let mut rejected_total = 0;
    
    for user in all_users {
        // 統計今日已審核（通過）的用戶
        if user.status == Some("approved".to_string()) {
            if let Some(approved_at) = user.approved_at {
                if approved_at.date_naive() == now {
                    approved_today += 1;
                }
            }
        }
        
        // 統計被拒絕的用戶總數
        if user.status == Some("rejected".to_string()) {
            rejected_total += 1;
        }
    }
    
    Ok(serde_json::json!({
        "approved_today": approved_today,
        "rejected_total": rejected_total
    }))
}

/// 用戶審核路由
pub fn user_approval_routes() -> Router<AppState> {
    Router::new()
        .route("/users/pending", get(get_pending_users))
        .route("/users/approve", post(approve_user))
        .route("/users/batch-approve", post(batch_approve_users))
        .route("/users/:user_id/logs", get(get_approval_logs))
        .route("/users/statistics", get(get_user_statistics))
}