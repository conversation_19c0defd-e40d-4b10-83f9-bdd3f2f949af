use axum::{
    extract::{Path, State, Json, Extension},
    http::StatusCode,
    routing::{get, post, put, delete},
    Router,
};
use serde_json::Value;

use crate::api::{
    auth::AppState,
    middleware::AuthContext,
    response::ApiResponse,
};
use crate::models::message::{CreateMessageRequest, MessageResponse};
use crate::repositories::message::MessageRepository;

pub fn message_routes() -> Router<AppState> {
    Router::new()
        .route("/", get(get_messages).post(create_message))
        .route("/:id", put(update_message).delete(delete_message))
        .route("/:id/read", post(mark_message_read))
        .route("/:id/status", put(update_message_status))
}

async fn create_message(
    State(state): State<AppState>,
    Extension(auth): Extension<AuthContext>,
    <PERSON><PERSON>(request): <PERSON><PERSON><CreateMessageRequest>,
) -> Result<Json<ApiResponse<MessageResponse>>, StatusCode> {
    // 臨時允許 admin 和 super_admin 創建訊息
    if !auth.is_super_admin() && !auth.is_admin() {
        return Err(StatusCode::FORBIDDEN);
    }

    let repo = MessageRepository::new(state.db.clone());
    
    match repo.create_message(request, auth.claims.user_id as i32).await {
        Ok(message) => {
            let response = MessageResponse {
                id: message.id,
                title: message.title,
                content: message.content,
                message_type: message.message_type,
                status: message.status,
                target_audience: message.target_audience,
                priority: message.priority,
                starts_at: message.starts_at,
                ends_at: message.ends_at,
                created_by: message.created_by,
                created_at: message.created_at,
                updated_at: message.updated_at,
                is_read: false,
            };
            Ok(Json(ApiResponse::success(response)))
        }
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

async fn get_messages(
    State(state): State<AppState>,
    Extension(auth): Extension<AuthContext>,
) -> Result<Json<ApiResponse<Vec<MessageResponse>>>, StatusCode> {
    let repo = MessageRepository::new(state.db.clone());
    
    // 如果是管理員，獲取所有消息
    if auth.is_admin() || auth.is_super_admin() {
        match repo.get_all_messages().await {
            Ok(messages) => Ok(Json(ApiResponse::success(messages))),
            Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
        }
    } else {
        // 普通用戶獲取活躍消息（包含閱讀狀態）
        match repo.get_active_messages(auth.claims.user_id as i32).await {
            Ok(messages) => Ok(Json(ApiResponse::success(messages))),
            Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
        }
    }
}

async fn mark_message_read(
    State(state): State<AppState>,
    Extension(auth): Extension<AuthContext>,
    Path(message_id): Path<i32>,
) -> Result<Json<ApiResponse<Value>>, StatusCode> {
    let repo = MessageRepository::new(state.db.clone());
    
    match repo.mark_as_read(message_id, auth.claims.user_id as i32).await {
        Ok(_) => Ok(Json(ApiResponse::success(serde_json::json!({"message": "標記為已讀成功"})))),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

async fn update_message(
    State(state): State<AppState>,
    Extension(auth): Extension<AuthContext>,
    Path(message_id): Path<i32>,
    Json(request): Json<CreateMessageRequest>,
) -> Result<Json<ApiResponse<MessageResponse>>, StatusCode> {
    // 臨時允許 admin 和 super_admin 更新訊息
    if !auth.is_super_admin() && !auth.is_admin() {
        return Err(StatusCode::FORBIDDEN);
    }

    let repo = MessageRepository::new(state.db.clone());

    match repo.update_message(message_id, request).await {
        Ok(message) => {
            let response = MessageResponse {
                id: message.id,
                title: message.title,
                content: message.content,
                message_type: message.message_type,
                status: message.status,
                target_audience: message.target_audience,
                priority: message.priority,
                starts_at: message.starts_at,
                ends_at: message.ends_at,
                created_by: message.created_by,
                created_at: message.created_at,
                updated_at: message.updated_at,
                is_read: false,
            };
            Ok(Json(ApiResponse::success(response)))
        }
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

async fn delete_message(
    State(state): State<AppState>,
    Extension(auth): Extension<AuthContext>,
    Path(message_id): Path<i32>,
) -> Result<Json<ApiResponse<Value>>, StatusCode> {
    // 臨時允許 admin 和 super_admin 刪除訊息
    if !auth.is_super_admin() && !auth.is_admin() {
        return Err(StatusCode::FORBIDDEN);
    }

    let repo = MessageRepository::new(state.db.clone());

    match repo.delete_message(message_id).await {
        Ok(_) => Ok(Json(ApiResponse::success(serde_json::json!({"message": "訊息刪除成功"})))),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

async fn update_message_status(
    State(state): State<AppState>,
    Extension(auth): Extension<AuthContext>,
    Path(message_id): Path<i32>,
    Json(status): Json<String>,
) -> Result<Json<ApiResponse<Value>>, StatusCode> {
    // 臨時允許 admin 和 super_admin 更新訊息狀態
    if !auth.is_super_admin() && !auth.is_admin() {
        return Err(StatusCode::FORBIDDEN);
    }

    let repo = MessageRepository::new(state.db.clone());

    match repo.update_message_status(message_id, &status).await {
        Ok(_) => Ok(Json(ApiResponse::success(serde_json::json!({"message": "狀態更新成功"})))),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}