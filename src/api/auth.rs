use axum::{
    extract::{State, Extension, Path},
    http::StatusCode,
    response::<PERSON><PERSON>,
    routing::{post, get, put},
    Router,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use crate::{
    services::auth::{LoginRequest, RegisterRequest, RefreshTokenRequest, LoginResponse},
    database::DbPool,
    config::Config,
    api::middleware::{C<PERSON>ms, AuthContext},
    models::User,
    api::response::ApiResponse,
};

#[derive(Debug, Serialize, Deserialize)]
pub struct ChangePasswordRequest {
    pub old_password: String,
    pub new_password: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateProfileRequest {
    pub pharmacy_name: Option<String>,
    pub phone: Option<String>,
    pub mobile: Option<String>,
    pub contact_person: Option<String>,
    pub institution_code: Option<String>,
    pub address: Option<String>,
    pub line_user_id: Option<String>,
    pub notification_email: Option<bool>,
    pub notification_line: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserProfileResponse {
    pub id: i64,
    pub username: String,
    pub email: String,
    pub pharmacy_name: String,
    pub phone: Option<String>,
    pub mobile: Option<String>,
    pub contact_person: Option<String>,
    pub institution_code: Option<String>,
    pub address: Option<String>,
    pub line_user_id: Option<String>,
    pub notification_email: bool,
    pub notification_line: bool,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

// 公開的認證路由（不需要認證）
pub fn public_auth_routes() -> Router<AppState> {
    Router::new()
        .route("/register", post(register))
        .route("/login", post(login))
        .route("/refresh", post(refresh_token))
}

// 受保護的認證路由（需要認證）
pub fn protected_auth_routes() -> Router<AppState> {
    Router::new()
        .route("/me", get(get_user_profile))  // /me 端點指向 get_user_profile
        .route("/profile", get(get_user_profile))
        .route("/profile", put(update_user_profile))  // 改為 PUT 方法
        .route("/change-password", post(change_password))
        .route("/logout", post(logout))
        .route("/sessions", get(get_user_sessions))
        .route("/sessions/:session_id", post(revoke_session))
}

// 為了向下相容，保留原來的函數
#[allow(dead_code)]
pub fn auth_routes() -> Router<AppState> {
    public_auth_routes()
}

#[derive(Clone)]
pub struct AppState {
    pub db: DbPool,
    pub config: Config,
    pub auth_service: Arc<dyn crate::services::auth::AuthService>,
    pub product_service: Arc<dyn crate::services::product::ProductService>,
    pub cart_service: Arc<dyn crate::services::cart::CartService>,
    pub order_service: Arc<dyn crate::services::order::OrderService>,
    #[allow(dead_code)]
    pub backup_service: Arc<dyn crate::services::backup::BackupService>,
    #[allow(dead_code)]
    pub notification_service: Arc<dyn crate::services::notification::NotificationService>,
    pub contact_service: Arc<dyn crate::services::contact::ContactService>,
}

async fn register(
    State(state): State<AppState>,
    Json(request): Json<RegisterRequest>,
) -> Result<Json<ApiResponse<User>>, (StatusCode, Json<ApiResponse<()>>)> {
    match state.auth_service.register(request).await {
        Ok(user) => {
            Ok(Json(ApiResponse {
                success: true,
                data: Some(user),
                message: None,
                error: None,
            }))
        }
        Err(e) => Err((StatusCode::BAD_REQUEST, Json(ApiResponse {
            success: false,
            data: None,
            message: None,
            error: Some(e.to_string()),
        }))),
    }
}

async fn login(
    State(state): State<AppState>,
    Json(request): Json<LoginRequest>,
) -> Result<Json<ApiResponse<LoginResponse>>, (StatusCode, Json<ApiResponse<()>>)> {
    match state.auth_service.login(request).await {
        Ok(login_response) => Ok(Json(ApiResponse {
            success: true,
            data: Some(login_response),
            message: None,
            error: None,
        })),
        Err(e) => Err((StatusCode::UNAUTHORIZED, Json(ApiResponse {
            success: false,
            data: None,
            message: None,
            error: Some(e.to_string()),
        }))),
    }
}

async fn refresh_token(
    State(state): State<AppState>,
    Json(request): Json<RefreshTokenRequest>,
) -> Result<Json<ApiResponse<LoginResponse>>, (StatusCode, Json<ApiResponse<()>>)> {
    match state.auth_service.refresh_token(request).await {
        Ok(login_response) => Ok(Json(ApiResponse {
            success: true,
            data: Some(login_response),
            message: None,
            error: None,
        })),
        Err(e) => Err((StatusCode::UNAUTHORIZED, Json(ApiResponse {
            success: false,
            data: None,
            message: None,
            error: Some(e.to_string()),
        }))),
    }
}

#[allow(dead_code)]
async fn get_current_user(
    Extension(claims): Extension<Claims>,
) -> Json<ApiResponse<Claims>> {
    Json(ApiResponse {
        success: true,
        data: Some(claims),
        message: None,
        error: None,
    })
}

async fn change_password(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
    Json(request): Json<ChangePasswordRequest>,
) -> Result<Json<ApiResponse<()>>, (StatusCode, Json<ApiResponse<()>>)> {
    match state.auth_service.change_password(claims.user_id, &request.old_password, &request.new_password).await {
        Ok(_) => Ok(Json(ApiResponse {
            success: true,
            data: Some(()),
            message: None,
            error: None,
        })),
        Err(e) => Err((StatusCode::BAD_REQUEST, Json(ApiResponse {
            success: false,
            data: None,
            message: None,
            error: Some(e.to_string()),
        }))),
    }
}

async fn logout(
    State(state): State<AppState>,
    Extension(auth_context): Extension<AuthContext>,
) -> Result<Json<ApiResponse<()>>, (StatusCode, Json<ApiResponse<()>>)> {
    match state.auth_service.logout(&auth_context.token).await {
        Ok(_) => Ok(Json(ApiResponse {
            success: true,
            data: Some(()),
            message: None,
            error: None,
        })),
        Err(e) => Err((StatusCode::BAD_REQUEST, Json(ApiResponse {
            success: false,
            data: None,
            message: None,
            error: Some(e.to_string()),
        }))),
    }
}

async fn get_user_sessions(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
) -> Result<Json<ApiResponse<Vec<crate::services::auth::SessionInfo>>>, (StatusCode, Json<ApiResponse<()>>)> {
    match state.auth_service.get_user_sessions(claims.user_id).await {
        Ok(sessions) => Ok(Json(ApiResponse {
            success: true,
            data: Some(sessions),
            message: None,
            error: None,
        })),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, Json(ApiResponse {
            success: false,
            data: None,
            message: None,
            error: Some(e.to_string()),
        }))),
    }
}

/// 取得使用者個人資料
async fn get_user_profile(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
) -> Result<Json<ApiResponse<UserProfileResponse>>, (StatusCode, Json<ApiResponse<()>>)> {
    match state.auth_service.get_user_by_id(claims.user_id).await {
        Ok(Some(user)) => {
            let profile = UserProfileResponse {
                id: user.id,
                username: user.username,
                email: user.email,
                pharmacy_name: user.pharmacy_name,
                phone: user.phone,
                mobile: user.mobile,
                contact_person: user.contact_person,
                institution_code: user.institution_code,
                address: user.address,
                line_user_id: user.line_user_id,
                notification_email: user.notification_email,
                notification_line: user.notification_line,
                created_at: user.created_at,
                updated_at: user.updated_at,
            };
            Ok(Json(ApiResponse {
                success: true,
                data: Some(profile),
                message: None,
                error: None,
            }))
        }
        Ok(None) => Err((StatusCode::NOT_FOUND, Json(ApiResponse {
            success: false,
            data: None,
            message: None,
            error: Some("User not found".to_string()),
        }))),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, Json(ApiResponse {
            success: false,
            data: None,
            message: None,
            error: Some(e.to_string()),
        }))),
    }
}

/// 更新使用者個人資料
async fn update_user_profile(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
    Json(request): Json<UpdateProfileRequest>,
) -> Result<Json<ApiResponse<UserProfileResponse>>, (StatusCode, Json<ApiResponse<()>>)> {
    match state.auth_service.update_user_profile(claims.user_id, request).await {
        Ok(user) => {
            let profile = UserProfileResponse {
                id: user.id,
                username: user.username,
                email: user.email,
                pharmacy_name: user.pharmacy_name,
                phone: user.phone,
                mobile: user.mobile,
                contact_person: user.contact_person,
                institution_code: user.institution_code,
                address: user.address,
                line_user_id: user.line_user_id,
                notification_email: user.notification_email,
                notification_line: user.notification_line,
                created_at: user.created_at,
                updated_at: user.updated_at,
            };
            Ok(Json(ApiResponse {
                success: true,
                data: Some(profile),
                message: None,
                error: None,
            }))
        }
        Err(e) => Err((StatusCode::BAD_REQUEST, Json(ApiResponse {
            success: false,
            data: None,
            message: None,
            error: Some(e.to_string()),
        }))),
    }
}

/// 撤銷特定會話
async fn revoke_session(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
    Path(session_id): Path<String>,
) -> Result<Json<ApiResponse<()>>, (StatusCode, Json<ApiResponse<()>>)> {
    match state.auth_service.revoke_session(claims.user_id, &session_id).await {
        Ok(_) => Ok(Json(ApiResponse {
            success: true,
            data: Some(()),
            message: None,
            error: None,
        })),
        Err(e) => Err((StatusCode::BAD_REQUEST, Json(ApiResponse {
            success: false,
            data: None,
            message: None,
            error: Some(e.to_string()),
        }))),
    }
}