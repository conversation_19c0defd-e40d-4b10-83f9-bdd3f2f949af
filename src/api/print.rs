use axum::{
    extract::{Query, Path, State},
    http::{StatusCode, HeaderValue},
    response::Response,
};
use serde::Deserialize;

use crate::{
    api::auth::AppState,
    api::middleware::Claims,
    error::AppError,
    services::print::{PrintService, PrintServiceImpl, PrintOptions, PrintFormat},
};

/// 列印查詢參數
#[derive(Debug, Deserialize)]
pub struct PrintQuery {
    pub include_prices: Option<bool>,
}

impl PrintQuery {
    fn to_print_options(&self) -> PrintOptions {
        PrintOptions {
            format: PrintFormat::Html,
            include_prices: self.include_prices.unwrap_or(true),
        }
    }
}

/// 列印訂單（僅管理者可用）
pub async fn print_order(
    State(state): State<AppState>,
    Path(order_id): Path<i64>,
    Query(query): Query<PrintQuery>,
    axum::Extension(claims): axum::Extension<Claims>,
) -> Result<Response, AppError> {
    // 檢查是否為管理者
    if !is_admin(&claims, &state).await? {
        return Err(AppError::Authorization("只有管理者可以列印訂單".to_string()));
    }

    let print_service = PrintServiceImpl::new(state.order_service.clone());
    let options = query.to_print_options();
    let result = print_service.print_order(order_id, options).await?;

    // 返回HTML響應
    Ok(Response::builder()
        .status(StatusCode::OK)
        .header("Content-Type", "text/html; charset=utf-8")
        .header(
            "Content-Disposition", 
            HeaderValue::from_str(&format!("inline; filename=\"{}\"", result.filename))
                .map_err(|_| AppError::Internal("Failed to set headers".to_string()))?
        )
        .body(result.content.into())
        .map_err(|_| AppError::Internal("Failed to create response".to_string()))?)
}

/// 檢查用戶是否為管理者
async fn is_admin(claims: &Claims, state: &AppState) -> Result<bool, AppError> {
    use crate::repositories::role::RoleRepository;
    use crate::repositories::role::PostgresRoleRepository;
    
    let role_repo = PostgresRoleRepository::new(state.db.clone());
    
    // 獲取用戶權限
    match role_repo.find_user_permissions(claims.user_id).await {
        Ok(Some(permissions)) => {
            // 檢查是否有管理者角色
            Ok(permissions.role_name == "admin" || permissions.role_name == "super_admin")
        }
        Ok(None) => Ok(false),
        Err(_) => Ok(false), // 如果查詢失敗，預設為非管理者
    }
}