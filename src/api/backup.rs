use axum::{
    extract::State,
    response::Json,
    routing::{get, post},
    Router,
};
use serde_json::{json, Value};
use crate::error::AppResult;
use crate::api::auth::AppState;
use crate::services::backup::{BackupService, BackupServiceImpl};

pub fn backup_routes() -> Router<AppState> {
    Router::new()
        .route("/create", post(create_backup))
        .route("/list", get(list_backups))
        .route("/cleanup", post(cleanup_backups))
        .route("/schedule", post(schedule_backup))
}

async fn create_backup(
    State(state): State<AppState>,
) -> AppResult<Json<Value>> {
    let backup_service = BackupServiceImpl::new(
        state.db.clone(),
        state.config.gcp.storage_bucket.clone(),
        state.config.gcp.credentials_path.clone(),
    ).await?;
    let backup_info = backup_service.create_backup().await?;
    
    Ok(Json(json!({
        "message": "Backup created successfully",
        "backup": backup_info
    })))
}

async fn list_backups(
    State(state): State<AppState>,
) -> AppResult<Json<Value>> {
    let backup_service = BackupServiceImpl::new(
        state.db.clone(),
        state.config.gcp.storage_bucket.clone(),
        state.config.gcp.credentials_path.clone(),
    ).await?;
    let backups = backup_service.list_backups().await?;
    
    Ok(Json(json!({
        "backups": backups,
        "total": backups.len()
    })))
}

async fn cleanup_backups(
    State(state): State<AppState>,
) -> AppResult<Json<Value>> {
    let backup_service = BackupServiceImpl::new(
        state.db.clone(),
        state.config.gcp.storage_bucket.clone(),
        state.config.gcp.credentials_path.clone(),
    ).await?;
    backup_service.cleanup_old_backups().await?;
    
    Ok(Json(json!({
        "message": "Old backups cleaned up successfully"
    })))
}

async fn schedule_backup(
    State(state): State<AppState>,
) -> AppResult<Json<Value>> {
    let backup_service = BackupServiceImpl::new(
        state.db.clone(),
        state.config.gcp.storage_bucket.clone(),
        state.config.gcp.credentials_path.clone(),
    ).await?;
    backup_service.schedule_daily_backup().await?;
    
    Ok(Json(json!({
        "message": "Daily backup scheduled successfully"
    })))
} 