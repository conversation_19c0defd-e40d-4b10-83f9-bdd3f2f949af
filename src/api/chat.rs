use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
    Extension,
};
use std::collections::HashMap;

use crate::{
    error::AppError,
    models::{
        chat::{
            ChatMessageResponse, SendMessageRequest, AdminReplyRequest, 
            UpdateMessageStatusRequest, ChatStats, ChatConversation
        },
        user::User,
    },
    api::response::ApiResponse,
    repositories::chat_simple::ChatRepository,
};

/// 檢查是否為管理員 (role_id = 1)
fn is_admin(user: &User) -> bool {
    user.role_id == 1
}

/// 用戶發送聊天訊息
pub async fn send_message(
    State(pool): State<sqlx::MySqlPool>,
    Extension(user): Extension<User>,
    Json(payload): Json<SendMessageRequest>,
) -> Result<Json<ApiResponse<ChatMessageResponse>>, (StatusCode, Json<ApiResponse<()>>)> {
    let chat_repo = ChatRepository::new(pool);

    // 驗證訊息類型
    if payload.message_type != "user" {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ApiResponse::error("無效的訊息類型")),
        ));
    }

    // 驗證訊息內容
    if payload.message.trim().is_empty() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ApiResponse::error("訊息內容不能為空")),
        ));
    }

    if payload.message.len() > 1000 {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ApiResponse::error("訊息內容過長")),
        ));
    }

    let user_name = user.pharmacy_name.clone().unwrap_or(user.username.clone());
    
    match chat_repo.save_user_message(&user.id.to_string(), &user_name, &payload.message).await {
        Ok(chat_message) => {
            let response = ChatMessageResponse::from(chat_message);
            Ok(Json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("儲存聊天訊息失敗: {:?}", e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::error("儲存訊息失敗")),
            ))
        }
    }
}

/// 獲取用戶的聊天記錄
pub async fn get_user_messages(
    State(pool): State<sqlx::MySqlPool>,
    Extension(user): Extension<User>,
    Query(params): Query<HashMap<String, String>>,
) -> Result<Json<ApiResponse<Vec<ChatMessageResponse>>>, (StatusCode, Json<ApiResponse<()>>)> {
    let chat_repo = ChatRepository::new(pool);

    // 允許管理員查看指定用戶的訊息，否則只能查看自己的
    let target_user_id = if is_admin(&user) {
        params.get("user_id").unwrap_or(&user.id.to_string()).clone()
    } else {
        user.id.to_string()
    };

    match chat_repo.get_user_messages(&target_user_id).await {
        Ok(messages) => {
            let responses: Vec<ChatMessageResponse> = messages
                .into_iter()
                .map(ChatMessageResponse::from)
                .collect();
            Ok(Json(ApiResponse::success(responses)))
        }
        Err(e) => {
            tracing::error!("載入聊天記錄失敗: {:?}", e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::error("載入聊天記錄失敗")),
            ))
        }
    }
}

/// 管理員獲取所有聊天記錄
pub async fn get_all_messages(
    State(pool): State<sqlx::MySqlPool>,
    Extension(user): Extension<User>,
) -> Result<Json<ApiResponse<Vec<ChatMessageResponse>>>, (StatusCode, Json<ApiResponse<()>>)> {
    // 檢查管理員權限
    if !is_admin(&user) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ApiResponse::error("權限不足")),
        ));
    }

    let chat_repo = ChatRepository::new(pool);

    match chat_repo.get_all_messages().await {
        Ok(messages) => {
            let responses: Vec<ChatMessageResponse> = messages
                .into_iter()
                .map(ChatMessageResponse::from)
                .collect();
            Ok(Json(ApiResponse::success(responses)))
        }
        Err(e) => {
            tracing::error!("載入所有聊天記錄失敗: {:?}", e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::error("載入聊天記錄失敗")),
            ))
        }
    }
}

/// 管理員發送回覆
pub async fn send_admin_reply(
    State(pool): State<sqlx::MySqlPool>,
    Extension(admin): Extension<User>,
    Json(payload): Json<AdminReplyRequest>,
) -> Result<Json<ApiResponse<ChatMessageResponse>>, (StatusCode, Json<ApiResponse<()>>)> {
    // 檢查管理員權限
    if !is_admin(&admin) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ApiResponse::error("權限不足")),
        ));
    }

    // 驗證訊息內容
    if payload.message.trim().is_empty() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ApiResponse::error("回覆內容不能為空")),
        ));
    }

    if payload.message.len() > 1000 {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ApiResponse::error("回覆內容過長")),
        ));
    }

    let chat_repo = ChatRepository::new(pool);
    let admin_name = admin.pharmacy_name.clone().unwrap_or(admin.username.clone());

    match chat_repo.save_admin_reply(
        &payload.user_id,
        &admin.id.to_string(),
        &admin_name,
        &payload.message,
    ).await {
        Ok(chat_message) => {
            let response = ChatMessageResponse::from(chat_message);
            Ok(Json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("儲存管理員回覆失敗: {:?}", e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::error("發送回覆失敗")),
            ))
        }
    }
}

/// 獲取聊天統計信息
pub async fn get_chat_stats(
    State(pool): State<sqlx::MySqlPool>,
    Extension(user): Extension<User>,
) -> Result<Json<ApiResponse<ChatStats>>, (StatusCode, Json<ApiResponse<()>>)> {
    // 檢查管理員權限
    if !is_admin(&user) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ApiResponse::error("權限不足")),
        ));
    }

    let chat_repo = ChatRepository::new(pool);

    match chat_repo.get_chat_stats().await {
        Ok(stats) => Ok(Json(ApiResponse::success(stats))),
        Err(e) => {
            tracing::error!("獲取聊天統計失敗: {:?}", e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::error("獲取統計信息失敗")),
            ))
        }
    }
}

/// 獲取聊天對話列表
pub async fn get_chat_conversations(
    State(pool): State<sqlx::MySqlPool>,
    Extension(user): Extension<User>,
) -> Result<Json<ApiResponse<Vec<ChatConversation>>>, (StatusCode, Json<ApiResponse<()>>)> {
    // 檢查管理員權限
    if !is_admin(&user) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ApiResponse::error("權限不足")),
        ));
    }

    let chat_repo = ChatRepository::new(pool);

    match chat_repo.get_chat_conversations().await {
        Ok(conversations) => Ok(Json(ApiResponse::success(conversations))),
        Err(e) => {
            tracing::error!("獲取聊天對話列表失敗: {:?}", e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::error("獲取對話列表失敗")),
            ))
        }
    }
}

/// 更新訊息狀態
pub async fn update_message_status(
    State(pool): State<sqlx::MySqlPool>,
    Extension(user): Extension<User>,
    Path(message_id): Path<i64>,
    Json(payload): Json<UpdateMessageStatusRequest>,
) -> Result<Json<ApiResponse<()>>, (StatusCode, Json<ApiResponse<()>>)> {
    // 檢查管理員權限
    if !is_admin(&user) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ApiResponse::error("權限不足")),
        ));
    }

    // 驗證狀態值
    if !["sent", "read", "resolved"].contains(&payload.status.as_str()) {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ApiResponse::error("無效的狀態值")),
        ));
    }

    let chat_repo = ChatRepository::new(pool);

    match chat_repo.update_message_status(message_id, &payload.status).await {
        Ok(_) => Ok(Json(ApiResponse::success(()))),
        Err(e) => {
            tracing::error!("更新訊息狀態失敗: {:?}", e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::error("更新狀態失敗")),
            ))
        }
    }
}

/// 標記用戶對話為已解決
pub async fn mark_conversation_resolved(
    State(pool): State<sqlx::MySqlPool>,
    Extension(user): Extension<User>,
    Path(user_id): Path<String>,
) -> Result<Json<ApiResponse<()>>, (StatusCode, Json<ApiResponse<()>>)> {
    // 檢查管理員權限
    if !is_admin(&user) {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ApiResponse::error("權限不足")),
        ));
    }

    let chat_repo = ChatRepository::new(pool);

    match chat_repo.mark_user_messages_resolved(&user_id).await {
        Ok(_) => Ok(Json(ApiResponse::success(()))),
        Err(e) => {
            tracing::error!("標記對話為已解決失敗: {:?}", e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::error("標記為已解決失敗")),
            ))
        }
    }
}
use axum::{
    routing::{get, post, put},
    Router,
};

use crate::api::auth::AppState;

/// 聊天功能路由
pub fn chat_routes() -> Router<AppState> {
    Router::new()
        // 用戶聊天功能
        .route("/chat/messages", post(send_message))
        .route("/chat/messages", get(get_user_messages))
        
        // 管理員聊天功能
        .route("/admin/chat/messages", get(get_all_messages))
        .route("/admin/chat/reply", post(send_admin_reply))
        .route("/admin/chat/stats", get(get_chat_stats))
        .route("/admin/chat/conversations", get(get_chat_conversations))
        .route("/admin/chat/messages/:message_id/status", put(update_message_status))
        .route("/admin/chat/conversations/:user_id/resolve", put(mark_conversation_resolved))
}