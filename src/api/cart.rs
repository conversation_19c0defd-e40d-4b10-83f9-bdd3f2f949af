use axum::{
    extract::{Path, State, Json as JsonExtractor, Extension},
    response::Json,
    routing::{get, post, put, delete},
    Router,
};
use serde_json::{json, Value};
use crate::models::{AddToCartRequest, UpdateCartItemRequest};
use crate::error::AppResult;
use crate::api::auth::AppState;
use crate::api::middleware::Claims;

pub fn cart_routes() -> Router<AppState> {
    Router::new()
        .route("/api/cart", get(get_cart))
        .route("/api/cart", post(add_to_cart))
        .route("/api/cart/summary", get(get_cart_summary))
        .route("/api/cart/validate", get(validate_cart_stock))
        .route("/api/cart/clear", delete(clear_cart))
        .route("/api/cart/items/:item_id", put(update_cart_item))
        .route("/api/cart/items/:item_id", delete(remove_cart_item))
}

/// 獲取購物車詳情
pub async fn get_cart(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
) -> AppResult<Json<Value>> {
    let user_id = claims.user_id;
    
    let cart_details = state.cart_service.get_cart(user_id).await?;
    
    Ok(Json(json!({
        "success": true,
        "data": {
            "cart": cart_details
        }
    })))
}

/// 添加商品到購物車
pub async fn add_to_cart(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
    JsonExtractor(request): JsonExtractor<AddToCartRequest>,
) -> AppResult<Json<Value>> {
    let user_id = claims.user_id;
    
    let cart_details = state.cart_service.add_to_cart(user_id, request).await?;
    
    Ok(Json(json!({
        "success": true,
        "message": "Item added to cart successfully",
        "data": {
            "cart": cart_details
        }
    })))
}

/// 更新購物車項目數量
async fn update_cart_item(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
    Path(item_id): Path<i64>,
    JsonExtractor(request): JsonExtractor<UpdateCartItemRequest>,
) -> AppResult<Json<Value>> {
    let user_id = claims.user_id;
    
    let cart_details = state.cart_service.update_cart_item(user_id, item_id, request).await?;
    
    Ok(Json(json!({
        "success": true,
        "message": "Cart item updated successfully",
        "data": {
            "cart": cart_details
        }
    })))
}

/// 從購物車移除項目
async fn remove_cart_item(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
    Path(item_id): Path<i64>,
) -> AppResult<Json<Value>> {
    let user_id = claims.user_id;
    
    let cart_details = state.cart_service.remove_from_cart(user_id, item_id).await?;
    
    Ok(Json(json!({
        "success": true,
        "message": "Item removed from cart successfully",
        "data": {
            "cart": cart_details
        }
    })))
}

/// 清空購物車
async fn clear_cart(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
) -> AppResult<Json<Value>> {
    let user_id = claims.user_id;
    
    let success = state.cart_service.clear_cart(user_id).await?;
    
    Ok(Json(json!({
        "success": success,
        "message": if success { "Cart cleared successfully" } else { "Cart not found" }
    })))
}

/// 獲取購物車摘要
async fn get_cart_summary(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
) -> AppResult<Json<Value>> {
    let user_id = claims.user_id;
    
    let summary = state.cart_service.get_cart_summary(user_id).await?;
    
    Ok(Json(json!({
        "success": true,
        "data": {
            "summary": summary
        }
    })))
}

/// 驗證購物車庫存
async fn validate_cart_stock(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
) -> AppResult<Json<Value>> {
    let user_id = claims.user_id;
    
    let validation_result = state.cart_service.validate_cart_stock(user_id).await?;
    
    Ok(Json(json!({
        "success": true,
        "data": {
            "validation": validation_result
        }
    })))
}