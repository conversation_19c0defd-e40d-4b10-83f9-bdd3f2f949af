use axum::{
    extract::{Path, Query, State, Extension},
    response::Json,
    routing::{get, post, put, delete},
    Router,
};
use serde::Deserialize;
use serde_json::{json, Value};
use tracing::info;

use crate::{
    api::{auth::AppState, middleware::Claims, response::ApiResponse},
    error::{AppResult, AppError},
    models::{CreatePromotionRequest, UpdatePromotionRequest, PromotionListResponse},
    services::promotion::{PromotionService, PromotionServiceImpl},
};

#[derive(Debug, Deserialize)]
pub struct PromotionQueryParams {
    page: Option<i64>,
    limit: Option<i64>,
    active_only: Option<bool>,
}

impl Default for PromotionQueryParams {
    fn default() -> Self {
        Self {
            page: Some(1),
            limit: Some(20),
            active_only: Some(false),
        }
    }
}

pub fn promotion_routes() -> Router<AppState> {
    Router::new()
        // 公開路由（需要認證但所有用戶都可訪問）
        .route("/", get(get_promotions))
        .route("/active", get(get_active_promotions))
        .route("/:id", get(get_promotion))
        .route("/:id/read", post(mark_promotion_as_read))
        .route("/unread-count", get(get_unread_count))
        // 管理員路由
        .route("/admin", post(create_promotion))
        .route("/admin", get(get_all_promotions_for_admin))
        .route("/admin/:id", put(update_promotion))
        .route("/admin/:id", delete(delete_promotion))
}

/// 獲取用戶的促銷訊息列表
async fn get_promotions(
    Query(params): Query<PromotionQueryParams>,
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
) -> AppResult<Json<ApiResponse<PromotionListResponse>>> {
    let params = PromotionQueryParams {
        page: params.page.or(Some(1)),
        limit: params.limit.or(Some(20)),
        active_only: params.active_only.or(Some(false)),
    };

    let promotion_service = PromotionServiceImpl::new(state.db.clone());

    let result = if params.active_only.unwrap_or(false) {
        promotion_service
            .get_active_promotions(claims.user_id, params.page.unwrap(), params.limit.unwrap())
            .await?
    } else {
        promotion_service
            .get_promotions_for_user(claims.user_id, params.page.unwrap(), params.limit.unwrap())
            .await?
    };

    info!(
        "用戶 {} 獲取促銷訊息列表：頁碼 {}, 限制 {}, 僅活躍 {}",
        claims.user_id,
        params.page.unwrap(),
        params.limit.unwrap(),
        params.active_only.unwrap_or(false)
    );

    Ok(Json(ApiResponse::success(result)))
}

/// 獲取活躍的促銷訊息
async fn get_active_promotions(
    Query(params): Query<PromotionQueryParams>,
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
) -> AppResult<Json<ApiResponse<PromotionListResponse>>> {
    let page = params.page.unwrap_or(1);
    let limit = params.limit.unwrap_or(20);

    let promotion_service = PromotionServiceImpl::new(state.db.clone());
    let result = promotion_service
        .get_active_promotions(claims.user_id, page, limit)
        .await?;

    info!("用戶 {} 獲取活躍促銷訊息", claims.user_id);

    Ok(Json(ApiResponse::success(result)))
}

/// 獲取單個促銷訊息
async fn get_promotion(
    Path(id): Path<i64>,
    State(state): State<AppState>,
    Extension(_claims): Extension<Claims>,
) -> AppResult<Json<ApiResponse<Value>>> {
    let promotion_service = PromotionServiceImpl::new(state.db.clone());
    
    if let Some(promotion) = promotion_service.get_promotion(id).await? {
        Ok(Json(ApiResponse::success(json!(promotion))))
    } else {
        Err(AppError::NotFound("促銷訊息不存在".to_string()))
    }
}

/// 標記促銷訊息為已讀
async fn mark_promotion_as_read(
    Path(id): Path<i64>,
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
) -> AppResult<Json<ApiResponse<Value>>> {
    let promotion_service = PromotionServiceImpl::new(state.db.clone());
    
    promotion_service.mark_as_read(id, claims.user_id).await?;
    
    info!("用戶 {} 標記促銷訊息 {} 為已讀", claims.user_id, id);

    Ok(Json(ApiResponse::success(json!({
        "message": "促銷訊息已標記為已讀"
    }))))
}

/// 獲取未讀促銷訊息數量
async fn get_unread_count(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
) -> AppResult<Json<ApiResponse<Value>>> {
    let promotion_service = PromotionServiceImpl::new(state.db.clone());
    let count = promotion_service.get_unread_count(claims.user_id).await?;

    Ok(Json(ApiResponse::success(json!({
        "unread_count": count
    }))))
}

/// 創建促銷訊息（管理員）
async fn create_promotion(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
    Json(request): Json<CreatePromotionRequest>,
) -> AppResult<Json<ApiResponse<Value>>> {
    let promotion_service = PromotionServiceImpl::new(state.db.clone());
    
    let promotion = promotion_service
        .create_promotion(request, claims.user_id)
        .await?;

    info!("管理員 {} 創建促銷訊息：ID {}", claims.user_id, promotion.id);

    Ok(Json(ApiResponse::success(json!(promotion))))
}

/// 獲取所有促銷訊息（管理員）
async fn get_all_promotions_for_admin(
    Query(params): Query<PromotionQueryParams>,
    State(state): State<AppState>,
    Extension(_claims): Extension<Claims>,
) -> AppResult<Json<ApiResponse<PromotionListResponse>>> {
    let page = params.page.unwrap_or(1);
    let limit = params.limit.unwrap_or(20);

    let promotion_service = PromotionServiceImpl::new(state.db.clone());
    let result = promotion_service.get_all_promotions(page, limit).await?;

    Ok(Json(ApiResponse::success(result)))
}

/// 更新促銷訊息（管理員）
async fn update_promotion(
    Path(id): Path<i64>,
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
    Json(request): Json<UpdatePromotionRequest>,
) -> AppResult<Json<ApiResponse<Value>>> {
    let promotion_service = PromotionServiceImpl::new(state.db.clone());
    
    let promotion = promotion_service
        .update_promotion(id, request, claims.user_id)
        .await?;

    info!("管理員 {} 更新促銷訊息：ID {}", claims.user_id, promotion.id);

    Ok(Json(ApiResponse::success(json!(promotion))))
}

/// 刪除促銷訊息（管理員）
async fn delete_promotion(
    Path(id): Path<i64>,
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
) -> AppResult<Json<ApiResponse<Value>>> {
    let promotion_service = PromotionServiceImpl::new(state.db.clone());
    
    let deleted = promotion_service.delete_promotion(id, claims.user_id).await?;

    if deleted {
        info!("管理員 {} 刪除促銷訊息：ID {}", claims.user_id, id);
        Ok(Json(ApiResponse::success(json!({
            "message": "促銷訊息刪除成功"
        }))))
    } else {
        Err(AppError::NotFound("促銷訊息不存在".to_string()))
    }
}