use axum::{
    extract::{Request, State},
    http::{header, StatusCode},
    middleware::Next,
    response::Response,
    Json,
};

use std::time::Instant;
use tracing::{info, warn, error};
use uuid::Uuid;

use crate::{
    api::auth::AppState,
    logging::structured_logging,
    repositories::role::{RoleRepository, PostgresRoleRepository},
    models::UserPermissions,
};

// 重新匯出 auth service 的 Claims 結構
pub use crate::services::auth::Claims;

#[derive(Debug, Clone)]
#[allow(dead_code)]
pub struct AuthContext {
    pub claims: Claims,
    pub token: String,
    pub permissions: Option<UserPermissions>,
}

#[allow(dead_code)]
impl AuthContext {
    pub fn has_permission(&self, permission: &str) -> bool {
        self.permissions
            .as_ref()
            .map(|p| p.has_permission(permission))
            .unwrap_or(false)
    }

    pub fn has_resource_action(&self, resource: &str, action: &str) -> bool {
        self.permissions
            .as_ref()
            .map(|p| p.has_resource_action(resource, action))
            .unwrap_or(false)
    }

    pub fn is_super_admin(&self) -> bool {
        // 簡化後不再有 super_admin 角色，所有管理員功能都歸到 admin
        self.is_admin()
    }

    pub fn is_admin(&self) -> bool {
        self.permissions
            .as_ref()
            .map(|p| p.is_admin())
            .unwrap_or(false)
    }

    pub fn can_manage_users(&self) -> bool {
        self.permissions
            .as_ref()
            .map(|p| p.can_manage_users())
            .unwrap_or(false)
    }

    pub fn can_manage_products(&self) -> bool {
        self.permissions
            .as_ref()
            .map(|p| p.can_manage_products())
            .unwrap_or(false)
    }

    pub fn can_view_all_orders(&self) -> bool {
        self.permissions
            .as_ref()
            .map(|p| p.can_view_all_orders())
            .unwrap_or(false)
    }
}

// AppState 定義已移至 auth.rs

pub async fn auth_middleware(
    State(state): State<AppState>,
    mut request: Request,
    next: Next,
) -> Result<Response, (StatusCode, String)> {
    // 從 Authorization header 提取 token
    let auth_header = request
        .headers()
        .get(header::AUTHORIZATION)
        .and_then(|auth_header| auth_header.to_str().ok())
        .and_then(|auth_str| {
            if auth_str.starts_with("Bearer ") {
                Some(auth_str[7..].to_string())
            } else {
                None
            }
        });

    let token = auth_header.ok_or_else(|| {
        (StatusCode::UNAUTHORIZED, "Missing authorization header".to_string())
    })?;

    // 使用 AuthService 驗證 token
    let claims = state.auth_service
        .validate_token(&token)
        .await
        .map_err(|_| (StatusCode::UNAUTHORIZED, "Invalid or expired token".to_string()))?;

    // 獲取用戶權限
    let role_repo = PostgresRoleRepository::new(state.db.clone());
    let user_permissions = role_repo
        .find_user_permissions(claims.user_id)
        .await
        .ok()
        .flatten();

    // 將 claims 和 token 加入到 request extensions 中
    let auth_context = AuthContext {
        claims: claims.clone(),
        token: token.clone(),
        permissions: user_permissions,
    };
    request.extensions_mut().insert(claims);
    request.extensions_mut().insert(auth_context);

    Ok(next.run(request).await)
}

#[allow(dead_code)]
pub async fn optional_auth_middleware(
    State(state): State<AppState>,
    mut request: Request,
    next: Next,
) -> Response {
    // 嘗試從 Authorization header 提取 token
    let auth_header = request
        .headers()
        .get(header::AUTHORIZATION)
        .and_then(|auth_header| auth_header.to_str().ok())
        .and_then(|auth_str| {
            if auth_str.starts_with("Bearer ") {
                Some(auth_str[7..].to_string())
            } else {
                None
            }
        });

    if let Some(token) = auth_header {
        // 使用 AuthService 嘗試驗證 token
        if let Ok(claims) = state.auth_service.validate_token(&token).await {
            // 獲取用戶權限
            let role_repo = PostgresRoleRepository::new(state.db.clone());
            let user_permissions = role_repo
                .find_user_permissions(claims.user_id)
                .await
                .ok()
                .flatten();

            // 將 claims 和 token 加入到 request extensions 中
            let auth_context = AuthContext {
                claims: claims.clone(),
                token: token.clone(),
                permissions: user_permissions,
            };
            request.extensions_mut().insert(claims);
            request.extensions_mut().insert(auth_context);
        }
    }

    next.run(request).await
}

#[allow(dead_code)]
pub async fn admin_middleware(
    request: Request,
    next: Next,
) -> Result<Response, (StatusCode, String)> {
    // 從 request extensions 中提取 claims
    let _claims = request
        .extensions()
        .get::<Claims>()
        .ok_or_else(|| (StatusCode::UNAUTHORIZED, "Authentication required".to_string()))?;

    // 檢查是否為管理員 (這裡需要根據實際需求調整)
    // 暫時允許所有認證使用者
    Ok(next.run(request).await)
}

/// CORS 中介軟體
#[allow(dead_code)]
pub async fn cors_middleware(
    request: Request,
    next: Next,
) -> Response {
    let mut response = next.run(request).await;
    
    // 設定 CORS headers
    response.headers_mut().insert(
        header::ACCESS_CONTROL_ALLOW_ORIGIN,
        header::HeaderValue::from_static("*"),
    );
    response.headers_mut().insert(
        header::ACCESS_CONTROL_ALLOW_METHODS,
        header::HeaderValue::from_static("GET, POST, PUT, DELETE, OPTIONS"),
    );
    response.headers_mut().insert(
        header::ACCESS_CONTROL_ALLOW_HEADERS,
        header::HeaderValue::from_static("Content-Type, Authorization"),
    );

    response
}

/// 增強的日誌中介軟體
pub async fn logging_middleware(
    State(_state): State<AppState>,
    request: Request,
    next: Next,
) -> Response {
    let start = Instant::now();
    let method = request.method().clone();
    let uri = request.uri().clone();
    let user_agent = request
        .headers()
        .get(header::USER_AGENT)
        .and_then(|h| h.to_str().ok())
        .unwrap_or("unknown");
    
    // 取得或建立請求 ID
    let request_id = request
        .extensions()
        .get::<String>()
        .cloned()
        .unwrap_or_else(|| Uuid::new_v4().to_string());
    
    // 取得使用者 ID（如果已認證）
    let user_id = request
        .extensions()
        .get::<Claims>()
        .map(|claims| claims.user_id);
    
    // 取得客戶端 IP
    let client_ip = request
        .headers()
        .get("x-forwarded-for")
        .and_then(|h| h.to_str().ok())
        .or_else(|| {
            request
                .headers()
                .get("x-real-ip")
                .and_then(|h| h.to_str().ok())
        })
        .unwrap_or("unknown");

    info!(
        method = %method,
        uri = %uri,
        user_agent = %user_agent,
        client_ip = %client_ip,
        user_id = ?user_id,
        request_id = %request_id,
        "HTTP 請求開始"
    );

    let response = next.run(request).await;
    let duration = start.elapsed();
    let status = response.status();

    // 使用結構化日誌記錄 API 請求
    structured_logging::log_api_request(
        method.as_str(),
        uri.path(),
        user_id,
        &request_id,
        Some(duration.as_millis() as u64),
        Some(status.as_u16()),
    );

    if status.is_success() {
        info!(
            method = %method,
            uri = %uri,
            status = %status,
            duration_ms = duration.as_millis(),
            user_id = ?user_id,
            request_id = %request_id,
            "HTTP 請求成功完成"
        );
    } else if status.is_client_error() {
        warn!(
            method = %method,
            uri = %uri,
            status = %status,
            duration_ms = duration.as_millis(),
            user_id = ?user_id,
            request_id = %request_id,
            "HTTP 請求客戶端錯誤"
        );
    } else {
        error!(
            method = %method,
            uri = %uri,
            status = %status,
            duration_ms = duration.as_millis(),
            user_id = ?user_id,
            request_id = %request_id,
            "HTTP 請求伺服器錯誤"
        );
    }

    response
}

/// 錯誤處理中介軟體
pub async fn error_handling_middleware(
    request: Request,
    next: Next,
) -> Response {
    let response = next.run(request).await;
    
    // 如果回應狀態是錯誤，記錄詳細資訊
    if response.status().is_server_error() {
        error!(
            status = %response.status(),
            "Server error occurred"
        );
    }
    
    response
}

/// 請求 ID 中介軟體
#[allow(dead_code)]
pub async fn request_id_middleware(
    mut request: Request,
    next: Next,
) -> Response {
    // 檢查是否已有請求 ID（從 header 或其他來源）
    let request_id = request
        .headers()
        .get("X-Request-ID")
        .and_then(|h| h.to_str().ok())
        .map(|s| s.to_string())
        .unwrap_or_else(|| Uuid::new_v4().to_string());
    
    // 將請求 ID 加入到 request extensions
    request.extensions_mut().insert(request_id.clone());
    
    let mut response = next.run(request).await;
    
    // 將請求 ID 加入到回應 header
    response.headers_mut().insert(
        "X-Request-ID",
        header::HeaderValue::from_str(&request_id).unwrap(),
    );
    
    response
}

/// 速率限制中介軟體（簡單實作）
#[allow(dead_code)]
pub async fn rate_limit_middleware(
    request: Request,
    next: Next,
) -> Result<Response, (StatusCode, Json<serde_json::Value>)> {
    // 這裡可以實作更複雜的速率限制邏輯
    // 目前只是一個佔位符
    
    // 檢查是否超過速率限制
    // let client_ip = get_client_ip(&request);
    // if is_rate_limited(&client_ip) {
    //     return Err((
    //         StatusCode::TOO_MANY_REQUESTS,
    //         Json(json!({
    //             "error": "Rate limit exceeded",
    //             "retry_after": 60
    //         }))
    //     ));
    // }
    
    Ok(next.run(request).await)
}

/// 權限檢查中間件 - 檢查特定權限
#[allow(dead_code)]
pub async fn require_permission_middleware(
    permission: String,
    request: Request,
    next: Next,
) -> Result<Response, (StatusCode, String)> {
    let auth_context = request
        .extensions()
        .get::<AuthContext>()
        .ok_or_else(|| (StatusCode::UNAUTHORIZED, "Authentication required".to_string()))?;

    if !auth_context.has_permission(&permission) {
        return Err((StatusCode::FORBIDDEN, "Insufficient permissions".to_string()));
    }

    Ok(next.run(request).await)
}

/// 資源和操作權限檢查中間件
#[allow(dead_code)]
pub async fn require_resource_action_middleware(
    resource: String,
    action: String,
    request: Request,
    next: Next,
) -> Result<Response, (StatusCode, String)> {
    let auth_context = request
        .extensions()
        .get::<AuthContext>()
        .ok_or_else(|| (StatusCode::UNAUTHORIZED, "Authentication required".to_string()))?;

    if !auth_context.has_resource_action(&resource, &action) {
        return Err((StatusCode::FORBIDDEN, "Insufficient permissions".to_string()));
    }

    Ok(next.run(request).await)
}

/// 管理員權限檢查中間件 (更新版本)
#[allow(dead_code)]
pub async fn require_admin_middleware(
    request: Request,
    next: Next,
) -> Result<Response, (StatusCode, String)> {
    let auth_context = request
        .extensions()
        .get::<AuthContext>()
        .ok_or_else(|| (StatusCode::UNAUTHORIZED, "Authentication required".to_string()))?;

    if !auth_context.is_admin() {
        return Err((StatusCode::FORBIDDEN, "Admin privileges required".to_string()));
    }

    Ok(next.run(request).await)
}

/// 超級管理員權限檢查中間件
#[allow(dead_code)]
pub async fn require_super_admin_middleware(
    request: Request,
    next: Next,
) -> Result<Response, (StatusCode, String)> {
    let auth_context = request
        .extensions()
        .get::<AuthContext>()
        .ok_or_else(|| (StatusCode::UNAUTHORIZED, "Authentication required".to_string()))?;

    if !auth_context.is_super_admin() {
        return Err((StatusCode::FORBIDDEN, "Super admin privileges required".to_string()));
    }

    Ok(next.run(request).await)
}

/// 產品管理權限檢查中間件
#[allow(dead_code)]
pub async fn require_product_management_middleware(
    request: Request,
    next: Next,
) -> Result<Response, (StatusCode, String)> {
    let auth_context = request
        .extensions()
        .get::<AuthContext>()
        .ok_or_else(|| (StatusCode::UNAUTHORIZED, "Authentication required".to_string()))?;

    if !auth_context.can_manage_products() {
        return Err((StatusCode::FORBIDDEN, "Product management privileges required".to_string()));
    }

    Ok(next.run(request).await)
}

/// 用戶管理權限檢查中間件
#[allow(dead_code)]
pub async fn require_user_management_middleware(
    request: Request,
    next: Next,
) -> Result<Response, (StatusCode, String)> {
    let auth_context = request
        .extensions()
        .get::<AuthContext>()
        .ok_or_else(|| (StatusCode::UNAUTHORIZED, "Authentication required".to_string()))?;

    if !auth_context.can_manage_users() {
        return Err((StatusCode::FORBIDDEN, "User management privileges required".to_string()));
    }

    Ok(next.run(request).await)
}

/// 查看所有訂單權限檢查中間件
pub async fn require_view_all_orders_middleware(
    request: Request,
    next: Next,
) -> Result<Response, (StatusCode, String)> {
    let auth_context = request
        .extensions()
        .get::<AuthContext>()
        .ok_or_else(|| (StatusCode::UNAUTHORIZED, "Authentication required".to_string()))?;

    // 檢查是否有查看所有訂單的權限
    if !auth_context.can_view_all_orders() {
        return Err((StatusCode::FORBIDDEN, "View all orders privileges required".to_string()));
    }

    Ok(next.run(request).await)
}