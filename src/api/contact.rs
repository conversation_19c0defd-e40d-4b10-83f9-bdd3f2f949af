use axum::{
    extract::{State, Extension, Json as JsonExtractor},
    response::Json,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use crate::error::AppResult;
use crate::api::auth::AppState;

pub fn contact_routes() -> Router<AppState> {
    Router::new()
        .route("/messages", get(get_contact_messages))
        .route("/messages", post(send_contact_message))
        .route("/admin/messages", get(get_all_messages_admin))
        .route("/admin/reply/:message_id", post(reply_to_message))
        .route("/test-message", post(create_test_message))
}

pub fn public_contact_routes() -> Router<AppState> {
    Router::new()
        .route("/debug", get(debug_messages))
        .route("/test-user-messages/:user_id", get(test_user_messages))
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SendContactMessageRequest {
    pub subject: String,
    pub message: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AdminReplyRequest {
    pub reply_message: String,
}

/// 獲取用戶的聯絡留言
async fn get_contact_messages(
    State(state): State<AppState>,
    Extension(claims): Extension<crate::api::middleware::Claims>,
) -> AppResult<Json<Value>> {
    let messages = state.contact_service.get_user_messages(claims.user_id).await?;
    
    Ok(Json(json!({
        "success": true,
        "data": {
            "messages": messages
        }
    })))
}

/// 發送聯絡留言
async fn send_contact_message(
    State(state): State<AppState>,
    Extension(claims): Extension<crate::api::middleware::Claims>,
    JsonExtractor(request): JsonExtractor<SendContactMessageRequest>,
) -> AppResult<Json<Value>> {
    if request.subject.trim().is_empty() {
        return Err(crate::error::AppError::Validation("主題不能為空".to_string()));
    }

    if request.message.trim().is_empty() {
        return Err(crate::error::AppError::Validation("留言內容不能為空".to_string()));
    }

    let message = state.contact_service.send_message(
        claims.user_id, 
        request.subject.trim().to_string(), 
        request.message.trim().to_string()
    ).await?;
    
    Ok(Json(json!({
        "success": true,
        "message": "留言發送成功",
        "data": {
            "message": message
        }
    })))
}

/// 管理員獲取所有聯絡留言
async fn get_all_messages_admin(
    State(state): State<AppState>,
    Extension(claims): Extension<crate::api::middleware::Claims>,
) -> AppResult<Json<Value>> {
    // 暫時註釋權限檢查來測試
    tracing::info!("用戶 {} (role_id: {:?}) 正在載入所有留言", claims.user_id, claims.role_id);

    let messages = match state.contact_service.get_all_messages().await {
        Ok(msgs) => {
            tracing::info!("成功載入 {} 條留言", msgs.len());
            msgs
        },
        Err(e) => {
            tracing::error!("載入留言失敗: {}", e);
            // 返回空列表而不是錯誤，讓前端正常顯示
            vec![]
        }
    };
    
    Ok(Json(json!({
        "success": true,
        "data": {
            "messages": messages
        }
    })))
}

/// 管理員回覆留言
async fn reply_to_message(
    State(state): State<AppState>,
    Extension(claims): Extension<crate::api::middleware::Claims>,
    axum::extract::Path(message_id): axum::extract::Path<i64>,
    JsonExtractor(request): JsonExtractor<AdminReplyRequest>,
) -> AppResult<Json<Value>> {
    // 檢查是否為管理員
    if claims.role_id.is_none() {
        return Err(crate::error::AppError::Authorization("需要管理員權限".to_string()));
    }

    if request.reply_message.trim().is_empty() {
        return Err(crate::error::AppError::Validation("回覆內容不能為空".to_string()));
    }

    let reply = state.contact_service.reply_to_message(
        message_id,
        claims.user_id,
        request.reply_message.trim().to_string()
    ).await?;
    
    Ok(Json(json!({
        "success": true,
        "message": "回覆發送成功",
        "data": {
            "reply": reply
        }
    })))
}

/// 創建測試留言（用於調試）
async fn create_test_message(
    State(state): State<AppState>,
    Extension(claims): Extension<crate::api::middleware::Claims>,
) -> AppResult<Json<Value>> {
    let message = state.contact_service.send_message(
        claims.user_id,
        "測試留言".to_string(),
        "這是一個測試留言，用於檢查管理員留言板功能是否正常工作。".to_string()
    ).await?;
    
    Ok(Json(json!({
        "success": true,
        "message": "測試留言已創建",
        "data": {
            "message": message
        }
    })))
}

/// 調試端點 - 直接查看所有留言（不需要認證）
async fn debug_messages(
    State(state): State<AppState>,
) -> AppResult<Json<Value>> {
    tracing::info!("調試端點被調用 - 直接查詢所有留言");
    
    let messages = state.contact_service.get_all_messages().await?;
    
    Ok(Json(json!({
        "success": true,
        "debug": true,
        "message": "調試查詢成功",
        "data": {
            "messages": messages,
            "count": messages.len()
        }
    })))
}

/// 測試特定用戶的留言（不需要認證）
async fn test_user_messages(
    State(state): State<AppState>,
    axum::extract::Path(user_id): axum::extract::Path<i64>,
) -> AppResult<Json<Value>> {
    tracing::info!("測試用戶 {} 的留言", user_id);
    
    let messages = state.contact_service.get_user_messages(user_id).await?;
    
    Ok(Json(json!({
        "success": true,
        "debug": true,
        "user_id": user_id,
        "message": "測試用戶留言查詢成功",
        "data": {
            "messages": messages,
            "count": messages.len()
        }
    })))
}