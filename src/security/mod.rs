pub mod data_masking;
pub mod sanitization;

use regex::Regex;
use lazy_static::lazy_static;
use serde::Serialize;
use std::collections::HashMap;

/// 敏感數據類型
#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, Hash)]
pub enum SensitiveDataType {
    Password,
    Email,
    Phone,
    CreditCard,
    IdNumber,
    Address,
    Custom(String),
}

/// 屏蔽策略
#[derive(Debug, Clone)]
pub enum MaskingStrategy {
    /// 完全隱藏
    Hide,
    /// 部分顯示（顯示前N個和後M個字符）
    Partial { show_start: usize, show_end: usize },
    /// 用指定字符替換
    Replace { replacement: char },
    /// 哈希處理
    Hash,
}

/// 敏感數據處理配置
#[derive(Debug, Clone)]
pub struct DataMaskingConfig {
    pub strategies: HashMap<SensitiveDataType, MaskingStrategy>,
    pub enabled: bool,
}

impl Default for DataMaskingConfig {
    fn default() -> Self {
        let mut strategies = HashMap::new();
        
        strategies.insert(SensitiveDataType::Password, MaskingStrategy::Hide);
        strategies.insert(SensitiveDataType::Email, MaskingStrategy::Partial { show_start: 2, show_end: 0 });
        strategies.insert(SensitiveDataType::Phone, MaskingStrategy::Partial { show_start: 3, show_end: 2 });
        strategies.insert(SensitiveDataType::CreditCard, MaskingStrategy::Partial { show_start: 4, show_end: 4 });
        strategies.insert(SensitiveDataType::IdNumber, MaskingStrategy::Partial { show_start: 2, show_end: 2 });
        strategies.insert(SensitiveDataType::Address, MaskingStrategy::Partial { show_start: 5, show_end: 0 });
        
        Self {
            strategies,
            enabled: true,
        }
    }
}

/// 敏感數據屏蔽器
#[derive(Debug)]
pub struct DataMasker {
    config: DataMaskingConfig,
    patterns: HashMap<SensitiveDataType, Regex>,
}

impl DataMasker {
    pub fn new(config: DataMaskingConfig) -> Self {
        let mut patterns = HashMap::new();
        
        // 編譯正則表達式模式
        patterns.insert(
            SensitiveDataType::Email,
            Regex::new(r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b").unwrap()
        );
        
        patterns.insert(
            SensitiveDataType::Phone,
            Regex::new(r"\b(?:\+?886[-\s]?)?(?:0?[2-9])[-\s]?\d{3,4}[-\s]?\d{4}\b").unwrap()
        );
        
        patterns.insert(
            SensitiveDataType::CreditCard,
            Regex::new(r"\b(?:\d{4}[-\s]?){3}\d{4}\b").unwrap()
        );
        
        patterns.insert(
            SensitiveDataType::IdNumber,
            Regex::new(r"\b[A-Z][12]\d{8}\b").unwrap() // 台灣身分證號
        );
        
        Self { config, patterns }
    }

    /// 屏蔽單個值
    pub fn mask_value(&self, value: &str, data_type: &SensitiveDataType) -> String {
        if !self.config.enabled {
            return value.to_string();
        }

        match self.config.strategies.get(data_type) {
            Some(strategy) => self.apply_strategy(value, strategy),
            None => value.to_string(),
        }
    }

    /// 應用屏蔽策略
    fn apply_strategy(&self, value: &str, strategy: &MaskingStrategy) -> String {
        match strategy {
            MaskingStrategy::Hide => "[HIDDEN]".to_string(),
            MaskingStrategy::Partial { show_start, show_end } => {
                self.mask_partial(value, *show_start, *show_end)
            }
            MaskingStrategy::Replace { replacement } => {
                replacement.to_string().repeat(value.len().min(8))
            }
            MaskingStrategy::Hash => {
                use std::collections::hash_map::DefaultHasher;
                use std::hash::{Hash, Hasher};
                
                let mut hasher = DefaultHasher::new();
                value.hash(&mut hasher);
                format!("hash_{:x}", hasher.finish())
            }
        }
    }

    /// 部分屏蔽
    fn mask_partial(&self, value: &str, show_start: usize, show_end: usize) -> String {
        let len = value.len();
        
        if len <= show_start + show_end || len <= 3 {
            return "*".repeat(len.min(8));
        }
        
        let start = &value[..show_start];
        let end = if show_end > 0 { &value[len - show_end..] } else { "" };
        let middle_len = len - show_start - show_end;
        let middle = "*".repeat(middle_len.min(8));
        
        format!("{}{}{}", start, middle, end)
    }

    /// 自動檢測並屏蔽文本中的敏感數據
    pub fn auto_mask_text(&self, text: &str) -> String {
        if !self.config.enabled {
            return text.to_string();
        }

        let mut result = text.to_string();
        
        for (data_type, pattern) in &self.patterns {
            if let Some(strategy) = self.config.strategies.get(data_type) {
                result = pattern.replace_all(&result, |caps: &regex::Captures| {
                    let matched = caps.get(0).unwrap().as_str();
                    self.apply_strategy(matched, strategy)
                }).to_string();
            }
        }
        
        result
    }

    /// 屏蔽結構化數據
    pub fn mask_json(&self, json_str: &str) -> Result<String, serde_json::Error> {
        let mut value: serde_json::Value = serde_json::from_str(json_str)?;
        self.mask_json_value(&mut value);
        serde_json::to_string(&value)
    }

    /// 遞歸屏蔽 JSON 值
    fn mask_json_value(&self, value: &mut serde_json::Value) {
        match value {
            serde_json::Value::Object(obj) => {
                for (key, val) in obj.iter_mut() {
                    if let Some(data_type) = self.detect_sensitive_field(key) {
                        if let serde_json::Value::String(s) = val {
                            *s = self.mask_value(s, &data_type);
                        }
                    } else {
                        self.mask_json_value(val);
                    }
                }
            }
            serde_json::Value::Array(arr) => {
                for item in arr.iter_mut() {
                    self.mask_json_value(item);
                }
            }
            _ => {} // 其他類型不處理
        }
    }

    /// 檢測敏感字段
    fn detect_sensitive_field(&self, field_name: &str) -> Option<SensitiveDataType> {
        let lower_field = field_name.to_lowercase();
        
        if lower_field.contains("password") || lower_field.contains("pwd") {
            Some(SensitiveDataType::Password)
        } else if lower_field.contains("email") {
            Some(SensitiveDataType::Email)
        } else if lower_field.contains("phone") || lower_field.contains("mobile") {
            Some(SensitiveDataType::Phone)
        } else if lower_field.contains("card") || lower_field.contains("credit") {
            Some(SensitiveDataType::CreditCard)
        } else if lower_field.contains("id") && (lower_field.contains("national") || lower_field.contains("identity")) {
            Some(SensitiveDataType::IdNumber)
        } else if lower_field.contains("address") || lower_field.contains("addr") {
            Some(SensitiveDataType::Address)
        } else {
            None
        }
    }
}

/// 敏感數據清理器（用於日誌和錯誤消息）
pub struct DataSanitizer {
    masker: DataMasker,
}

impl DataSanitizer {
    pub fn new(config: DataMaskingConfig) -> Self {
        Self {
            masker: DataMasker::new(config),
        }
    }

    /// 清理日誌消息
    pub fn sanitize_log_message(&self, message: &str) -> String {
        self.masker.auto_mask_text(message)
    }

    /// 清理錯誤消息
    pub fn sanitize_error_message(&self, error: &str) -> String {
        // 額外處理常見的錯誤模式
        let mut sanitized = self.masker.auto_mask_text(error);
        
        // 屏蔽 SQL 語句中的敏感信息
        sanitized = self.sanitize_sql_in_error(&sanitized);
        
        // 屏蔽文件路徑中的敏感信息
        sanitized = self.sanitize_file_paths(&sanitized);
        
        sanitized
    }

    /// 屏蔽 SQL 語句中的敏感信息
    fn sanitize_sql_in_error(&self, error: &str) -> String {
        lazy_static! {
            static ref SQL_VALUE_PATTERN: Regex = Regex::new(r"('([^'\\]|\\.)*')").unwrap();
        }
        
        SQL_VALUE_PATTERN.replace_all(error, "'[REDACTED]'").to_string()
    }

    /// 屏蔽文件路徑中的敏感信息
    fn sanitize_file_paths(&self, error: &str) -> String {
        lazy_static! {
            static ref FILE_PATH_PATTERN: Regex = Regex::new(r"/(?:home|Users)/[^/\s]+").unwrap();
        }
        
        FILE_PATH_PATTERN.replace_all(error, "/[USER]").to_string()
    }
}

/// 自定義序列化器，用於自動屏蔽敏感數據
#[derive(Debug, Serialize)]
pub struct MaskedData<T> {
    #[serde(flatten)]
    data: T,
    #[serde(skip)]
    masker: DataMasker,
}

impl<T> MaskedData<T>
where
    T: Serialize,
{
    pub fn new(data: T, config: DataMaskingConfig) -> Self {
        Self {
            data,
            masker: DataMasker::new(config),
        }
    }

    /// 序列化為屏蔽後的 JSON
    pub fn to_masked_json(&self) -> Result<String, serde_json::Error> {
        let json_str = serde_json::to_string(&self.data)?;
        self.masker.mask_json(&json_str)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_email_masking() {
        let config = DataMaskingConfig::default();
        let masker = DataMasker::new(config);
        
        let email = "<EMAIL>";
        let masked = masker.mask_value(email, &SensitiveDataType::Email);
        assert_eq!(masked, "us*************");
    }

    #[test]
    fn test_phone_masking() {
        let config = DataMaskingConfig::default();
        let masker = DataMasker::new(config);
        
        let phone = "0912345678";
        let masked = masker.mask_value(phone, &SensitiveDataType::Phone);
        assert_eq!(masked, "091*****78");
    }

    #[test]
    fn test_auto_mask_text() {
        let config = DataMaskingConfig::default();
        let masker = DataMasker::new(config);
        
        let text = "<NAME_EMAIL> or call 0912345678";
        let masked = masker.auto_mask_text(text);
        
        assert!(masked.contains("us*"));
        assert!(masked.contains("091*"));
    }

    #[test]
    fn test_json_masking() {
        let config = DataMaskingConfig::default();
        let masker = DataMasker::new(config);
        
        let json = r#"{"email": "<EMAIL>", "password": "secret123", "name": "John"}"#;
        let masked = masker.mask_json(json).unwrap();
        
        assert!(masked.contains("[HIDDEN]"));
        assert!(masked.contains("te*"));
        assert!(masked.contains("John")); // 非敏感數據應保持不變
    }

    #[test]
    fn test_data_sanitizer() {
        let config = DataMaskingConfig::default();
        let sanitizer = DataSanitizer::new(config);
        
        let error = "Database error with query: INSERT INTO users (email, password) VALUES ('<EMAIL>', 'secret123')";
        let sanitized = sanitizer.sanitize_error_message(error);
        
        assert!(sanitized.contains("[REDACTED]"));
    }
}