use regex::Regex;
use lazy_static::lazy_static;
use std::collections::HashSet;

/// 輸入清理和驗證
pub struct InputSanitizer {
    /// 允許的 HTML 標籤
    allowed_tags: HashSet<String>,
    /// 是否啟用嚴格模式
    strict_mode: bool,
}

impl Default for InputSanitizer {
    fn default() -> Self {
        let allowed_tags = HashSet::new();
        // 預設不允許任何 HTML 標籤
        
        Self {
            allowed_tags,
            strict_mode: true,
        }
    }
}

impl InputSanitizer {
    /// 創建新的輸入清理器
    pub fn new(allowed_tags: Vec<String>, strict_mode: bool) -> Self {
        Self {
            allowed_tags: allowed_tags.into_iter().collect(),
            strict_mode,
        }
    }

    /// 清理文本輸入，移除潛在的惡意內容
    pub fn sanitize_text(&self, input: &str) -> String {
        let mut sanitized = input.to_string();
        
        // 1. 移除或轉義 HTML 標籤
        sanitized = self.sanitize_html(&sanitized);
        
        // 2. 移除 SQL 注入嘗試
        sanitized = self.sanitize_sql_injection(&sanitized);
        
        // 3. 移除 XSS 嘗試
        sanitized = self.sanitize_xss(&sanitized);
        
        // 4. 正規化空白字符
        sanitized = self.normalize_whitespace(&sanitized);
        
        // 5. 限制長度
        if sanitized.len() > 10000 {
            sanitized.truncate(10000);
            sanitized.push_str("...[truncated]");
        }
        
        sanitized
    }

    /// 清理 HTML 內容
    fn sanitize_html(&self, input: &str) -> String {
        lazy_static! {
            static ref HTML_TAG_PATTERN: Regex = Regex::new(r"<[^>]*>").unwrap();
            static ref SCRIPT_PATTERN: Regex = Regex::new(r"(?i)<script[^>]*>.*?</script>").unwrap();
            static ref STYLE_PATTERN: Regex = Regex::new(r"(?i)<style[^>]*>.*?</style>").unwrap();
        }

        let mut sanitized = input.to_string();
        
        // 移除 script 和 style 標籤
        sanitized = SCRIPT_PATTERN.replace_all(&sanitized, "").to_string();
        sanitized = STYLE_PATTERN.replace_all(&sanitized, "").to_string();
        
        if self.strict_mode || self.allowed_tags.is_empty() {
            // 移除所有 HTML 標籤
            sanitized = HTML_TAG_PATTERN.replace_all(&sanitized, "").to_string();
        } else {
            // 只保留允許的標籤（簡化實現）
            // 在生產環境中，建議使用專門的 HTML 清理庫如 ammonia
            sanitized = self.filter_allowed_tags(&sanitized);
        }
        
        // HTML 實體編碼
        sanitized = html_escape::encode_text(&sanitized).to_string();
        
        sanitized
    }

    /// 過濾允許的標籤
    fn filter_allowed_tags(&self, input: &str) -> String {
        lazy_static! {
            static ref TAG_PATTERN: Regex = Regex::new(r"<(/?)([a-zA-Z][a-zA-Z0-9]*)[^>]*>").unwrap();
        }

        TAG_PATTERN.replace_all(input, |caps: &regex::Captures| {
            let tag_name = caps.get(2).unwrap().as_str().to_lowercase();
            if self.allowed_tags.contains(&tag_name) {
                caps.get(0).unwrap().as_str().to_string()
            } else {
                String::new()
            }
        }).to_string()
    }

    /// 清理 SQL 注入嘗試
    fn sanitize_sql_injection(&self, input: &str) -> String {
        lazy_static! {
            static ref SQL_KEYWORDS: Vec<Regex> = vec![
                Regex::new(r"(?i)\b(union\s+select|union\s+all\s+select)\b").unwrap(),
                Regex::new(r"(?i)\b(drop\s+table|drop\s+database)\b").unwrap(),
                Regex::new(r"(?i)\b(insert\s+into|update\s+set|delete\s+from)\b").unwrap(),
                Regex::new(r"(?i)\b(exec\s*\(|execute\s*\()\b").unwrap(),
                Regex::new(r"(?i)\b(script\s*:|javascript\s*:)\b").unwrap(),
                Regex::new(r"(?i)\b(or\s+1\s*=\s*1|and\s+1\s*=\s*1)\b").unwrap(),
                Regex::new(r"(?i)(\-\-|\#|\/\*|\*\/)").unwrap(),
            ];
        }

        let mut sanitized = input.to_string();
        
        for pattern in SQL_KEYWORDS.iter() {
            sanitized = pattern.replace_all(&sanitized, "[FILTERED]").to_string();
        }
        
        sanitized
    }

    /// 清理 XSS 嘗試
    fn sanitize_xss(&self, input: &str) -> String {
        lazy_static! {
            static ref XSS_PATTERNS: Vec<Regex> = vec![
                Regex::new(r"(?i)javascript:").unwrap(),
                Regex::new(r"(?i)vbscript:").unwrap(),
                Regex::new(r"(?i)data:text/html").unwrap(),
                Regex::new(r"(?i)on\w+\s*=").unwrap(), // onclick, onload, etc.
                Regex::new(r"(?i)expression\s*\(").unwrap(),
                Regex::new(r"(?i)url\s*\(\s*javascript:").unwrap(),
            ];
        }

        let mut sanitized = input.to_string();
        
        for pattern in XSS_PATTERNS.iter() {
            sanitized = pattern.replace_all(&sanitized, "[FILTERED]").to_string();
        }
        
        sanitized
    }

    /// 正規化空白字符
    fn normalize_whitespace(&self, input: &str) -> String {
        lazy_static! {
            static ref WHITESPACE_PATTERN: Regex = Regex::new(r"\s+").unwrap();
        }

        let trimmed = input.trim();
        WHITESPACE_PATTERN.replace_all(trimmed, " ").to_string()
    }

    /// 驗證輸入是否安全
    pub fn is_safe(&self, input: &str) -> bool {
        let sanitized = self.sanitize_text(input);
        sanitized == input
    }

    /// 檢查是否包含可疑模式
    pub fn contains_suspicious_patterns(&self, input: &str) -> Vec<String> {
        let mut suspicious = Vec::new();
        
        lazy_static! {
            static ref SUSPICIOUS_PATTERNS: Vec<(&'static str, Regex)> = vec![
                ("SQL_INJECTION", Regex::new(r"(?i)\b(union|select|drop|insert|update|delete|exec|script)\b").unwrap()),
                ("XSS_ATTEMPT", Regex::new(r"(?i)(<script|javascript:|on\w+\s*=)").unwrap()),
                ("PATH_TRAVERSAL", Regex::new(r"(\.\./|\.\.\\)").unwrap()),
                ("COMMAND_INJECTION", Regex::new(r"[;&|`$(){}]").unwrap()),
            ];
        }

        for (name, pattern) in SUSPICIOUS_PATTERNS.iter() {
            if pattern.is_match(input) {
                suspicious.push(name.to_string());
            }
        }
        
        suspicious
    }
}

/// 檔案名稱清理器
pub struct FilenameSanitizer;

impl FilenameSanitizer {
    /// 清理檔案名稱
    pub fn sanitize_filename(filename: &str) -> String {
        lazy_static! {
            static ref INVALID_CHARS: Regex = Regex::new(r#"[<>:"/\\|?*\x00-\x1f]"#).unwrap();
            static ref RESERVED_NAMES: HashSet<&'static str> = {
                let mut set = HashSet::new();
                set.insert("CON");
                set.insert("PRN");
                set.insert("AUX");
                set.insert("NUL");
                for i in 1..=9 {
                    set.insert(Box::leak(format!("COM{}", i).into_boxed_str()));
                    set.insert(Box::leak(format!("LPT{}", i).into_boxed_str()));
                }
                set
            };
        }

        let mut sanitized = filename.trim().to_string();
        
        // 移除無效字符
        sanitized = INVALID_CHARS.replace_all(&sanitized, "_").to_string();
        
        // 檢查保留名稱
        let name_without_ext = sanitized.split('.').next().unwrap_or(&sanitized).to_uppercase();
        if RESERVED_NAMES.contains(name_without_ext.as_str()) {
            sanitized = format!("file_{}", sanitized);
        }
        
        // 限制長度
        if sanitized.len() > 255 {
            // 保留檔案副檔名
            if let Some(dot_pos) = sanitized.rfind('.') {
                let ext = &sanitized[dot_pos..];
                let name = &sanitized[..dot_pos];
                let max_name_len = 255 - ext.len();
                sanitized = format!("{}{}", &name[..max_name_len.min(name.len())], ext);
            } else {
                sanitized.truncate(255);
            }
        }
        
        // 確保不以點結尾（Windows 限制）
        sanitized = sanitized.trim_end_matches('.').to_string();
        
        // 如果清理後為空，提供預設名稱
        if sanitized.is_empty() {
            sanitized = "untitled".to_string();
        }
        
        sanitized
    }

    /// 驗證檔案名稱是否安全
    pub fn is_safe_filename(filename: &str) -> bool {
        let sanitized = Self::sanitize_filename(filename);
        sanitized == filename
    }
}

/// URL 清理器
pub struct UrlSanitizer;

impl UrlSanitizer {
    /// 清理 URL
    pub fn sanitize_url(url: &str) -> Option<String> {
        // 只允許 HTTP 和 HTTPS 協議
        if !url.starts_with("http://") && !url.starts_with("https://") {
            return None;
        }
        
        // 解析 URL
        match url::Url::parse(url) {
            Ok(parsed_url) => {
                // 檢查是否為惡意域名（簡化檢查）
                if Self::is_malicious_domain(parsed_url.host_str().unwrap_or("")) {
                    return None;
                }
                
                Some(parsed_url.to_string())
            }
            Err(_) => None,
        }
    }

    /// 檢查是否為惡意域名
    fn is_malicious_domain(domain: &str) -> bool {
        lazy_static! {
            static ref BLOCKED_DOMAINS: HashSet<&'static str> = {
                let mut set = HashSet::new();
                set.insert("localhost");
                set.insert("127.0.0.1");
                set.insert("0.0.0.0");
                set.insert("::1");
                // 可以添加更多已知的惡意域名
                set
            };
        }

        BLOCKED_DOMAINS.contains(domain.to_lowercase().as_str())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_html_sanitization() {
        let sanitizer = InputSanitizer::default();
        
        let input = r#"<script>alert('xss')</script>Hello <b>World</b>"#;
        let sanitized = sanitizer.sanitize_text(input);
        
        assert!(!sanitized.contains("<script>"));
        assert!(!sanitized.contains("<b>"));
        assert!(sanitized.contains("Hello"));
        assert!(sanitized.contains("World"));
    }

    #[test]
    fn test_sql_injection_sanitization() {
        let sanitizer = InputSanitizer::default();
        
        let input = "'; DROP TABLE users; --";
        let sanitized = sanitizer.sanitize_text(input);
        
        assert!(sanitized.contains("[FILTERED]"));
        assert!(!sanitized.contains("DROP TABLE"));
    }

    #[test]
    fn test_xss_sanitization() {
        let sanitizer = InputSanitizer::default();
        
        let input = r#"<img src="x" onerror="alert('xss')">"#;
        let sanitized = sanitizer.sanitize_text(input);
        
        assert!(sanitized.contains("[FILTERED]"));
        assert!(!sanitized.contains("onerror"));
    }

    #[test]
    fn test_suspicious_pattern_detection() {
        let sanitizer = InputSanitizer::default();
        
        let input = "SELECT * FROM users WHERE id = 1";
        let suspicious = sanitizer.contains_suspicious_patterns(input);
        
        assert!(suspicious.contains(&"SQL_INJECTION".to_string()));
    }

    #[test]
    fn test_filename_sanitization() {
        assert_eq!(FilenameSanitizer::sanitize_filename("file<name>.txt"), "file_name_.txt");
        assert_eq!(FilenameSanitizer::sanitize_filename("CON.txt"), "file_CON.txt");
        assert_eq!(FilenameSanitizer::sanitize_filename(""), "untitled");
        
        let long_name = "a".repeat(300);
        let sanitized = FilenameSanitizer::sanitize_filename(&format!("{}.txt", long_name));
        assert!(sanitized.len() <= 255);
        assert!(sanitized.ends_with(".txt"));
    }

    #[test]
    fn test_url_sanitization() {
        assert!(UrlSanitizer::sanitize_url("https://example.com").is_some());
        assert!(UrlSanitizer::sanitize_url("http://example.com").is_some());
        assert!(UrlSanitizer::sanitize_url("ftp://example.com").is_none());
        assert!(UrlSanitizer::sanitize_url("javascript:alert(1)").is_none());
        assert!(UrlSanitizer::sanitize_url("https://localhost").is_none());
    }

    #[test]
    fn test_whitespace_normalization() {
        let sanitizer = InputSanitizer::default();
        
        let input = "  Hello    World  \n\t  ";
        let sanitized = sanitizer.sanitize_text(input);
        
        assert_eq!(sanitized, "Hello World");
    }
}