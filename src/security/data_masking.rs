use serde::{Deserialize, Serialize, Serializer, Deserializer};
use secrecy::{Secret, ExposeSecret};
use std::fmt;

/// 敏感字符串包裝器
#[derive(Clone)]
pub struct SensitiveString(Secret<String>);

impl SensitiveString {
    pub fn new(value: String) -> Self {
        Self(Secret::new(value))
    }

    pub fn expose_secret(&self) -> &str {
        self.0.expose_secret()
    }

    /// 創建屏蔽版本用於日誌
    pub fn masked(&self) -> String {
        let value = self.0.expose_secret();
        if value.len() <= 4 {
            "*".repeat(value.len())
        } else {
            format!("{}***{}", &value[..2], &value[value.len()-2..])
        }
    }
}

impl fmt::Debug for SensitiveString {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_tuple("SensitiveString")
            .field(&"[REDACTED]")
            .finish()
    }
}

impl fmt::Display for SensitiveString {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "[REDACTED]")
    }
}

impl<'de> Deserialize<'de> for SensitiveString {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        Ok(SensitiveString::new(s))
    }
}

impl Serialize for SensitiveString {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        // 序列化時始終返回屏蔽值
        "[REDACTED]".serialize(serializer)
    }
}

/// 可序列化的敏感數據，帶有自定義屏蔽邏輯
#[derive(Debug, Clone)]
pub struct MaskedField<T> {
    value: T,
    mask_fn: fn(&T) -> String,
}

impl<T> MaskedField<T> {
    pub fn new(value: T, mask_fn: fn(&T) -> String) -> Self {
        Self { value, mask_fn }
    }

    pub fn expose(&self) -> &T {
        &self.value
    }

    pub fn masked(&self) -> String {
        (self.mask_fn)(&self.value)
    }
}

impl<T> Serialize for MaskedField<T>
where
    T: Clone,
{
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        self.masked().serialize(serializer)
    }
}

impl<'de, T> Deserialize<'de> for MaskedField<T>
where
    T: Deserialize<'de>,
{
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: Deserializer<'de>,
    {
        let value = T::deserialize(deserializer)?;
        // 預設使用簡單屏蔽函數
        Ok(MaskedField::new(value, |_| "[MASKED]".to_string()))
    }
}

/// 預定義的屏蔽函數
pub mod mask_functions {

    pub fn mask_email(email: &str) -> String {
        if let Some(at_pos) = email.find('@') {
            let (local, domain) = email.split_at(at_pos);
            if local.len() <= 2 {
                format!("{}@{}", "*".repeat(local.len()), domain)
            } else {
                format!("{}{}@{}", &local[..2], "*".repeat(local.len() - 2), domain)
            }
        } else {
            "*".repeat(email.len().min(8))
        }
    }

    pub fn mask_phone(phone: &str) -> String {
        let digits: String = phone.chars().filter(|c| c.is_ascii_digit()).collect();
        if digits.len() >= 6 {
            format!("{}***{}", &digits[..3], &digits[digits.len()-2..])
        } else {
            "*".repeat(phone.len().min(8))
        }
    }

    pub fn mask_id_number(id: &str) -> String {
        if id.len() >= 4 {
            format!("{}***{}", &id[..2], &id[id.len()-2..])
        } else {
            "*".repeat(id.len())
        }
    }

    pub fn mask_address(address: &str) -> String {
        if address.len() <= 10 {
            "*".repeat(address.len().min(8))
        } else {
            format!("{}***", &address[..5])
        }
    }

    pub fn mask_credit_card(card: &str) -> String {
        let digits: String = card.chars().filter(|c| c.is_ascii_digit()).collect();
        if digits.len() >= 8 {
            format!("{} **** **** {}", &digits[..4], &digits[digits.len()-4..])
        } else {
            "**** **** **** ****".to_string()
        }
    }
}

/// 敏感數據類型別名
pub type SensitiveEmail = MaskedField<String>;
pub type SensitivePhone = MaskedField<String>;
pub type SensitiveIdNumber = MaskedField<String>;
pub type SensitiveAddress = MaskedField<String>;
pub type SensitiveCreditCard = MaskedField<String>;

impl SensitiveEmail {
    pub fn new_email(email: String) -> Self {
        MaskedField::new(email, |s| mask_functions::mask_email(s))
    }
}

impl SensitivePhone {
    pub fn new_phone(phone: String) -> Self {
        MaskedField::new(phone, |s| mask_functions::mask_phone(s))
    }
}

impl SensitiveIdNumber {
    pub fn new_id(id: String) -> Self {
        MaskedField::new(id, |s| mask_functions::mask_id_number(s))
    }
}

impl SensitiveAddress {
    pub fn new_address(address: String) -> Self {
        MaskedField::new(address, |s| mask_functions::mask_address(s))
    }
}

impl SensitiveCreditCard {
    pub fn new_card(card: String) -> Self {
        MaskedField::new(card, |s| mask_functions::mask_credit_card(s))
    }
}

/// 敏感用戶信息結構
#[derive(Debug, Serialize, Deserialize)]
pub struct SensitiveUserInfo {
    pub id: i64,
    pub username: String,
    #[serde(skip_serializing)]
    pub password_hash: SensitiveString,
    pub email: SensitiveEmail,
    pub phone: Option<SensitivePhone>,
    pub pharmacy_name: String,
    pub line_user_id: Option<String>,
}

impl SensitiveUserInfo {
    pub fn new(
        id: i64,
        username: String,
        password_hash: String,
        email: String,
        phone: Option<String>,
        pharmacy_name: String,
        line_user_id: Option<String>,
    ) -> Self {
        Self {
            id,
            username,
            password_hash: SensitiveString::new(password_hash),
            email: SensitiveEmail::new_email(email),
            phone: phone.map(SensitivePhone::new_phone),
            pharmacy_name,
            line_user_id,
        }
    }

    /// 獲取用於日誌的安全版本
    pub fn for_logging(&self) -> serde_json::Value {
        serde_json::json!({
            "id": self.id,
            "username": self.username,
            "email": self.email.masked(),
            "phone": self.phone.as_ref().map(|p| p.masked()),
            "pharmacy_name": self.pharmacy_name,
            "has_line_id": self.line_user_id.is_some()
        })
    }
}

/// 自動屏蔽 trait
pub trait AutoMask {
    /// 返回屏蔽版本用於序列化
    fn masked(&self) -> serde_json::Value;
    
    /// 返回用於日誌的安全版本
    fn for_logging(&self) -> serde_json::Value;
}

/// 為常見類型實現自動屏蔽
impl AutoMask for crate::models::user::User {
    fn masked(&self) -> serde_json::Value {
        serde_json::json!({
            "id": self.id,
            "username": self.username,
            "email": mask_functions::mask_email(&self.email),
            "pharmacy_name": self.pharmacy_name,
            "phone": self.phone.as_ref().map(|p| mask_functions::mask_phone(p)),
            "line_user_id": self.line_user_id.as_ref().map(|_| "[REDACTED]"),
            "notification_email": self.notification_email,
            "notification_line": self.notification_line,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        })
    }

    fn for_logging(&self) -> serde_json::Value {
        serde_json::json!({
            "id": self.id,
            "username": self.username,
            "email_domain": self.email.split('@').nth(1).unwrap_or("unknown"),
            "pharmacy_name": self.pharmacy_name,
            "has_phone": self.phone.is_some(),
            "has_line_id": self.line_user_id.is_some(),
            "notifications_enabled": self.notification_email || self.notification_line
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sensitive_string() {
        let sensitive = SensitiveString::new("password123".to_string());
        
        // Debug 應該顯示 [REDACTED]
        assert_eq!(format!("{:?}", sensitive), "SensitiveString(\"[REDACTED]\")");
        
        // Display 應該顯示 [REDACTED]
        assert_eq!(format!("{}", sensitive), "[REDACTED]");
        
        // 屏蔽版本
        assert_eq!(sensitive.masked(), "pa***23");
        
        // 實際值應該可以訪問
        assert_eq!(sensitive.expose_secret(), "password123");
    }

    #[test]
    fn test_email_masking() {
        let email = SensitiveEmail::new_email("<EMAIL>".to_string());
        assert_eq!(email.masked(), "us**@example.com");
    }

    #[test]
    fn test_phone_masking() {
        let phone = SensitivePhone::new_phone("0912-345-678".to_string());
        assert_eq!(phone.masked(), "091***78");
    }

    #[test]
    fn test_credit_card_masking() {
        let card = SensitiveCreditCard::new_card("1234-5678-9012-3456".to_string());
        assert_eq!(card.masked(), "1234 **** **** 3456");
    }

    #[test]
    fn test_serialization() {
        let sensitive = SensitiveString::new("secret".to_string());
        let json = serde_json::to_string(&sensitive).unwrap();
        assert_eq!(json, "\"[REDACTED]\"");
    }

    #[test]
    fn test_sensitive_user_info() {
        let user = SensitiveUserInfo::new(
            1,
            "testuser".to_string(),
            "hashedpassword".to_string(),
            "<EMAIL>".to_string(),
            Some("0912345678".to_string()),
            "Test Pharmacy".to_string(),
            Some("line123".to_string()),
        );

        let logging_info = user.for_logging();
        assert!(logging_info["email"].as_str().unwrap().contains("te**"));
        assert!(logging_info["phone"].as_str().unwrap().contains("091***"));
        assert_eq!(logging_info["has_line_id"], true);
    }
}