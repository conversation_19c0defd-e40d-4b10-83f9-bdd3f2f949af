use async_trait::async_trait;
use std::time::{Duration, Instant};
use tracing::{info, warn, error};

use crate::models::{EmailNotification, LineNotification, Order, User, OrderStatus};
use crate::error::AppResult;
use crate::config::Config;
use crate::logging::structured_logging;
use lettre::{
    transport::smtp::authentication::Credentials,
    Message, SmtpTransport, Transport,
    message::{header::ContentType, MultiPart, SinglePart},
};
use serde_json::json;
use reqwest::Client;
use tokio::time::sleep;
use chrono::Utc;
use serde::{Deserialize, Serialize};

pub struct NotificationServiceImpl {
    config: Config,
    http_client: Client,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum NotificationType {
    OrderConfirmation,
    OrderStatusUpdate,
    StockAlert,
    SystemMaintenance,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NotificationValidation {
    pub email_valid: bool,
    pub line_valid: bool,
    pub email_error: Option<String>,
    pub line_error: Option<String>,
}

impl NotificationServiceImpl {
    pub fn new(config: Config) -> Self {
        Self {
            config,
            http_client: Client::new(),
        }
    }

    async fn create_smtp_transport(&self) -> Result<SmtpTransport, crate::error::AppError> {
        let creds = Credentials::new(
            self.config.email.smtp_username.clone(),
            self.config.email.smtp_password.clone(),
        );

        let mailer = SmtpTransport::relay(&self.config.email.smtp_host)
            .map_err(|e| crate::error::AppError::Configuration(format!("SMTP relay error: {}", e)))?
            .credentials(creds)
            .port(self.config.email.smtp_port)
            .build();

        Ok(mailer)
    }

    async fn send_email_with_retry(&self, notification: EmailNotification, max_retries: u32) -> AppResult<()> {
        let start_time = Instant::now();
        let mut attempts = 0;
        let mut last_error = None;

        info!(
            recipient = %notification.to,
            subject = %notification.subject,
            max_retries = max_retries,
            "開始發送 Email 通知"
        );

        while attempts <= max_retries {
            let attempt_start = Instant::now();
            
            match self.send_email_internal(&notification).await {
                Ok(_) => {
                    let total_duration = start_time.elapsed();
                    let attempt_duration = attempt_start.elapsed();
                    
                    // 記錄通知發送成功
                    structured_logging::log_notification_sent(
                        "email",
                        &notification.to,
                        true,
                        total_duration.as_millis() as u64,
                        None,
                        None,
                    );
                    
                    if attempts > 0 {
                        info!(
                            recipient = %notification.to,
                            attempts = attempts + 1,
                            total_duration_ms = total_duration.as_millis(),
                            "Email 重試發送成功"
                        );
                    } else {
                        info!(
                            recipient = %notification.to,
                            duration_ms = attempt_duration.as_millis(),
                            "Email 發送成功"
                        );
                    }
                    return Ok(());
                }
                Err(e) => {
                    last_error = Some(e);
                    attempts += 1;
                    
                    if attempts <= max_retries {
                        let delay = Duration::from_secs(2_u64.pow(attempts - 1)); // Exponential backoff
                        warn!(
                            recipient = %notification.to,
                            attempt = attempts,
                            max_retries = max_retries,
                            retry_delay_secs = delay.as_secs(),
                            error = %last_error.as_ref().unwrap(),
                            "Email 發送失敗，準備重試"
                        );
                        sleep(delay).await;
                    }
                }
            }
        }

        let total_duration = start_time.elapsed();
        let final_error = last_error.unwrap();
        
        // 記錄通知發送失敗
        structured_logging::log_notification_sent(
            "email",
            &notification.to,
            false,
            total_duration.as_millis() as u64,
            Some(&final_error.to_string()),
            None,
        );
        
        error!(
            recipient = %notification.to,
            total_attempts = max_retries + 1,
            total_duration_ms = total_duration.as_millis(),
            error = %final_error,
            "Email 發送最終失敗"
        );
        
        Err(final_error)
    }

    async fn send_email_internal(&self, notification: &EmailNotification) -> AppResult<()> {
        let from_address = self.config.email.from_email.parse()
            .map_err(|e| crate::error::AppError::Configuration(format!("Invalid from email: {}", e)))?;
        
        let to_address = notification.to.parse()
            .map_err(|e| crate::error::AppError::Configuration(format!("Invalid to email: {}", e)))?;

        let email_builder = Message::builder()
            .from(from_address)
            .to(to_address)
            .subject(&notification.subject);

        let email = if let Some(html_body) = &notification.html_body {
            // Create multipart email with both text and HTML
            email_builder
                .multipart(
                    MultiPart::alternative()
                        .singlepart(
                            SinglePart::builder()
                                .header(ContentType::TEXT_PLAIN)
                                .body(notification.body.clone())
                        )
                        .singlepart(
                            SinglePart::builder()
                                .header(ContentType::TEXT_HTML)
                                .body(html_body.clone())
                        )
                )
                .map_err(|e| crate::error::AppError::Configuration(format!("Email build error: {}", e)))?
        } else {
            // Simple text email
            email_builder
                .body(notification.body.clone())
                .map_err(|e| crate::error::AppError::Configuration(format!("Email build error: {}", e)))?
        };

        let mailer = self.create_smtp_transport().await?;
        
        mailer.send(&email)
            .map_err(|e| {
                crate::error::AppError::Notification(format!("Email send error: {}", e))
            })?;

        Ok(())
    }

    fn generate_order_confirmation_html(&self, order: &Order, user: &User) -> String {
        format!(
            r#"
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>訂單確認</title>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background-color: #4CAF50; color: white; padding: 20px; text-align: center; }}
                    .content {{ padding: 20px; background-color: #f9f9f9; }}
                    .order-details {{ background-color: white; padding: 15px; margin: 10px 0; border-radius: 5px; }}
                    .footer {{ text-align: center; padding: 20px; color: #666; }}
                    .amount {{ font-size: 1.2em; font-weight: bold; color: #4CAF50; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>訂單確認</h1>
                    </div>
                    <div class="content">
                        <p>親愛的 <strong>{}</strong>，</p>
                        <p>感謝您的訂購！您的訂單已成功建立。</p>
                        
                        <div class="order-details">
                            <h3>訂單詳情</h3>
                            <p><strong>訂單編號：</strong>{}</p>
                            <p><strong>訂單總額：</strong><span class="amount">NT$ {}</span></p>
                            <p><strong>訂單狀態：</strong>待處理</p>
                            <p><strong>建立時間：</strong>{}</p>
                        </div>
                        
                        <p>我們會盡快處理您的訂單，並在狀態更新時通知您。</p>
                        <p>如有任何問題，請聯繫我們。</p>
                    </div>
                    <div class="footer">
                        <p>謝謝您的信任！</p>
                        <p>藥品中盤商採購系統</p>
                    </div>
                </div>
            </body>
            </html>
            "#,
            user.pharmacy_name,
            order.order_number,
            order.total_amount,
            order.created_at.format("%Y-%m-%d %H:%M:%S")
        )
    }

    fn generate_order_status_html(&self, order: &Order, user: &User, status_text: &str) -> String {
        format!(
            r#"
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>訂單狀態更新</title>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background-color: #2196F3; color: white; padding: 20px; text-align: center; }}
                    .content {{ padding: 20px; background-color: #f9f9f9; }}
                    .order-details {{ background-color: white; padding: 15px; margin: 10px 0; border-radius: 5px; }}
                    .footer {{ text-align: center; padding: 20px; color: #666; }}
                    .status {{ font-size: 1.2em; font-weight: bold; color: #2196F3; }}
                    .amount {{ font-size: 1.1em; font-weight: bold; color: #4CAF50; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>訂單狀態更新</h1>
                    </div>
                    <div class="content">
                        <p>親愛的 <strong>{}</strong>，</p>
                        <p>您的訂單狀態已更新。</p>
                        
                        <div class="order-details">
                            <h3>訂單資訊</h3>
                            <p><strong>訂單編號：</strong>{}</p>
                            <p><strong>新狀態：</strong><span class="status">{}</span></p>
                            <p><strong>訂單總額：</strong><span class="amount">NT$ {}</span></p>
                            <p><strong>更新時間：</strong>{}</p>
                        </div>
                        
                        <p>如有任何問題，請聯繫我們。</p>
                    </div>
                    <div class="footer">
                        <p>謝謝您的信任！</p>
                        <p>藥品中盤商採購系統</p>
                    </div>
                </div>
            </body>
            </html>
            "#,
            user.pharmacy_name,
            order.order_number,
            status_text,
            order.total_amount,
            chrono::Utc::now().format("%Y-%m-%d %H:%M:%S")
        )
    }

    async fn send_line_message_with_retry(&self, notification: LineNotification, max_retries: u32) -> AppResult<()> {
        let start_time = Instant::now();
        let mut attempts = 0;
        let mut last_error = None;

        info!(
            user_id = %notification.user_id,
            message_length = notification.message.len(),
            max_retries = max_retries,
            "開始發送 Line 通知"
        );

        while attempts <= max_retries {
            let attempt_start = Instant::now();
            
            match self.send_line_message_internal(&notification).await {
                Ok(_) => {
                    let total_duration = start_time.elapsed();
                    let attempt_duration = attempt_start.elapsed();
                    
                    // 記錄通知發送成功
                    structured_logging::log_notification_sent(
                        "line",
                        &notification.user_id,
                        true,
                        total_duration.as_millis() as u64,
                        None,
                        None,
                    );
                    
                    if attempts > 0 {
                        info!(
                            user_id = %notification.user_id,
                            attempts = attempts + 1,
                            total_duration_ms = total_duration.as_millis(),
                            "Line 重試發送成功"
                        );
                    } else {
                        info!(
                            user_id = %notification.user_id,
                            duration_ms = attempt_duration.as_millis(),
                            "Line 發送成功"
                        );
                    }
                    return Ok(());
                }
                Err(e) => {
                    last_error = Some(e);
                    attempts += 1;
                    
                    if attempts <= max_retries {
                        let delay = Duration::from_secs(2_u64.pow(attempts - 1)); // Exponential backoff
                        warn!(
                            user_id = %notification.user_id,
                            attempt = attempts,
                            max_retries = max_retries,
                            retry_delay_secs = delay.as_secs(),
                            error = %last_error.as_ref().unwrap(),
                            "Line 發送失敗，準備重試"
                        );
                        sleep(delay).await;
                    }
                }
            }
        }

        let total_duration = start_time.elapsed();
        let final_error = last_error.unwrap();
        
        // 記錄通知發送失敗
        structured_logging::log_notification_sent(
            "line",
            &notification.user_id,
            false,
            total_duration.as_millis() as u64,
            Some(&final_error.to_string()),
            None,
        );
        
        error!(
            user_id = %notification.user_id,
            total_attempts = max_retries + 1,
            total_duration_ms = total_duration.as_millis(),
            error = %final_error,
            "Line 發送最終失敗"
        );
        
        Err(final_error)
    }

    async fn send_line_message_internal(&self, notification: &LineNotification) -> AppResult<()> {
        // Validate message length (Line has a 5000 character limit)
        if notification.message.len() > 5000 {
            return Err(crate::error::AppError::Validation(
                "Line message exceeds 5000 character limit".to_string()
            ));
        }

        let message = json!({
            "to": notification.user_id,
            "messages": [
                {
                    "type": "text",
                    "text": notification.message
                }
            ]
        });

        let response = self.http_client
            .post("https://api.line.me/v2/bot/message/push")
            .header("Authorization", format!("Bearer {}", self.config.line.channel_access_token))
            .header("Content-Type", "application/json")
            .json(&message)
            .timeout(Duration::from_secs(30))
            .send()
            .await
            .map_err(|e| {
                crate::error::AppError::Notification(format!("Line API request error: {}", e))
            })?;

        let status = response.status();
        if !status.is_success() {
            let error_text = response.text().await.unwrap_or_default();
            
            // Handle specific Line API error codes
            let error_message = match status.as_u16() {
                400 => format!("Bad Request - Invalid message format: {}", error_text),
                401 => "Unauthorized - Invalid channel access token".to_string(),
                403 => "Forbidden - User blocked the bot or invalid user ID".to_string(),
                429 => "Rate limit exceeded - Too many requests".to_string(),
                500 => "Line server error - Please try again later".to_string(),
                _ => format!("Line API error ({}): {}", status, error_text),
            };
            
            tracing::error!("Line API error: {}", error_message);
            return Err(crate::error::AppError::Notification(error_message));
        }

        Ok(())
    }

    fn format_line_order_confirmation(&self, order: &Order, user: &User) -> String {
        format!(
            "🎉 訂單確認通知\n\n📋 訂單編號：{}\n🏥 藥局：{}\n💰 總額：NT$ {}\n📅 建立時間：{}\n📊 狀態：待處理\n\n我們會盡快處理您的訂單！\n如有問題請聯繫我們 📞",
            order.order_number,
            user.pharmacy_name,
            order.total_amount,
            order.created_at.format("%Y-%m-%d %H:%M")
        )
    }

    fn format_line_order_status_update(&self, order: &Order, user: &User, status_text: &str) -> String {
        let status_emoji = match order.status {
            OrderStatus::待處理 => "⏳",
            OrderStatus::揀貨中 => "🔄",
            OrderStatus::已出貨 => "📦",
        };

        format!(
            "{} 訂單狀態更新\n\n📋 訂單編號：{}\n🏥 藥局：{}\n📊 新狀態：{}\n💰 總額：NT$ {}\n🕐 更新時間：{}\n\n感謝您的耐心等候！",
            status_emoji,
            order.order_number,
            user.pharmacy_name,
            status_text,
            order.total_amount,
            Utc::now().format("%Y-%m-%d %H:%M")
        )
    }
}

#[async_trait]
impl NotificationService for NotificationServiceImpl {
    async fn send_email(&self, notification: EmailNotification) -> AppResult<()> {
        self.send_email_with_retry(notification, 3).await
    }

    async fn send_line_message(&self, notification: LineNotification) -> AppResult<()> {
        self.send_line_message_with_retry(notification, 3).await
    }

    async fn send_order_confirmation(&self, order: &Order, user: &User) -> AppResult<()> {
        // 發送 Email 確認
        if user.notification_email {
            let email_notification = self.format_order_confirmation_email(order, user).await?;
            self.send_email(email_notification).await?;
        }

        // 發送 Line 確認
        if user.notification_line && user.line_user_id.is_some() {
            let line_notification = self.format_order_confirmation_line(order, user).await?;
            self.send_line_message(line_notification).await?;
        }

        Ok(())
    }

    async fn send_order_status_update(&self, order: &Order, user: &User) -> AppResult<()> {
        let status_text = match order.status {
            OrderStatus::待處理 => "待處理",
            OrderStatus::揀貨中 => "揀貨中",
            OrderStatus::已出貨 => "已出貨",
        };

        // Email 狀態更新
        if user.notification_email {
            let text_body = format!(
                "親愛的 {}，\n\n您的訂單 {} 狀態已更新為：{}\n\n訂單總額：NT$ {}\n更新時間：{}\n\n如有任何問題，請聯繫我們。\n\n謝謝！\n\n藥品中盤商採購系統",
                user.pharmacy_name,
                order.order_number,
                status_text,
                order.total_amount,
                chrono::Utc::now().format("%Y-%m-%d %H:%M:%S")
            );

            let html_body = self.generate_order_status_html(order, user, status_text);

            let email_notification = EmailNotification {
                to: user.email.clone(),
                subject: format!("訂單狀態更新 - {}", order.order_number),
                body: text_body,
                html_body: Some(html_body),
            };
            self.send_email(email_notification).await?;
        }

        // Line 狀態更新
        if user.notification_line && user.line_user_id.is_some() {
            let message = self.format_line_order_status_update(order, user, status_text);
            
            let line_notification = LineNotification {
                user_id: user.line_user_id.clone().unwrap(),
                message,
            };
            self.send_line_message(line_notification).await?;
        }

        Ok(())
    }

    async fn format_order_confirmation_email(&self, order: &Order, user: &User) -> AppResult<EmailNotification> {
        let text_body = format!(
            "親愛的 {}，\n\n感謝您的訂購！您的訂單已成功建立。\n\n訂單詳情：\n訂單編號：{}\n訂單總額：NT$ {}\n訂單狀態：待處理\n建立時間：{}\n\n我們會盡快處理您的訂單，並在狀態更新時通知您。\n\n如有任何問題，請聯繫我們。\n\n謝謝！\n\n藥品中盤商採購系統",
            user.pharmacy_name,
            order.order_number,
            order.total_amount,
            order.created_at.format("%Y-%m-%d %H:%M:%S")
        );

        let html_body = self.generate_order_confirmation_html(order, user);

        let email_notification = EmailNotification {
            to: user.email.clone(),
            subject: format!("訂單確認 - {}", order.order_number),
            body: text_body,
            html_body: Some(html_body),
        };

        Ok(email_notification)
    }

    async fn format_order_confirmation_line(&self, order: &Order, user: &User) -> AppResult<LineNotification> {
        let message = self.format_line_order_confirmation(order, user);
        
        let line_notification = LineNotification {
            user_id: user.line_user_id.clone().unwrap(),
            message,
        };

        Ok(line_notification)
    }

    async fn send_notification_with_preferences(&self, order: &Order, user: &User, notification_type: NotificationType) -> AppResult<()> {
        let mut errors = Vec::new();

        match notification_type {
            NotificationType::OrderConfirmation => {
                // 發送 Email 確認（如果用戶啟用）
                if user.notification_email {
                    match self.format_order_confirmation_email(order, user).await {
                        Ok(email_notification) => {
                            if let Err(e) = self.send_email(email_notification).await {
                                tracing::error!("Failed to send order confirmation email: {}", e);
                                errors.push(format!("Email: {}", e));
                            }
                        }
                        Err(e) => {
                            tracing::error!("Failed to format order confirmation email: {}", e);
                            errors.push(format!("Email formatting: {}", e));
                        }
                    }
                }

                // 發送 Line 確認（如果用戶啟用且有 Line ID）
                if user.notification_line && user.line_user_id.is_some() {
                    match self.format_order_confirmation_line(order, user).await {
                        Ok(line_notification) => {
                            if let Err(e) = self.send_line_message(line_notification).await {
                                tracing::error!("Failed to send order confirmation Line message: {}", e);
                                errors.push(format!("Line: {}", e));
                            }
                        }
                        Err(e) => {
                            tracing::error!("Failed to format order confirmation Line message: {}", e);
                            errors.push(format!("Line formatting: {}", e));
                        }
                    }
                }
            }
            NotificationType::OrderStatusUpdate => {
                if let Err(e) = self.send_order_status_update(order, user).await {
                    tracing::error!("Failed to send order status update: {}", e);
                    errors.push(format!("Status update: {}", e));
                }
            }
            _ => {
                return Err(crate::error::AppError::Notification(
                    "Unsupported notification type".to_string()
                ));
            }
        }

        // 如果所有通知都失敗，回傳錯誤
        if !errors.is_empty() && 
           (!user.notification_email || errors.iter().any(|e| e.starts_with("Email"))) &&
           (!user.notification_line || !user.line_user_id.is_some() || errors.iter().any(|e| e.starts_with("Line"))) {
            return Err(crate::error::AppError::Notification(
                format!("All notifications failed: {}", errors.join(", "))
            ));
        }

        // 如果至少有一個通知成功，記錄部分失敗但不回傳錯誤
        if !errors.is_empty() {
            tracing::warn!("Some notifications failed: {}", errors.join(", "));
        }

        Ok(())
    }

    async fn validate_notification_setup(&self, user: &User) -> AppResult<NotificationValidation> {
        let mut validation = NotificationValidation {
            email_valid: false,
            line_valid: false,
            email_error: None,
            line_error: None,
        };

        // 驗證 Email 設定
        if user.notification_email {
            // 檢查 Email 格式
            if user.email.contains('@') && user.email.contains('.') {
                // 嘗試建立 SMTP 連線來驗證設定
                match self.create_smtp_transport().await {
                    Ok(_) => validation.email_valid = true,
                    Err(e) => validation.email_error = Some(format!("SMTP configuration error: {}", e)),
                }
            } else {
                validation.email_error = Some("Invalid email format".to_string());
            }
        } else {
            validation.email_error = Some("Email notifications disabled".to_string());
        }

        // 驗證 Line 設定
        if user.notification_line {
            if let Some(ref line_user_id) = user.line_user_id {
                if !line_user_id.is_empty() {
                    // 檢查 Line 設定是否有效
                    if !self.config.line.channel_access_token.is_empty() {
                        validation.line_valid = true;
                    } else {
                        validation.line_error = Some("Line channel access token not configured".to_string());
                    }
                } else {
                    validation.line_error = Some("Line user ID is empty".to_string());
                }
            } else {
                validation.line_error = Some("Line user ID not set".to_string());
            }
        } else {
            validation.line_error = Some("Line notifications disabled".to_string());
        }

        Ok(validation)
    }
}

#[async_trait]
pub trait NotificationService: Send + Sync {
    async fn send_email(&self, notification: EmailNotification) -> AppResult<()>;
    async fn send_line_message(&self, notification: LineNotification) -> AppResult<()>;
    #[allow(dead_code)]
    async fn send_order_confirmation(&self, order: &Order, user: &User) -> AppResult<()>;
    async fn send_order_status_update(&self, order: &Order, user: &User) -> AppResult<()>;
    async fn format_order_confirmation_email(&self, order: &Order, user: &User) -> AppResult<EmailNotification>;
    async fn format_order_confirmation_line(&self, order: &Order, user: &User) -> AppResult<LineNotification>;
    #[allow(dead_code)]
    async fn send_notification_with_preferences(&self, order: &Order, user: &User, notification_type: NotificationType) -> AppResult<()>;
    #[allow(dead_code)]
    async fn validate_notification_setup(&self, user: &User) -> AppResult<NotificationValidation>;
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::{Config, EmailConfig, LineConfig, GcpConfig};
    use crate::models::{User, Order, OrderStatus};
    use chrono::Utc;
    use rust_decimal::Decimal;
    use std::str::FromStr;

    fn create_test_config() -> Config {
        Config {
            database_url: "postgresql://postgres:password@localhost:5432/test_notification_db".to_string(),
            jwt_secret: "test_secret".to_string(),
            server_port: 8080,
            email: EmailConfig {
                smtp_host: "smtp.gmail.com".to_string(),
                smtp_port: 587,
                smtp_username: "<EMAIL>".to_string(),
                smtp_password: "test_password".to_string(),
                from_email: "<EMAIL>".to_string(),
            },
            line: LineConfig {
                channel_access_token: "test_token".to_string(),
                channel_secret: "test_secret".to_string(),
            },
            gcp: GcpConfig {
                project_id: "test_project".to_string(),
                storage_bucket: "test_bucket".to_string(),
                credentials_path: None,
            },
        }
    }

    fn create_test_user() -> User {
        User {
            id: 1,
            username: "testuser".to_string(),
            email: "<EMAIL>".to_string(),
            password_hash: "hashed_password".to_string(),
            pharmacy_name: "Test Pharmacy".to_string(),
            phone: Some("0912345678".to_string()),
            line_user_id: Some("line_user_123".to_string()),
            notification_email: true,
            notification_line: true,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    fn create_test_order() -> Order {
        Order {
            id: 1,
            order_number: "ORD-20240101-001".to_string(),
            user_id: 1,
            status: OrderStatus::Pending,
            total_amount: Decimal::from_str("1500.00").unwrap(),
            notes: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    #[tokio::test]
    async fn test_format_order_confirmation_email() {
        let config = create_test_config();
        let service = NotificationServiceImpl::new(config);
        let user = create_test_user();
        let order = create_test_order();

        let result = service.format_order_confirmation_email(&order, &user).await;
        assert!(result.is_ok());

        let email = result.unwrap();
        assert_eq!(email.to, user.email);
        assert!(email.subject.contains(&order.order_number));
        assert!(email.body.contains(&user.pharmacy_name));
        assert!(email.body.contains(&order.total_amount.to_string()));
        assert!(email.html_body.is_some());
    }

    #[tokio::test]
    async fn test_format_order_confirmation_line() {
        let config = create_test_config();
        let service = NotificationServiceImpl::new(config);
        let user = create_test_user();
        let order = create_test_order();

        let result = service.format_order_confirmation_line(&order, &user).await;
        assert!(result.is_ok());

        let line_notification = result.unwrap();
        assert_eq!(line_notification.user_id, user.line_user_id.unwrap());
        assert!(line_notification.message.contains(&order.order_number));
        assert!(line_notification.message.contains(&user.pharmacy_name));
        assert!(line_notification.message.contains(&order.total_amount.to_string()));
    }

    #[test]
    fn test_format_line_order_confirmation() {
        let config = create_test_config();
        let service = NotificationServiceImpl::new(config);
        let user = create_test_user();
        let order = create_test_order();

        let message = service.format_line_order_confirmation(&order, &user);
        
        assert!(message.contains("🎉 訂單確認通知"));
        assert!(message.contains(&order.order_number));
        assert!(message.contains(&user.pharmacy_name));
        assert!(message.contains(&order.total_amount.to_string()));
        assert!(message.contains("待處理"));
    }

    #[test]
    fn test_format_line_order_status_update() {
        let config = create_test_config();
        let service = NotificationServiceImpl::new(config);
        let user = create_test_user();
        let mut order = create_test_order();
        order.status = OrderStatus::Confirmed;

        let message = service.format_line_order_status_update(&order, &user, "已確認");
        
        assert!(message.contains("✅ 訂單狀態更新"));
        assert!(message.contains(&order.order_number));
        assert!(message.contains(&user.pharmacy_name));
        assert!(message.contains("已確認"));
        assert!(message.contains(&order.total_amount.to_string()));
    }

    #[tokio::test]
    async fn test_validate_notification_setup_valid() {
        let config = create_test_config();
        let service = NotificationServiceImpl::new(config);
        let user = create_test_user();

        let result = service.validate_notification_setup(&user).await;
        assert!(result.is_ok());

        let validation = result.unwrap();
        // Note: email_valid might be false due to SMTP connection issues in test environment
        // but we can check that line validation works
        assert!(validation.line_valid);
        assert!(validation.line_error.is_none());
    }

    #[tokio::test]
    async fn test_validate_notification_setup_invalid_email() {
        let config = create_test_config();
        let service = NotificationServiceImpl::new(config);
        let mut user = create_test_user();
        user.email = "invalid-email".to_string();

        let result = service.validate_notification_setup(&user).await;
        assert!(result.is_ok());

        let validation = result.unwrap();
        assert!(!validation.email_valid);
        assert!(validation.email_error.is_some());
        assert!(validation.email_error.unwrap().contains("Invalid email format"));
    }

    #[tokio::test]
    async fn test_validate_notification_setup_no_line_user_id() {
        let config = create_test_config();
        let service = NotificationServiceImpl::new(config);
        let mut user = create_test_user();
        user.line_user_id = None;

        let result = service.validate_notification_setup(&user).await;
        assert!(result.is_ok());

        let validation = result.unwrap();
        assert!(!validation.line_valid);
        assert!(validation.line_error.is_some());
        assert!(validation.line_error.unwrap().contains("Line user ID not set"));
    }

    #[tokio::test]
    async fn test_validate_notification_setup_disabled_notifications() {
        let config = create_test_config();
        let service = NotificationServiceImpl::new(config);
        let mut user = create_test_user();
        user.notification_email = false;
        user.notification_line = false;

        let result = service.validate_notification_setup(&user).await;
        assert!(result.is_ok());

        let validation = result.unwrap();
        assert!(!validation.email_valid);
        assert!(!validation.line_valid);
        assert!(validation.email_error.is_some());
        assert!(validation.line_error.is_some());
        assert!(validation.email_error.unwrap().contains("disabled"));
        assert!(validation.line_error.unwrap().contains("disabled"));
    }

    #[test]
    fn test_generate_order_confirmation_html() {
        let config = create_test_config();
        let service = NotificationServiceImpl::new(config);
        let user = create_test_user();
        let order = create_test_order();

        let html = service.generate_order_confirmation_html(&order, &user);
        
        assert!(html.contains("<!DOCTYPE html>"));
        assert!(html.contains("訂單確認"));
        assert!(html.contains(&user.pharmacy_name));
        assert!(html.contains(&order.order_number));
        assert!(html.contains(&order.total_amount.to_string()));
        assert!(html.contains("待處理"));
    }

    #[test]
    fn test_generate_order_status_html() {
        let config = create_test_config();
        let service = NotificationServiceImpl::new(config);
        let user = create_test_user();
        let order = create_test_order();
        let status_text = "已確認";

        let html = service.generate_order_status_html(&order, &user, status_text);
        
        assert!(html.contains("<!DOCTYPE html>"));
        assert!(html.contains("訂單狀態更新"));
        assert!(html.contains(&user.pharmacy_name));
        assert!(html.contains(&order.order_number));
        assert!(html.contains(status_text));
        assert!(html.contains(&order.total_amount.to_string()));
    }

    #[test]
    fn test_line_message_length_validation() {
        let config = create_test_config();
        let _service = NotificationServiceImpl::new(config);
        
        // Test message that's too long (over 5000 characters)
        let long_message = "a".repeat(5001);
        let notification = LineNotification {
            user_id: "test_user".to_string(),
            message: long_message,
        };

        // This would be tested in an async context, but we can test the validation logic
        assert!(notification.message.len() > 5000);
    }

    #[tokio::test]
    async fn test_notification_preferences() {
        let user = create_test_user();
        
        // Test notification preferences
        let preferences = user.notification_preferences();
        assert!(preferences.email);
        assert!(preferences.line);

        // Test has_notification_enabled
        assert!(user.has_notification_enabled(crate::models::user::NotificationChannel::Email));
        assert!(user.has_notification_enabled(crate::models::user::NotificationChannel::Line));

        // Test user without Line ID
        let mut user_no_line = user.clone();
        user_no_line.line_user_id = None;
        assert!(!user_no_line.has_notification_enabled(crate::models::user::NotificationChannel::Line));
    }
}