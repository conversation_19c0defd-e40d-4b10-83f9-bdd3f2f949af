use std::sync::Arc;
use async_trait::async_trait;
use crate::error::AppResult;
use crate::repositories::contact::ContactRepository;

#[async_trait]
pub trait ContactService: Send + Sync {
    async fn get_user_messages(&self, user_id: i64) -> AppResult<Vec<serde_json::Value>>;
    async fn get_all_messages(&self) -> AppResult<Vec<serde_json::Value>>;
    async fn send_message(&self, user_id: i64, subject: String, message: String) -> AppResult<serde_json::Value>;
    async fn reply_to_message(&self, message_id: i64, admin_id: i64, reply_message: String) -> AppResult<serde_json::Value>;
}

pub struct ContactServiceImpl {
    contact_repository: Arc<dyn ContactRepository>,
}

impl ContactServiceImpl {
    pub fn new(contact_repository: Arc<dyn ContactRepository>) -> Self {
        Self {
            contact_repository,
        }
    }
}

#[async_trait]
impl ContactService for ContactServiceImpl {
    async fn get_user_messages(&self, user_id: i64) -> AppResult<Vec<serde_json::Value>> {
        self.contact_repository.get_user_messages(user_id).await
    }

    async fn get_all_messages(&self) -> AppResult<Vec<serde_json::Value>> {
        self.contact_repository.get_all_messages().await
    }

    async fn send_message(&self, user_id: i64, subject: String, message: String) -> AppResult<serde_json::Value> {
        self.contact_repository.create_message(user_id, subject, message).await
    }

    async fn reply_to_message(&self, message_id: i64, admin_id: i64, reply_message: String) -> AppResult<serde_json::Value> {
        self.contact_repository.create_admin_reply(message_id, admin_id, reply_message).await
    }
}