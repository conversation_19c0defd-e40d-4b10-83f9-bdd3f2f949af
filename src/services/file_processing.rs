use calamine::{Reader, Xlsx, open_workbook_from_rs, RangeDeserializerBuilder};
use csv::ReaderBuilder;
use rust_decimal::Decimal;
use std::io::Cursor;
use async_trait::async_trait;
use futures::stream::{Stream, StreamExt};
use std::pin::Pin;

use crate::error::{AppError, AppResult};
use crate::models::product::{ProductImportData, ImportResult};
use crate::models::validation::Validate;

#[async_trait]
pub trait FileProcessingService: Send + Sync {
    #[allow(dead_code)]
    async fn process_excel_file(&self, file_data: Vec<u8>) -> AppResult<ImportResult>;
    #[allow(dead_code)]
    async fn process_csv_file(&self, file_data: Vec<u8>) -> AppResult<ImportResult>;
    async fn parse_excel_data(&self, file_data: Vec<u8>) -> AppResult<Vec<ProductImportData>>;
    async fn parse_csv_data(&self, file_data: Vec<u8>) -> AppResult<Vec<ProductImportData>>;
    
    /// 流式處理大文件 - 分批處理以減少內存使用
    #[allow(dead_code)]
    async fn process_large_file_stream<'a>(
        &'a self, 
        file_data: Vec<u8>,
        batch_size: usize,
    ) -> AppResult<Pin<Box<dyn Stream<Item = AppResult<Vec<ProductImportData>>> + Send + 'a>>>;
    
    /// 驗證文件大小和格式
    fn validate_file_constraints(&self, file_data: &[u8]) -> AppResult<()>;
}

pub struct FileProcessingServiceImpl;

impl FileProcessingServiceImpl {
    pub fn new() -> Self {
        Self
    }

    /// 解析 Excel 檔案中的藥品資料
    fn parse_excel_file_internal(&self, file_data: Vec<u8>) -> AppResult<Vec<ProductImportData>> {
        let cursor = Cursor::new(file_data);
        let mut workbook: Xlsx<_> = open_workbook_from_rs(cursor)
            .map_err(|e| AppError::FileProcessing(format!("Failed to open Excel file: {}", e)))?;

        // 取得第一個工作表
        let sheet_names = workbook.sheet_names();
        if sheet_names.is_empty() {
            return Err(AppError::FileProcessing("Excel file contains no sheets".to_string()));
        }

        let sheet_name = &sheet_names[0];
        let range = workbook
            .worksheet_range(sheet_name)
            .ok_or_else(|| AppError::FileProcessing(format!("Worksheet '{}' not found", sheet_name)))?
            .map_err(|e| AppError::FileProcessing(format!("Failed to read worksheet: {}", e)))?;

        let mut products = Vec::new();
        let mut row_index = 0;

        // 使用 RangeDeserializerBuilder 來解析資料
        let iter = RangeDeserializerBuilder::new().from_range(&range)
            .map_err(|e| AppError::FileProcessing(format!("Failed to create deserializer: {}", e)))?;

        for result in iter {
            row_index += 1;
            
            // 跳過標題行
            if row_index == 1 {
                continue;
            }

            match result {
                Ok(row) => {
                    match self.parse_excel_row(row, row_index) {
                        Ok(product) => products.push(product),
                        Err(e) => {
                            tracing::warn!("Failed to parse row {}: {}", row_index, e);
                            // 繼續處理其他行，不中斷整個匯入過程
                        }
                    }
                }
                Err(e) => {
                    tracing::warn!("Failed to deserialize row {}: {}", row_index, e);
                }
            }
        }

        Ok(products)
    }

    /// 解析單一 Excel 行資料
    fn parse_excel_row(&self, row: (String, String, String, String, f64, i32, String), row_index: usize) -> AppResult<ProductImportData> {
        let (nhi_code, name, manufacturer, unit, selling_price, stock_quantity, description) = row;

        // 驗證必要欄位
        if nhi_code.trim().is_empty() {
            return Err(AppError::FileProcessing(format!("Row {}: NHI code is required", row_index)));
        }
        if name.trim().is_empty() {
            return Err(AppError::FileProcessing(format!("Row {}: Product name is required", row_index)));
        }
        if manufacturer.trim().is_empty() {
            return Err(AppError::FileProcessing(format!("Row {}: Manufacturer is required", row_index)));
        }
        if unit.trim().is_empty() {
            return Err(AppError::FileProcessing(format!("Row {}: Unit is required", row_index)));
        }

        // 驗證數值
        if selling_price <= 0.0 {
            return Err(AppError::FileProcessing(format!("Row {}: Unit price must be positive", row_index)));
        }
        if stock_quantity < 0 {
            return Err(AppError::FileProcessing(format!("Row {}: Stock quantity cannot be negative", row_index)));
        }

        let product_data = ProductImportData {
            nhi_code: nhi_code.trim().to_string(),
            name: name.trim().to_string(),
            english_name: None, // Excel 導入時暫時不支持英文名稱
            ingredients: None, // Excel 導入時暫時不支持成分含量
            dosage_form: None, // Excel 導入時暫時不支持劑型
            manufacturer: manufacturer.trim().to_string(),
            unit: unit.trim().to_string(),
            selling_price: Decimal::from_f64_retain(selling_price)
                .ok_or_else(|| AppError::FileProcessing(format!("Row {}: Invalid unit price format", row_index)))?,
            stock_quantity,
            description: if description.trim().is_empty() { None } else { Some(description.trim().to_string()) },
        };

        // 驗證產品資料
        product_data.validate()
            .map_err(|errors| {
                let error_messages: Vec<String> = errors.iter()
                    .map(|e| format!("{}", e))
                    .collect();
                AppError::FileProcessing(format!("Row {}: Validation errors: {}", row_index, error_messages.join(", ")))
            })?;

        Ok(product_data)
    }

    /// 解析 CSV 檔案中的藥品資料
    fn parse_csv_file_internal(&self, file_data: Vec<u8>) -> AppResult<Vec<ProductImportData>> {
        let cursor = Cursor::new(file_data);
        let mut reader = ReaderBuilder::new()
            .has_headers(true)
            .from_reader(cursor);

        let mut products = Vec::new();
        let mut row_index = 1; // CSV 從第二行開始（第一行是標題）

        for result in reader.records() {
            row_index += 1;
            
            match result {
                Ok(record) => {
                    match self.parse_csv_row(&record, row_index) {
                        Ok(product) => products.push(product),
                        Err(e) => {
                            tracing::warn!("Failed to parse CSV row {}: {}", row_index, e);
                            // 繼續處理其他行，不中斷整個匯入過程
                        }
                    }
                }
                Err(e) => {
                    return Err(AppError::FileProcessing(format!("Failed to read CSV row {}: {}", row_index, e)));
                }
            }
        }

        Ok(products)
    }

    /// 解析單一 CSV 行資料
    fn parse_csv_row(&self, record: &csv::StringRecord, row_index: usize) -> AppResult<ProductImportData> {
        // 預期的 CSV 格式: nhi_code, name, manufacturer, unit, selling_price, stock_quantity, description
        if record.len() < 6 {
            return Err(AppError::FileProcessing(format!("Row {}: Insufficient columns (expected at least 6)", row_index)));
        }

        let nhi_code = record.get(0).unwrap_or("").trim();
        let name = record.get(1).unwrap_or("").trim();
        let manufacturer = record.get(2).unwrap_or("").trim();
        let unit = record.get(3).unwrap_or("").trim();
        let unit_price_str = record.get(4).unwrap_or("").trim();
        let stock_quantity_str = record.get(5).unwrap_or("").trim();
        let description = record.get(6).unwrap_or("").trim();

        // 驗證必要欄位
        if nhi_code.is_empty() {
            return Err(AppError::FileProcessing(format!("Row {}: NHI code is required", row_index)));
        }
        if name.is_empty() {
            return Err(AppError::FileProcessing(format!("Row {}: Product name is required", row_index)));
        }
        if manufacturer.is_empty() {
            return Err(AppError::FileProcessing(format!("Row {}: Manufacturer is required", row_index)));
        }
        if unit.is_empty() {
            return Err(AppError::FileProcessing(format!("Row {}: Unit is required", row_index)));
        }

        // 解析數值
        let stock_quantity: i32 = stock_quantity_str.parse()
            .map_err(|_| AppError::FileProcessing(format!("Row {}: Invalid stock quantity format: {}", row_index, stock_quantity_str)))?;

        // 驗證庫存數量
        if stock_quantity < 0 {
            return Err(AppError::FileProcessing(format!("Row {}: Stock quantity cannot be negative", row_index)));
        }

        let unit_price = unit_price_str.parse::<Decimal>()
            .map_err(|_| AppError::FileProcessing(format!("Row {}: Invalid unit price format: {}", row_index, unit_price_str)))?;

        // 驗證價格必須為正數
        if unit_price <= Decimal::ZERO {
            return Err(AppError::FileProcessing(format!("Row {}: Unit price must be positive", row_index)));
        }

        let product_data = ProductImportData {
            nhi_code: nhi_code.to_string(),
            name: name.to_string(),
            english_name: None, // CSV 導入時暫時不支持英文名稱
            ingredients: None, // CSV 導入時暫時不支持成分含量
            dosage_form: None, // CSV 導入時暫時不支持劑型
            manufacturer: manufacturer.to_string(),
            unit: unit.to_string(),
            selling_price: unit_price,
            stock_quantity,
            description: if description.is_empty() { None } else { Some(description.to_string()) },
        };

        // 驗證產品資料
        product_data.validate()
            .map_err(|errors| {
                let error_messages: Vec<String> = errors.iter()
                    .map(|e| format!("{}", e))
                    .collect();
                AppError::FileProcessing(format!("Row {}: Validation errors: {}", row_index, error_messages.join(", ")))
            })?;

        Ok(product_data)
    }

    /// 處理匯入結果統計
    #[allow(dead_code)]
    fn create_import_result(&self, products: Vec<ProductImportData>, errors: Vec<String>) -> ImportResult {
        ImportResult {
            total_rows: products.len() + errors.len(),
            imported_count: products.len(),
            error_count: errors.len(),
            errors,
        }
    }
}

#[async_trait]
impl FileProcessingService for FileProcessingServiceImpl {
    async fn process_excel_file(&self, file_data: Vec<u8>) -> AppResult<ImportResult> {
        tracing::info!("Starting Excel file processing, file size: {} bytes", file_data.len());
        
        // 驗證檔案大小
        if file_data.is_empty() {
            return Err(AppError::FileProcessing("File is empty".to_string()));
        }
        
        // 限制檔案大小 (例如 10MB)
        const MAX_FILE_SIZE: usize = 10 * 1024 * 1024;
        if file_data.len() > MAX_FILE_SIZE {
            return Err(AppError::FileProcessing("File size exceeds maximum limit (10MB)".to_string()));
        }

        let products = self.parse_excel_file_internal(file_data)?;
        
        tracing::info!("Successfully processed Excel file, imported {} products", products.len());
        
        Ok(ImportResult {
            total_rows: products.len(),
            imported_count: products.len(),
            error_count: 0,
            errors: Vec::new(),
        })
    }

    async fn process_csv_file(&self, file_data: Vec<u8>) -> AppResult<ImportResult> {
        tracing::info!("Starting CSV file processing, file size: {} bytes", file_data.len());
        
        // 驗證檔案大小
        if file_data.is_empty() {
            return Err(AppError::FileProcessing("File is empty".to_string()));
        }
        
        // 限制檔案大小 (例如 10MB)
        const MAX_FILE_SIZE: usize = 10 * 1024 * 1024;
        if file_data.len() > MAX_FILE_SIZE {
            return Err(AppError::FileProcessing("File size exceeds maximum limit (10MB)".to_string()));
        }

        let products = self.parse_csv_file_internal(file_data)?;
        
        tracing::info!("Successfully processed CSV file, imported {} products", products.len());
        
        Ok(ImportResult {
            total_rows: products.len(),
            imported_count: products.len(),
            error_count: 0,
            errors: Vec::new(),
        })
    }

    async fn parse_excel_data(&self, file_data: Vec<u8>) -> AppResult<Vec<ProductImportData>> {
        tracing::info!("Parsing Excel data, file size: {} bytes", file_data.len());
        
        // 驗證檔案大小
        if file_data.is_empty() {
            return Err(AppError::FileProcessing("File is empty".to_string()));
        }
        
        // 限制檔案大小 (例如 10MB)
        const MAX_FILE_SIZE: usize = 10 * 1024 * 1024;
        if file_data.len() > MAX_FILE_SIZE {
            return Err(AppError::FileProcessing("File size exceeds maximum limit (10MB)".to_string()));
        }

        self.parse_excel_file_internal(file_data)
    }

    async fn parse_csv_data(&self, file_data: Vec<u8>) -> AppResult<Vec<ProductImportData>> {
        tracing::info!("Parsing CSV data, file size: {} bytes", file_data.len());
        
        // 驗證檔案大小
        if file_data.is_empty() {
            return Err(AppError::FileProcessing("File is empty".to_string()));
        }
        
        // 限制檔案大小 (例如 10MB)
        const MAX_FILE_SIZE: usize = 10 * 1024 * 1024;
        if file_data.len() > MAX_FILE_SIZE {
            return Err(AppError::FileProcessing("File size exceeds maximum limit (10MB)".to_string()));
        }

        self.parse_csv_file_internal(file_data)
    }

    async fn process_large_file_stream<'a>(
        &'a self, 
        file_data: Vec<u8>,
        batch_size: usize,
    ) -> AppResult<Pin<Box<dyn Stream<Item = AppResult<Vec<ProductImportData>>> + Send + 'a>>> {
        // 驗證檔案約束
        self.validate_file_constraints(&file_data)?;
        
        // 解析完整資料
        let all_data = self.parse_csv_file_internal(file_data)?;
        
        // 創建批次流
        let batches: Vec<Vec<ProductImportData>> = all_data
            .chunks(batch_size)
            .map(|chunk| chunk.to_vec())
            .collect();
            
        let stream = futures::stream::iter(batches)
            .map(|batch| Ok(batch));
        
        Ok(Box::pin(stream))
    }
    
    fn validate_file_constraints(&self, file_data: &[u8]) -> AppResult<()> {
        // 檢查檔案是否為空
        if file_data.is_empty() {
            return Err(AppError::FileProcessing("File is empty".to_string()));
        }
        
        // 檢查檔案大小限制 (50MB)
        const MAX_FILE_SIZE: usize = 50 * 1024 * 1024;
        if file_data.len() > MAX_FILE_SIZE {
            return Err(AppError::FileProcessing(format!(
                "File size ({} bytes) exceeds maximum limit ({} bytes)", 
                file_data.len(), 
                MAX_FILE_SIZE
            )));
        }
        
        // 檢查最小檔案大小
        const MIN_FILE_SIZE: usize = 10;
        if file_data.len() < MIN_FILE_SIZE {
            return Err(AppError::FileProcessing("File is too small to contain valid data".to_string()));
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal_macros::dec;

    #[tokio::test]
    async fn test_process_empty_file() {
        let service = FileProcessingServiceImpl::new();
        let empty_data = Vec::new();
        
        let result = service.process_excel_file(empty_data).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("File is empty"));
    }

    #[tokio::test]
    async fn test_process_oversized_file() {
        let service = FileProcessingServiceImpl::new();
        let oversized_data = vec![0u8; 11 * 1024 * 1024]; // 11MB
        
        let result = service.process_excel_file(oversized_data).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("File size exceeds maximum limit"));
    }

    #[test]
    fn test_parse_csv_row_valid() {
        let service = FileProcessingServiceImpl::new();
        let record = csv::StringRecord::from(vec![
            "A001234567",
            "阿司匹林",
            "台灣製藥",
            "盒",
            "50.5",
            "100",
            "止痛藥"
        ]);
        
        let result = service.parse_csv_row(&record, 2);
        assert!(result.is_ok());
        
        let product = result.unwrap();
        assert_eq!(product.nhi_code, "A001234567");
        assert_eq!(product.name, "阿司匹林");
        assert_eq!(product.manufacturer, "台灣製藥");
        assert_eq!(product.unit, "盒");
        assert_eq!(product.selling_price, dec!(50.5));
        assert_eq!(product.stock_quantity, 100);
        assert_eq!(product.description, Some("止痛藥".to_string()));
    }

    #[test]
    fn test_parse_csv_row_missing_columns() {
        let service = FileProcessingServiceImpl::new();
        let record = csv::StringRecord::from(vec!["A001", "阿司匹林"]); // 只有2個欄位
        
        let result = service.parse_csv_row(&record, 2);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Insufficient columns"));
    }

    #[test]
    fn test_parse_csv_row_invalid_price() {
        let service = FileProcessingServiceImpl::new();
        let record = csv::StringRecord::from(vec![
            "A001234567",
            "阿司匹林",
            "台灣製藥",
            "盒",
            "invalid_price", // 無效價格
            "100",
            ""
        ]);
        
        let result = service.parse_csv_row(&record, 2);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Invalid unit price format"));
    }

    #[test]
    fn test_parse_csv_row_negative_price() {
        let service = FileProcessingServiceImpl::new();
        let record = csv::StringRecord::from(vec![
            "A001234567",
            "阿司匹林",
            "台灣製藥",
            "盒",
            "-10.5", // 負價格
            "100",
            ""
        ]);
        
        let result = service.parse_csv_row(&record, 2);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Unit price must be positive"));
    }

    #[test]
    fn test_parse_csv_row_invalid_stock() {
        let service = FileProcessingServiceImpl::new();
        let record = csv::StringRecord::from(vec![
            "A001234567",
            "阿司匹林",
            "台灣製藥",
            "盒",
            "50.5",
            "invalid_stock", // 無效庫存
            ""
        ]);
        
        let result = service.parse_csv_row(&record, 2);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Invalid stock quantity format"));
    }

    #[test]
    fn test_parse_csv_row_negative_stock() {
        let service = FileProcessingServiceImpl::new();
        let record = csv::StringRecord::from(vec![
            "A001234567",
            "阿司匹林",
            "台灣製藥",
            "盒",
            "50.5",
            "-10", // 負庫存
            ""
        ]);
        
        let result = service.parse_csv_row(&record, 2);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Stock quantity cannot be negative"));
    }

    #[test]
    fn test_parse_csv_row_empty_required_fields() {
        let service = FileProcessingServiceImpl::new();
        let record = csv::StringRecord::from(vec![
            "", // 空的 NHI 代碼
            "阿司匹林",
            "台灣製藥",
            "盒",
            "50.5",
            "100",
            ""
        ]);
        
        let result = service.parse_csv_row(&record, 2);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("NHI code is required"));
    }

    #[test]
    fn test_create_import_result() {
        let service = FileProcessingServiceImpl::new();
        let products = vec![
            ProductImportData {
                nhi_code: "A001".to_string(),
                name: "Product 1".to_string(),
                manufacturer: "Manufacturer 1".to_string(),
                unit: "盒".to_string(),
                selling_price: dec!(50.0),
                stock_quantity: 100,
                description: None,
            }
        ];
        let errors = vec!["Error 1".to_string(), "Error 2".to_string()];
        
        let result = service.create_import_result(products, errors);
        
        assert_eq!(result.total_rows, 3);
        assert_eq!(result.imported_count, 1);
        assert_eq!(result.error_count, 2);
        assert_eq!(result.errors.len(), 2);
    }

    #[tokio::test]
    async fn test_process_csv_file_with_valid_data() {
        let service = FileProcessingServiceImpl::new();
        
        // 建立測試 CSV 資料
        let csv_content = "nhi_code,name,manufacturer,unit,selling_price,stock_quantity,description\nA001234567,阿司匹林,台灣製藥,盒,50.5,100,止痛藥\nB002345678,維他命C,健康製藥,瓶,25.0,200,維他命補充劑";
        let csv_data = csv_content.as_bytes().to_vec();
        
        let result = service.process_csv_file(csv_data).await;
        assert!(result.is_ok());
        
        let import_result = result.unwrap();
        assert_eq!(import_result.imported_count, 2);
        assert_eq!(import_result.error_count, 0);
        assert_eq!(import_result.total_rows, 2);
    }

    #[test]
    fn test_parse_csv_data_with_multiple_rows() {
        let service = FileProcessingServiceImpl::new();
        
        let csv_content = "nhi_code,name,manufacturer,unit,selling_price,stock_quantity,description\nA001234567,阿司匹林,台灣製藥,盒,50.5,100,止痛藥\nB002345678,維他命C,健康製藥,瓶,25.0,200,維他命補充劑\nC003456789,感冒糖漿,藥品公司,瓶,80.0,50,";
        let csv_data = csv_content.as_bytes().to_vec();
        
        let result = service.parse_csv_file_internal(csv_data);
        assert!(result.is_ok());
        
        let products = result.unwrap();
        assert_eq!(products.len(), 3);
        
        // 驗證第一個產品
        assert_eq!(products[0].nhi_code, "A001234567");
        assert_eq!(products[0].name, "阿司匹林");
        assert_eq!(products[0].description, Some("止痛藥".to_string()));
        
        // 驗證第二個產品
        assert_eq!(products[1].nhi_code, "B002345678");
        assert_eq!(products[1].name, "維他命C");
        assert_eq!(products[1].stock_quantity, 200);
        
        // 驗證第三個產品（空描述）
        assert_eq!(products[2].nhi_code, "C003456789");
        assert_eq!(products[2].description, None);
    }

    #[test]
    fn test_parse_csv_row_with_whitespace() {
        let service = FileProcessingServiceImpl::new();
        let record = csv::StringRecord::from(vec![
            "  A001234567  ", // 前後有空格
            "  阿司匹林  ",
            "  台灣製藥  ",
            "  盒  ",
            "  50.5  ",
            "  100  ",
            "  止痛藥  "
        ]);
        
        let result = service.parse_csv_row(&record, 2);
        assert!(result.is_ok());
        
        let product = result.unwrap();
        assert_eq!(product.nhi_code, "A001234567"); // 應該去除空格
        assert_eq!(product.name, "阿司匹林");
        assert_eq!(product.manufacturer, "台灣製藥");
        assert_eq!(product.unit, "盒");
        assert_eq!(product.description, Some("止痛藥".to_string()));
    }

    #[test]
    fn test_parse_csv_row_without_description() {
        let service = FileProcessingServiceImpl::new();
        let record = csv::StringRecord::from(vec![
            "A001234567",
            "阿司匹林",
            "台灣製藥",
            "盒",
            "50.5",
            "100"
            // 沒有描述欄位
        ]);
        
        let result = service.parse_csv_row(&record, 2);
        assert!(result.is_ok());
        
        let product = result.unwrap();
        assert_eq!(product.description, None);
    }

    #[test]
    fn test_parse_csv_row_zero_stock() {
        let service = FileProcessingServiceImpl::new();
        let record = csv::StringRecord::from(vec![
            "A001234567",
            "阿司匹林",
            "台灣製藥",
            "盒",
            "50.5",
            "0", // 零庫存應該是有效的
            ""
        ]);
        
        let result = service.parse_csv_row(&record, 2);
        assert!(result.is_ok());
        
        let product = result.unwrap();
        assert_eq!(product.stock_quantity, 0);
    }

    #[test]
    fn test_parse_csv_row_decimal_price() {
        let service = FileProcessingServiceImpl::new();
        let record = csv::StringRecord::from(vec![
            "A001234567",
            "阿司匹林",
            "台灣製藥",
            "盒",
            "123.45", // 兩位小數
            "100",
            ""
        ]);
        
        let result = service.parse_csv_row(&record, 2);
        assert!(result.is_ok());
        
        let product = result.unwrap();
        // 使用字串比較來避免浮點精度問題
        assert_eq!(product.unit_price.to_string(), "123.45");
    }
}