use std::sync::Arc;
use async_trait::async_trait;
use serde::{Serialize, Deserialize};
use tracing::info;

use crate::error::{AppError, AppResult};
use crate::models::{Order, OrderItem, OrderDetails, Order<PERSON>tatus, OrderFilter, CreateOrderRequest};
use crate::models::validation::Validate;
use crate::repositories::order::OrderRepository;
use crate::repositories::cart::CartRepository;
use crate::services::product::ProductService;

#[async_trait]
pub trait OrderService: Send + Sync {
    #[allow(dead_code)]
    async fn create_order_from_cart(&self, user_id: i64) -> AppResult<OrderDetails>;
    async fn create_order_from_cart_with_notes(&self, user_id: i64, notes: Option<String>) -> AppResult<OrderDetails>;
    async fn create_order_from_request(&self, user_id: i64, request: CreateOrderRequest) -> AppResult<OrderDetails>;
    async fn get_order(&self, order_id: i64, user_id: i64) -> AppResult<Option<OrderDetails>>;
    async fn get_order_details_by_id(&self, order_id: i64) -> AppResult<Option<OrderDetails>>;
    async fn get_order_by_number(&self, order_number: &str, user_id: i64) -> AppResult<Option<OrderDetails>>;
    #[allow(dead_code)]
    async fn update_order_status(&self, order_id: i64, user_id: i64, status: OrderStatus) -> AppResult<OrderDetails>;
    async fn update_order_status_admin(&self, order_id: i64, status: OrderStatus) -> AppResult<OrderDetails>;
    async fn list_user_orders(&self, user_id: i64, filter: Option<OrderFilter>, page: Option<u32>, limit: Option<u32>) -> AppResult<Vec<Order>>;
    async fn list_all_orders_with_users(&self, filter: Option<OrderFilter>, page: u32, limit: u32) -> AppResult<Vec<serde_json::Value>>;
    async fn cancel_order(&self, order_id: i64, user_id: i64) -> AppResult<OrderDetails>;
    async fn get_order_count(&self, user_id: i64, filter: Option<OrderFilter>) -> AppResult<i64>;
    async fn update_order_item_stock_status(&self, item_id: i64, is_stock_out: bool) -> AppResult<OrderItem>;
    async fn update_order_item_quantity(&self, item_id: i64, quantity: i32) -> AppResult<OrderItem>;
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OrderCreationResult {
    pub order: OrderDetails,
    pub warnings: Vec<String>,
}

pub struct OrderServiceImpl {
    order_repository: Arc<dyn OrderRepository>,
    cart_repository: Arc<dyn CartRepository>,
    product_service: Arc<dyn ProductService>,
}

impl OrderServiceImpl {
    pub fn new(
        order_repository: Arc<dyn OrderRepository>,
        cart_repository: Arc<dyn CartRepository>,
        product_service: Arc<dyn ProductService>,
    ) -> Self {
        Self {
            order_repository,
            cart_repository,
            product_service,
        }
    }

    /// 驗證訂單項目的庫存和價格
    async fn validate_order_items(&self, items: &[crate::models::CreateOrderItem]) -> AppResult<Vec<OrderItem>> {
        let mut order_items = Vec::new();
        
        for item in items {
            // 檢查產品是否存在
            let product = self.product_service.get_product_by_id(item.product_id).await?
                .ok_or_else(|| AppError::NotFound(format!("Product with id {} not found", item.product_id)))?;

            // 檢查產品是否啟用
            if !product.is_active {
                return Err(AppError::Validation(format!("Product {} is not active", product.name)));
            }

            // 檢查庫存是否充足
            if !product.has_sufficient_stock(item.quantity) {
                return Err(AppError::Validation(format!(
                    "Insufficient stock for product {}: requested {}, available {}",
                    product.name, item.quantity, product.stock_quantity
                )));
            }

            // 創建訂單項目
            let order_item = OrderItem::new(item.product_id, item.quantity, product.selling_price)
                .map_err(|e| AppError::Validation(format!("Invalid order item: {:?}", e)))?;

            order_items.push(order_item);
        }

        Ok(order_items)
    }

    /// 從購物車項目創建訂單項目
    async fn create_order_items_from_cart(&self, cart_details: &crate::models::CartDetails) -> AppResult<Vec<OrderItem>> {
        let mut order_items = Vec::new();

        for cart_item_detail in &cart_details.items {
            // 重新驗證產品和庫存
            let product = self.product_service.get_product_by_id(cart_item_detail.item.product_id).await?
                .ok_or_else(|| AppError::NotFound(format!("Product with id {} not found", cart_item_detail.item.product_id)))?;

            if !product.is_active {
                return Err(AppError::Validation(format!("Product {} is no longer active", cart_item_detail.product_name)));
            }

            if !product.has_sufficient_stock(cart_item_detail.item.quantity) {
                return Err(AppError::Validation(format!(
                    "Insufficient stock for product {}: requested {}, available {}",
                    cart_item_detail.product_name, cart_item_detail.item.quantity, product.stock_quantity
                )));
            }

            // 使用當前產品價格創建訂單項目
            let order_item = OrderItem::new(
                cart_item_detail.item.product_id,
                cart_item_detail.item.quantity,
                product.selling_price,
            ).map_err(|e| AppError::Validation(format!("Invalid order item: {:?}", e)))?;

            order_items.push(order_item);
        }

        Ok(order_items)
    }

    /// 減少產品庫存
    async fn reduce_product_stock(&self, order_items: &[OrderItem]) -> AppResult<()> {
        for item in order_items {
            let product = self.product_service.get_product_by_id(item.product_id).await?
                .ok_or_else(|| AppError::NotFound(format!("Product with id {} not found", item.product_id)))?;

            let new_stock = product.stock_quantity - item.quantity;
            self.product_service.update_stock(item.product_id, new_stock).await?;
        }
        Ok(())
    }
}

#[async_trait]
impl OrderService for OrderServiceImpl {
    async fn create_order_from_cart(&self, user_id: i64) -> AppResult<OrderDetails> {
        self.create_order_from_cart_with_notes(user_id, None).await
    }

    async fn create_order_from_cart_with_notes(&self, user_id: i64, notes: Option<String>) -> AppResult<OrderDetails> {
        // 獲取購物車詳情
        let cart_details = self.cart_repository.get_cart_details(user_id).await?
            .ok_or_else(|| AppError::NotFound("Cart not found or empty".to_string()))?;

        if cart_details.items.is_empty() {
            return Err(AppError::Validation("Cart is empty".to_string()));
        }

        // 從購物車項目創建訂單項目
        let order_items = self.create_order_items_from_cart(&cart_details).await?;

        // 創建訂單（帶備註）
        let mut order = Order::new(user_id, order_items.clone())
            .map_err(|e| AppError::Validation(format!("Invalid order: {:?}", e)))?;
        
        // 設置備註
        if let Some(notes_text) = notes {
            if !notes_text.trim().is_empty() {
                order.notes = Some(notes_text.trim().to_string());
            }
        }

        // 儲存訂單到資料庫
        let created_order = self.order_repository.create_order(order).await?;
        let created_items = self.order_repository.create_order_items(created_order.id, order_items).await?;

        // 記錄訂單項目創建
        info!("Created {} order items for order {}", created_items.len(), created_order.id);
        
        // 注意：庫存已經在加入購物車時扣除，這裡不需要再次扣除

        // 清空購物車（不恢復庫存，因為庫存已經在加入購物車時扣除）
        self.cart_repository.clear_cart(cart_details.cart.id).await?;

        // 獲取完整的訂單詳情
        self.order_repository.get_order_details(created_order.id).await?
            .ok_or_else(|| AppError::Internal("Failed to retrieve order details after creation".to_string()))
    }

    async fn create_order_from_request(&self, user_id: i64, request: CreateOrderRequest) -> AppResult<OrderDetails> {
        // 驗證請求
        request.validate().map_err(|errors| {
            AppError::Validation(format!("Invalid create order request: {:?}", errors))
        })?;

        // 驗證訂單項目
        let order_items = self.validate_order_items(&request.items).await?;

        // 創建訂單
        let order = Order::new(user_id, order_items.clone())
            .map_err(|e| AppError::Validation(format!("Invalid order: {:?}", e)))?;

        // 儲存訂單到資料庫
        let created_order = self.order_repository.create_order(order).await?;
        let created_items = self.order_repository.create_order_items(created_order.id, order_items).await?;

        // 減少產品庫存
        self.reduce_product_stock(&created_items).await?;

        // 獲取完整的訂單詳情
        self.order_repository.get_order_details(created_order.id).await?
            .ok_or_else(|| AppError::Internal("Failed to retrieve order details after creation".to_string()))
    }

    async fn get_order(&self, order_id: i64, user_id: i64) -> AppResult<Option<OrderDetails>> {
        let order_details = self.order_repository.get_order_details(order_id).await?;
        
        // 確保訂單屬於該用戶
        if let Some(ref details) = order_details {
            if details.order.user_id != user_id {
                return Err(AppError::Authorization("Access denied to this order".to_string()));
            }
        }

        Ok(order_details)
    }

    async fn get_order_details_by_id(&self, order_id: i64) -> AppResult<Option<OrderDetails>> {
        // 直接獲取訂單詳情，不檢查用戶ID（用於管理員）
        self.order_repository.get_order_details(order_id).await
    }

    async fn get_order_by_number(&self, order_number: &str, user_id: i64) -> AppResult<Option<OrderDetails>> {
        let order = self.order_repository.find_by_order_number(order_number).await?;
        
        if let Some(order) = order {
            if order.user_id != user_id {
                return Err(AppError::Authorization("Access denied to this order".to_string()));
            }
            self.order_repository.get_order_details(order.id).await
        } else {
            Ok(None)
        }
    }

    async fn update_order_status(&self, order_id: i64, user_id: i64, status: OrderStatus) -> AppResult<OrderDetails> {
        // 獲取現有訂單
        let existing_order = self.order_repository.find_by_id(order_id).await?
            .ok_or_else(|| AppError::NotFound("Order not found".to_string()))?;

        // 確保訂單屬於該用戶
        if existing_order.user_id != user_id {
            return Err(AppError::Authorization("Access denied to this order".to_string()));
        }

        // 檢查狀態轉換是否有效
        if !existing_order.status.can_transition_to(&status) {
            return Err(AppError::Validation(format!(
                "Cannot transition from {:?} to {:?}",
                existing_order.status, status
            )));
        }

        // 更新狀態
        self.order_repository.update_status(order_id, status).await?;

        // 獲取更新後的訂單詳情
        self.order_repository.get_order_details(order_id).await?
            .ok_or_else(|| AppError::Internal("Failed to retrieve order details after status update".to_string()))
    }

    async fn update_order_status_admin(&self, order_id: i64, status: OrderStatus) -> AppResult<OrderDetails> {
        // 獲取現有訂單
        let existing_order = self.order_repository.find_by_id(order_id).await?
            .ok_or_else(|| AppError::NotFound("Order not found".to_string()))?;

        // 管理員可以進行任何狀態轉換，但仍然檢查基本的業務邏輯
        // 例如：不能從已送達變回待處理（除非有特殊原因）
        if existing_order.status == OrderStatus::已出貨 && status == OrderStatus::待處理 {
            return Err(AppError::Validation(
                "Cannot change delivered order back to pending without special authorization".to_string()
            ));
        }

        // 更新狀態
        self.order_repository.update_status(order_id, status).await?;

        // 獲取更新後的訂單詳情
        self.order_repository.get_order_details(order_id).await?
            .ok_or_else(|| AppError::Internal("Failed to retrieve order details after status update".to_string()))
    }

    async fn list_user_orders(&self, user_id: i64, filter: Option<OrderFilter>, page: Option<u32>, limit: Option<u32>) -> AppResult<Vec<Order>> {
        let limit = limit.unwrap_or(50) as i32;
        let page = page.unwrap_or(1);
        let offset = ((page - 1) * limit as u32) as i32;

        self.order_repository.list_by_user(user_id, filter, Some(limit), Some(offset)).await
    }

    async fn list_all_orders_with_users(&self, filter: Option<OrderFilter>, page: u32, limit: u32) -> AppResult<Vec<serde_json::Value>> {
        let limit = limit as i32;
        let offset = ((page - 1) * limit as u32) as i32;

        // 調用 repository 方法獲取所有訂單及其用戶信息
        self.order_repository.list_all_with_users(filter, Some(limit), Some(offset)).await
    }

    async fn cancel_order(&self, order_id: i64, user_id: i64) -> AppResult<OrderDetails> {
        // 獲取現有訂單
        let existing_order = self.order_repository.find_by_id(order_id).await?
            .ok_or_else(|| AppError::NotFound("Order not found".to_string()))?;

        // 確保訂單屬於該用戶
        if existing_order.user_id != user_id {
            return Err(AppError::Authorization("Access denied to this order".to_string()));
        }

        // 檢查訂單是否可以取消
        if !existing_order.is_cancellable() {
            return Err(AppError::Validation("Order cannot be cancelled".to_string()));
        }

        // 恢復庫存（因為庫存在加入購物車時就被扣除了）
        let order_items = self.order_repository.get_order_items(order_id).await?;
        
        // 恢復產品庫存
        for item in &order_items {
            let product = self.product_service.get_product_by_id(item.product_id).await?
                .ok_or_else(|| AppError::NotFound(format!("Product with id {} not found", item.product_id)))?;

            let new_stock = product.stock_quantity + item.quantity;
            self.product_service.update_stock(item.product_id, new_stock).await?;
        }

        // 簡化版本：取消功能暫時不可用
        Err(AppError::Validation("取消功能暫時不可用".to_string()))
    }

    async fn get_order_count(&self, user_id: i64, filter: Option<OrderFilter>) -> AppResult<i64> {
        self.order_repository.count_by_user(user_id, filter).await
    }

    async fn update_order_item_stock_status(&self, item_id: i64, is_stock_out: bool) -> AppResult<OrderItem> {
        self.order_repository.update_order_item_stock_status(item_id, is_stock_out).await
    }

    async fn update_order_item_quantity(&self, item_id: i64, quantity: i32) -> AppResult<OrderItem> {
        if quantity < 0 {
            return Err(AppError::Validation("數量不能為負數".to_string()));
        }
        self.order_repository.update_order_item_quantity(item_id, quantity).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::models::{Product, Cart, CartItem, CartDetails, CartItemDetails, CreateOrderItem};
    use crate::repositories::order::OrderRepository;
    use crate::repositories::cart::CartRepository;
    use crate::services::product::ProductService;
    use async_trait::async_trait;
    use mockall::mock;
    use rust_decimal_macros::dec;
    use chrono::Utc;

    mock! {
        OrderRepo {}

        #[async_trait]
        impl OrderRepository for OrderRepo {
            async fn create_order(&self, order: Order) -> Result<Order, AppError>;
            async fn create_order_items(&self, order_id: i64, items: Vec<OrderItem>) -> Result<Vec<OrderItem>, AppError>;
            async fn find_by_id(&self, id: i64) -> Result<Option<Order>, AppError>;
            async fn find_by_order_number(&self, order_number: &str) -> Result<Option<Order>, AppError>;
            async fn update_status(&self, id: i64, status: OrderStatus) -> Result<Order, AppError>;
            async fn list_by_user(&self, user_id: i64, filter: Option<OrderFilter>, limit: Option<i32>, offset: Option<i32>) -> Result<Vec<Order>, AppError>;
            async fn get_order_details(&self, id: i64) -> Result<Option<OrderDetails>, AppError>;
            async fn get_order_items(&self, order_id: i64) -> Result<Vec<OrderItem>, AppError>;
            async fn update_order(&self, id: i64, total_amount: rust_decimal::Decimal) -> Result<Order, AppError>;
            async fn delete_order(&self, id: i64) -> Result<bool, AppError>;
            async fn count_by_user(&self, user_id: i64, filter: Option<OrderFilter>) -> Result<i64, AppError>;
        }
    }

    mock! {
        CartRepo {}

        #[async_trait]
        impl CartRepository for CartRepo {
            async fn create_cart(&self, user_id: i64) -> Result<Cart, AppError>;
            async fn find_cart_by_user_id(&self, user_id: i64) -> Result<Option<Cart>, AppError>;
            async fn get_or_create_cart(&self, user_id: i64) -> Result<Cart, AppError>;
            async fn add_item(&self, cart_id: i64, product_id: i64, quantity: i32, unit_price: rust_decimal::Decimal) -> Result<CartItem, AppError>;
            async fn update_item_quantity(&self, cart_item_id: i64, quantity: i32) -> Result<CartItem, AppError>;
            async fn remove_item(&self, cart_item_id: i64) -> Result<bool, AppError>;
            async fn find_cart_item(&self, cart_id: i64, product_id: i64) -> Result<Option<CartItem>, AppError>;
            async fn get_cart_details(&self, user_id: i64) -> Result<Option<CartDetails>, AppError>;
            async fn clear_cart(&self, cart_id: i64) -> Result<bool, AppError>;
            async fn get_cart_items(&self, cart_id: i64) -> Result<Vec<CartItem>, AppError>;
            async fn update_cart_timestamp(&self, cart_id: i64) -> Result<(), AppError>;
        }
    }

    mock! {
        ProductService {}

        #[async_trait]
        impl ProductService for ProductService {
            async fn import_from_excel(&self, file_data: Vec<u8>) -> AppResult<crate::models::product::ImportResult>;
            async fn import_from_csv(&self, file_data: Vec<u8>) -> AppResult<crate::models::product::ImportResult>;
            async fn get_products(&self, filter: Option<crate::models::product::ProductFilter>, page: Option<u32>, limit: Option<u32>) -> AppResult<Vec<Product>>;
            async fn get_product_by_id(&self, id: i64) -> AppResult<Option<Product>>;
            async fn get_product_by_nhi_code(&self, nhi_code: &str) -> AppResult<Option<Product>>;
            async fn update_stock(&self, product_id: i64, quantity: i32) -> AppResult<Product>;
            async fn get_low_stock_products(&self) -> AppResult<Vec<Product>>;
            async fn check_stock_availability(&self, product_id: i64, required_quantity: i32) -> AppResult<bool>;
            async fn get_stock_status(&self, product_id: i64) -> AppResult<crate::services::product::StockStatus>;
            async fn bulk_update_stock(&self, updates: Vec<crate::services::product::StockUpdate>) -> AppResult<Vec<Product>>;
        }
    }

    fn create_test_product() -> Product {
        Product {
            id: 1,
            nhi_code: "A001".to_string(),
            name: "阿司匹林".to_string(),
            manufacturer: "台廠".to_string(),
            unit: "盒".to_string(),
            unit_price: dec!(50.0),
            stock_quantity: 100,
            description: Some("止痛藥".to_string()),
            is_active: true,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    fn create_test_cart_details() -> CartDetails {
        let cart = Cart {
            id: 1,
            user_id: 1,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        let cart_item = CartItem {
            id: 1,
            cart_id: 1,
            product_id: 1,
            quantity: 2,
            unit_price: dec!(50.0),
            subtotal: dec!(100.0),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        CartDetails {
            cart,
            items: vec![CartItemDetails {
                item: cart_item,
                product_name: "阿司匹林".to_string(),
                product_nhi_code: "A001".to_string(),
                available_stock: 100,
            }],
            total_amount: dec!(100.0),
            total_items: 2,
        }
    }

    fn create_test_order() -> Order {
        Order {
            id: 1,
            order_number: "ORD-20240101-ABC123".to_string(),
            user_id: 1,
            status: OrderStatus::Pending,
            total_amount: dec!(100.0),
            notes: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    fn create_test_order_details() -> OrderDetails {
        let order = create_test_order();
        let order_item = OrderItem {
            id: 1,
            order_id: 1,
            product_id: 1,
            quantity: 2,
            unit_price: dec!(50.0),
            subtotal: dec!(100.0),
        };

        OrderDetails {
            order,
            items: vec![crate::models::OrderItemDetails {
                item: order_item,
                product_name: "阿司匹林".to_string(),
                product_nhi_code: "A001".to_string(),
            }],
        }
    }

    #[tokio::test]
    async fn test_create_order_from_cart_success() {
        let mut mock_order_repo = MockOrderRepo::new();
        let mut mock_cart_repo = MockCartRepo::new();
        let mut mock_product_service = MockProductService::new();

        let cart_details = create_test_cart_details();
        let order = create_test_order();
        let order_details = create_test_order_details();

        // Mock cart repository
        mock_cart_repo
            .expect_get_cart_details()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(move |_| Ok(Some(cart_details.clone())));

        mock_cart_repo
            .expect_clear_cart()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(true));

        // Mock product service
        mock_product_service
            .expect_get_product_by_id()
            .times(2) // 一次驗證，一次減庫存
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(Some(create_test_product())));

        mock_product_service
            .expect_update_stock()
            .times(1)
            .with(mockall::predicate::eq(1), mockall::predicate::eq(98))
            .returning(|_, _| Ok(create_test_product()));

        // Mock order repository
        mock_order_repo
            .expect_create_order()
            .times(1)
            .returning(move |_| Ok(order.clone()));

        mock_order_repo
            .expect_create_order_items()
            .times(1)
            .returning(|_, items| Ok(items));

        mock_order_repo
            .expect_get_order_details()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(move |_| Ok(Some(order_details.clone())));

        let service = OrderServiceImpl::new(
            Arc::new(mock_order_repo),
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.create_order_from_cart(1).await;
        assert!(result.is_ok());
        
        let order_details = result.unwrap();
        assert_eq!(order_details.order.user_id, 1);
        assert_eq!(order_details.items.len(), 1);
    }

    #[tokio::test]
    async fn test_create_order_from_cart_empty_cart() {
        let mock_order_repo = MockOrderRepo::new();
        let mut mock_cart_repo = MockCartRepo::new();
        let mock_product_service = MockProductService::new();

        mock_cart_repo
            .expect_get_cart_details()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(None));

        let service = OrderServiceImpl::new(
            Arc::new(mock_order_repo),
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.create_order_from_cart(1).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Cart not found or empty"));
    }

    #[tokio::test]
    async fn test_create_order_from_request_success() {
        let mut mock_order_repo = MockOrderRepo::new();
        let mock_cart_repo = MockCartRepo::new();
        let mut mock_product_service = MockProductService::new();

        let request = CreateOrderRequest {
            items: vec![CreateOrderItem {
                product_id: 1,
                quantity: 2,
            }],
        };

        let order = create_test_order();
        let order_details = create_test_order_details();

        // Mock product service
        mock_product_service
            .expect_get_product_by_id()
            .times(2) // 一次驗證，一次減庫存
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(Some(create_test_product())));

        mock_product_service
            .expect_update_stock()
            .times(1)
            .with(mockall::predicate::eq(1), mockall::predicate::eq(98))
            .returning(|_, _| Ok(create_test_product()));

        // Mock order repository
        mock_order_repo
            .expect_create_order()
            .times(1)
            .returning(move |_| Ok(order.clone()));

        mock_order_repo
            .expect_create_order_items()
            .times(1)
            .returning(|_, items| Ok(items));

        mock_order_repo
            .expect_get_order_details()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(move |_| Ok(Some(order_details.clone())));

        let service = OrderServiceImpl::new(
            Arc::new(mock_order_repo),
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.create_order_from_request(1, request).await;
        assert!(result.is_ok());
        
        let order_details = result.unwrap();
        assert_eq!(order_details.order.user_id, 1);
        assert_eq!(order_details.items.len(), 1);
    }

    #[tokio::test]
    async fn test_create_order_insufficient_stock() {
        let mock_order_repo = MockOrderRepo::new();
        let mock_cart_repo = MockCartRepo::new();
        let mut mock_product_service = MockProductService::new();

        let request = CreateOrderRequest {
            items: vec![CreateOrderItem {
                product_id: 1,
                quantity: 200, // 超過庫存
            }],
        };

        // Mock product with insufficient stock
        mock_product_service
            .expect_get_product_by_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(Some(create_test_product()))); // 只有 100 庫存

        let service = OrderServiceImpl::new(
            Arc::new(mock_order_repo),
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.create_order_from_request(1, request).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Insufficient stock"));
    }

    #[tokio::test]
    async fn test_update_order_status_success() {
        let mut mock_order_repo = MockOrderRepo::new();
        let mock_cart_repo = MockCartRepo::new();
        let mock_product_service = MockProductService::new();

        let order = create_test_order();
        let order_details = create_test_order_details();

        mock_order_repo
            .expect_find_by_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(move |_| Ok(Some(order.clone())));

        mock_order_repo
            .expect_update_status()
            .times(1)
            .with(mockall::predicate::eq(1), mockall::predicate::eq(OrderStatus::Confirmed))
            .returning(|_, _| Ok(create_test_order()));

        mock_order_repo
            .expect_get_order_details()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(move |_| Ok(Some(order_details.clone())));

        let service = OrderServiceImpl::new(
            Arc::new(mock_order_repo),
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.update_order_status(1, 1, OrderStatus::Confirmed).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_update_order_status_invalid_transition() {
        let mut mock_order_repo = MockOrderRepo::new();
        let mock_cart_repo = MockCartRepo::new();
        let mock_product_service = MockProductService::new();

        let mut order = create_test_order();
        order.status = OrderStatus::Delivered; // 已完成的訂單

        mock_order_repo
            .expect_find_by_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(move |_| Ok(Some(order.clone())));

        let service = OrderServiceImpl::new(
            Arc::new(mock_order_repo),
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.update_order_status(1, 1, OrderStatus::Processing).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Cannot transition"));
    }

    #[tokio::test]
    async fn test_cancel_order_success() {
        let mut mock_order_repo = MockOrderRepo::new();
        let mock_cart_repo = MockCartRepo::new();
        let mut mock_product_service = MockProductService::new();

        let mut order = create_test_order();
        order.status = OrderStatus::Confirmed;
        let order_details = create_test_order_details();

        mock_order_repo
            .expect_find_by_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(move |_| Ok(Some(order.clone())));

        mock_order_repo
            .expect_get_order_items()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(vec![OrderItem {
                id: 1,
                order_id: 1,
                product_id: 1,
                quantity: 2,
                unit_price: dec!(50.0),
                subtotal: dec!(100.0),
            }]));

        mock_product_service
            .expect_get_product_by_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(Some(create_test_product())));

        mock_product_service
            .expect_update_stock()
            .times(1)
            .with(mockall::predicate::eq(1), mockall::predicate::eq(102)) // 恢復庫存
            .returning(|_, _| Ok(create_test_product()));

        mock_order_repo
            .expect_update_status()
            .times(1)
            .with(mockall::predicate::eq(1), mockall::predicate::eq(OrderStatus::Cancelled))
            .returning(|_, _| Ok(create_test_order()));

        mock_order_repo
            .expect_get_order_details()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(move |_| Ok(Some(order_details.clone())));

        let service = OrderServiceImpl::new(
            Arc::new(mock_order_repo),
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.cancel_order(1, 1).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_get_order_unauthorized() {
        let mut mock_order_repo = MockOrderRepo::new();
        let mock_cart_repo = MockCartRepo::new();
        let mock_product_service = MockProductService::new();

        let mut order_details = create_test_order_details();
        order_details.order.user_id = 2; // 不同的用戶

        mock_order_repo
            .expect_get_order_details()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(move |_| Ok(Some(order_details.clone())));

        let service = OrderServiceImpl::new(
            Arc::new(mock_order_repo),
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.get_order(1, 1).await; // 用戶 1 嘗試存取用戶 2 的訂單
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Access denied"));
    }

    #[tokio::test]
    async fn test_list_user_orders_with_filter() {
        let mut mock_order_repo = MockOrderRepo::new();
        let mock_cart_repo = MockCartRepo::new();
        let mock_product_service = MockProductService::new();

        let filter = OrderFilter {
            status: Some(OrderStatus::Pending),
            start_date: None,
            end_date: None,
            page: Some(1),
            limit: Some(10),
        };

        let orders = vec![create_test_order()];

        mock_order_repo
            .expect_list_by_user()
            .times(1)
            .with(
                mockall::predicate::eq(1),
                mockall::predicate::eq(Some(filter.clone())),
                mockall::predicate::eq(Some(10)),
                mockall::predicate::eq(Some(0)),
            )
            .returning(move |_, _, _, _| Ok(orders.clone()));

        let service = OrderServiceImpl::new(
            Arc::new(mock_order_repo),
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.list_user_orders(1, Some(filter), Some(1), Some(10)).await;
        assert!(result.is_ok());
        
        let orders = result.unwrap();
        assert_eq!(orders.len(), 1);
        assert_eq!(orders[0].status, OrderStatus::Pending);
    }

    #[tokio::test]
    async fn test_list_user_orders_pagination() {
        let mut mock_order_repo = MockOrderRepo::new();
        let mock_cart_repo = MockCartRepo::new();
        let mock_product_service = MockProductService::new();

        let orders = vec![create_test_order()];

        mock_order_repo
            .expect_list_by_user()
            .times(1)
            .with(
                mockall::predicate::eq(1),
                mockall::predicate::eq(None),
                mockall::predicate::eq(Some(20)),
                mockall::predicate::eq(Some(20)), // page 2, offset = (2-1) * 20
            )
            .returning(move |_, _, _, _| Ok(orders.clone()));

        let service = OrderServiceImpl::new(
            Arc::new(mock_order_repo),
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.list_user_orders(1, None, Some(2), Some(20)).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_get_order_count() {
        let mut mock_order_repo = MockOrderRepo::new();
        let mock_cart_repo = MockCartRepo::new();
        let mock_product_service = MockProductService::new();

        let filter = OrderFilter {
            status: Some(OrderStatus::Pending),
            start_date: None,
            end_date: None,
            page: None,
            limit: None,
        };

        mock_order_repo
            .expect_count_by_user()
            .times(1)
            .with(
                mockall::predicate::eq(1),
                mockall::predicate::eq(Some(filter.clone())),
            )
            .returning(|_, _| Ok(5));

        let service = OrderServiceImpl::new(
            Arc::new(mock_order_repo),
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.get_order_count(1, Some(filter)).await;
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), 5);
    }

    #[tokio::test]
    async fn test_get_order_by_number_success() {
        let mut mock_order_repo = MockOrderRepo::new();
        let mock_cart_repo = MockCartRepo::new();
        let mock_product_service = MockProductService::new();

        let order = create_test_order();
        let order_details = create_test_order_details();

        mock_order_repo
            .expect_find_by_order_number()
            .times(1)
            .with(mockall::predicate::eq("ORD-20240101-ABC123"))
            .returning(move |_| Ok(Some(order.clone())));

        mock_order_repo
            .expect_get_order_details()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(move |_| Ok(Some(order_details.clone())));

        let service = OrderServiceImpl::new(
            Arc::new(mock_order_repo),
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.get_order_by_number("ORD-20240101-ABC123", 1).await;
        assert!(result.is_ok());
        assert!(result.unwrap().is_some());
    }

    #[tokio::test]
    async fn test_get_order_by_number_not_found() {
        let mut mock_order_repo = MockOrderRepo::new();
        let mock_cart_repo = MockCartRepo::new();
        let mock_product_service = MockProductService::new();

        mock_order_repo
            .expect_find_by_order_number()
            .times(1)
            .with(mockall::predicate::eq("INVALID-ORDER"))
            .returning(|_| Ok(None));

        let service = OrderServiceImpl::new(
            Arc::new(mock_order_repo),
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.get_order_by_number("INVALID-ORDER", 1).await;
        assert!(result.is_ok());
        assert!(result.unwrap().is_none());
    }

    #[tokio::test]
    async fn test_get_order_by_number_unauthorized() {
        let mut mock_order_repo = MockOrderRepo::new();
        let mock_cart_repo = MockCartRepo::new();
        let mock_product_service = MockProductService::new();

        let mut order = create_test_order();
        order.user_id = 2; // 不同的用戶

        mock_order_repo
            .expect_find_by_order_number()
            .times(1)
            .with(mockall::predicate::eq("ORD-20240101-ABC123"))
            .returning(move |_| Ok(Some(order.clone())));

        let service = OrderServiceImpl::new(
            Arc::new(mock_order_repo),
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.get_order_by_number("ORD-20240101-ABC123", 1).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Access denied"));
    }

    #[tokio::test]
    async fn test_cancel_order_not_cancellable() {
        let mut mock_order_repo = MockOrderRepo::new();
        let mock_cart_repo = MockCartRepo::new();
        let mock_product_service = MockProductService::new();

        let mut order = create_test_order();
        order.status = OrderStatus::Delivered; // 已完成的訂單不可取消

        mock_order_repo
            .expect_find_by_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(move |_| Ok(Some(order.clone())));

        let service = OrderServiceImpl::new(
            Arc::new(mock_order_repo),
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.cancel_order(1, 1).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("cannot be cancelled"));
    }

    #[tokio::test]
    async fn test_cancel_order_pending_status() {
        let mut mock_order_repo = MockOrderRepo::new();
        let mock_cart_repo = MockCartRepo::new();
        let mock_product_service = MockProductService::new();

        let order = create_test_order(); // Pending 狀態
        let order_details = create_test_order_details();

        mock_order_repo
            .expect_find_by_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(move |_| Ok(Some(order.clone())));

        // Pending 狀態不需要恢復庫存，所以不會調用 get_order_items

        mock_order_repo
            .expect_update_status()
            .times(1)
            .with(mockall::predicate::eq(1), mockall::predicate::eq(OrderStatus::Cancelled))
            .returning(|_, _| Ok(create_test_order()));

        mock_order_repo
            .expect_get_order_details()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(move |_| Ok(Some(order_details.clone())));

        let service = OrderServiceImpl::new(
            Arc::new(mock_order_repo),
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.cancel_order(1, 1).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_create_order_inactive_product() {
        let mock_order_repo = MockOrderRepo::new();
        let mock_cart_repo = MockCartRepo::new();
        let mut mock_product_service = MockProductService::new();

        let request = CreateOrderRequest {
            items: vec![CreateOrderItem {
                product_id: 1,
                quantity: 2,
            }],
        };

        let mut product = create_test_product();
        product.is_active = false; // 產品未啟用

        mock_product_service
            .expect_get_product_by_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(move |_| Ok(Some(product.clone())));

        let service = OrderServiceImpl::new(
            Arc::new(mock_order_repo),
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.create_order_from_request(1, request).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("not active"));
    }

    #[tokio::test]
    async fn test_create_order_product_not_found() {
        let mock_order_repo = MockOrderRepo::new();
        let mock_cart_repo = MockCartRepo::new();
        let mut mock_product_service = MockProductService::new();

        let request = CreateOrderRequest {
            items: vec![CreateOrderItem {
                product_id: 999, // 不存在的產品
                quantity: 2,
            }],
        };

        mock_product_service
            .expect_get_product_by_id()
            .times(1)
            .with(mockall::predicate::eq(999))
            .returning(|_| Ok(None));

        let service = OrderServiceImpl::new(
            Arc::new(mock_order_repo),
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.create_order_from_request(1, request).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("not found"));
    }

    #[tokio::test]
    async fn test_create_order_from_cart_with_inactive_product() {
        let mock_order_repo = MockOrderRepo::new();
        let mut mock_cart_repo = MockCartRepo::new();
        let mut mock_product_service = MockProductService::new();

        let cart_details = create_test_cart_details();

        mock_cart_repo
            .expect_get_cart_details()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(move |_| Ok(Some(cart_details.clone())));

        let mut product = create_test_product();
        product.is_active = false; // 產品未啟用

        mock_product_service
            .expect_get_product_by_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(move |_| Ok(Some(product.clone())));

        let service = OrderServiceImpl::new(
            Arc::new(mock_order_repo),
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.create_order_from_cart(1).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("no longer active"));
    }

    #[tokio::test]
    async fn test_update_order_status_order_not_found() {
        let mut mock_order_repo = MockOrderRepo::new();
        let mock_cart_repo = MockCartRepo::new();
        let mock_product_service = MockProductService::new();

        mock_order_repo
            .expect_find_by_id()
            .times(1)
            .with(mockall::predicate::eq(999))
            .returning(|_| Ok(None));

        let service = OrderServiceImpl::new(
            Arc::new(mock_order_repo),
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.update_order_status(999, 1, OrderStatus::Confirmed).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Order not found"));
    }

    #[tokio::test]
    async fn test_get_order_not_found() {
        let mut mock_order_repo = MockOrderRepo::new();
        let mock_cart_repo = MockCartRepo::new();
        let mock_product_service = MockProductService::new();

        mock_order_repo
            .expect_get_order_details()
            .times(1)
            .with(mockall::predicate::eq(999))
            .returning(|_| Ok(None));

        let service = OrderServiceImpl::new(
            Arc::new(mock_order_repo),
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.get_order(999, 1).await;
        assert!(result.is_ok());
        assert!(result.unwrap().is_none());
    }
}