use crate::{
    models::User,
    repositories::{
        base::BaseRepository,
        user::{UserRepository, PostgresUserRepository, CreateUser},
        role::{RoleRepository, PostgresRoleRepository},
    },
    database::DbPool,
    error::AppError,
    config::Config,
};
use async_trait::async_trait;
use bcrypt::{hash, DEFAULT_COST};
use tracing::{info, warn, error};

#[async_trait]
pub trait AdminService: Send + Sync {
    #[allow(dead_code)]
    async fn initialize_super_admin(&self) -> Result<(), AppError>;
    async fn ensure_default_roles(&self) -> Result<(), AppError>;
    async fn create_super_admin_user(&self, username: &str, email: &str, password: &str, pharmacy_name: &str) -> Result<User, AppError>;
    async fn check_super_admin_exists(&self) -> Result<bool, AppError>;
}

pub struct AdminServiceImpl {
    user_repo: PostgresUserRepository,
    role_repo: PostgresRoleRepository,
    #[allow(dead_code)]
    config: Config,
}

impl AdminServiceImpl {
    #[allow(dead_code)]
    pub fn new(pool: DbPool, config: Config) -> Self {
        Self {
            user_repo: PostgresUserRepository::new(pool.clone()),
            role_repo: PostgresRoleRepository::new(pool),
            config,
        }
    }

    fn hash_password(&self, password: &str) -> Result<String, AppError> {
        hash(password, DEFAULT_COST)
            .map_err(|e| AppError::Internal(format!("Password hashing failed: {}", e)))
    }
}

#[async_trait]
impl AdminService for AdminServiceImpl {
    async fn initialize_super_admin(&self) -> Result<(), AppError> {
        info!("開始初始化管理員...");

        // 1. 首先確保預設角色存在
        self.ensure_default_roles().await?;

        // 2. 檢查是否已存在管理員
        if self.check_super_admin_exists().await? {
            info!("管理員已存在，跳過初始化");
            return Ok(());
        }

        // 3. 從環境變數或配置中獲取管理員資訊
        let admin_username = std::env::var("ADMIN_USERNAME")
            .unwrap_or_else(|_| "admin".to_string());
        let admin_email = std::env::var("ADMIN_EMAIL")
            .unwrap_or_else(|_| "<EMAIL>".to_string());
        let admin_password = std::env::var("ADMIN_PASSWORD")
            .unwrap_or_else(|_| "Admin123!".to_string());
        let admin_pharmacy_name = std::env::var("ADMIN_PHARMACY_NAME")
            .unwrap_or_else(|_| "HappyOrder 總部".to_string());

        // 4. 創建管理員用戶
        let admin_user = self.create_super_admin_user(
            &admin_username,
            &admin_email,
            &admin_password,
            &admin_pharmacy_name,
        ).await?;

        info!(
            "管理員初始化成功！用戶ID: {}, 用戶名: {}, 電郵: {}",
            admin_user.id, admin_user.username, admin_user.email
        );

        warn!(
            "⚠️  預設管理員資訊：\n用戶名: {}\n電郵: {}\n密碼: {}\n請立即登入並更改密碼！",
            admin_username, admin_email, admin_password
        );

        Ok(())
    }

    async fn ensure_default_roles(&self) -> Result<(), AppError> {
        info!("檢查並確保預設角色存在...");

        let default_roles = [
            ("admin", "管理員 - 擁有所有權限"),
            ("pharmacy", "藥局用戶 - 可以下訂單和查看自己的資料"),
            ("viewer", "檢視者 - 只能查看資料"),
        ];

        for (role_name, description) in &default_roles {
            match self.role_repo.find_role_by_name(role_name).await {
                Ok(Some(_)) => {
                    info!("角色 '{}' 已存在", role_name);
                }
                Ok(None) => {
                    info!("創建預設角色: {}", role_name);
                    self.role_repo.create_role(role_name, Some(description)).await
                        .map_err(AppError::from)?;
                }
                Err(e) => {
                    error!("檢查角色 '{}' 時發生錯誤: {:?}", role_name, e);
                    return Err(AppError::from(e));
                }
            }
        }

        Ok(())
    }

    async fn create_super_admin_user(&self, username: &str, email: &str, password: &str, pharmacy_name: &str) -> Result<User, AppError> {
        info!("創建管理員用戶: {}", username);

        // 檢查用戶名是否已存在
        if let Some(_) = self.user_repo.find_by_username(username).await.map_err(AppError::from)? {
            return Err(AppError::Validation("Username already exists".to_string()));
        }

        // 檢查電子郵件是否已存在
        if let Some(_) = self.user_repo.find_by_email(email).await.map_err(AppError::from)? {
            return Err(AppError::Validation("Email already exists".to_string()));
        }

        // 獲取管理員角色 ID
        let admin_role = self.role_repo.find_role_by_name("admin").await
            .map_err(AppError::from)?
            .ok_or_else(|| AppError::NotFound("Admin role not found".to_string()))?;

        // 雜湊密碼
        let password_hash = self.hash_password(password)?;

        // 創建用戶
        let create_user = CreateUser {
            username: username.to_string(),
            email: email.to_string(),
            password_hash,
            pharmacy_name: pharmacy_name.to_string(),
            phone: None,
            line_user_id: None,
            role_id: Some(admin_role.id),
            contact_person: Some("系統管理員".to_string()),
            mobile: None,
            institution_code: Some("ADMIN".to_string()),
            address: Some("系統內建管理員".to_string()),
        };

        let user = self.user_repo.create_user(create_user).await.map_err(AppError::from)?;

        info!("超級管理員用戶創建成功: ID {}", user.id);
        Ok(user)
    }

    async fn check_super_admin_exists(&self) -> Result<bool, AppError> {
        // 獲取管理員角色
        let admin_role = match self.role_repo.find_role_by_name("admin").await.map_err(AppError::from)? {
            Some(role) => role,
            None => return Ok(false), // 如果角色不存在，說明還沒有管理員
        };

        // 檢查是否有用戶擁有管理員角色
        // 這裡需要在用戶倉庫中添加一個按角色查找用戶的方法
        // 暫時使用簡單的方法：查詢所有用戶並檢查角色ID
        let users = self.user_repo.list(Some(100), None).await.map_err(AppError::from)?;
        
        for user in users {
            if user.role_id == admin_role.id {
                info!("發現管理員用戶: {} (ID: {})", user.username, user.id);
                return Ok(true);
            }
        }

        info!("未發現管理員用戶");
        Ok(false)
    }
}

// 初始化函數，可以在應用啟動時調用
#[allow(dead_code)]
pub async fn initialize_system(pool: DbPool, config: Config) -> Result<(), AppError> {
    info!("開始系統初始化...");
    
    let admin_service = AdminServiceImpl::new(pool, config);
    
    // 初始化超級管理員
    admin_service.initialize_super_admin().await?;
    
    info!("系統初始化完成");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::database::Database;
    use tempfile::NamedTempFile;

    async fn create_test_admin_service() -> AdminServiceImpl {
        // 使用 PostgreSQL 測試資料庫
        let database_url = std::env::var("TEST_DATABASE_URL")
            .expect("TEST_DATABASE_URL must be set for cloud database testing");
        let database = Database::new(&database_url).await.unwrap();
        
        let config = crate::config::Config {
            database_url: database_url.clone(),
            jwt_secret: "test_secret_key_for_jwt_testing_12345".to_string(),
            server_port: 8080,
            email: crate::config::EmailConfig {
                smtp_host: "smtp.test.com".to_string(),
                smtp_port: 587,
                smtp_username: "<EMAIL>".to_string(),
                smtp_password: "test_password".to_string(),
                from_email: "<EMAIL>".to_string(),
            },
            line: crate::config::LineConfig {
                channel_access_token: "test_token".to_string(),
                channel_secret: "test_secret".to_string(),
            },
            gcp: crate::config::GcpConfig {
                project_id: "test_project".to_string(),
                storage_bucket: "test_bucket".to_string(),
                credentials_path: None,
            },
        };

        AdminServiceImpl::new(database.pool().clone(), config)
    }

    #[tokio::test]
    async fn test_ensure_default_roles() {
        let admin_service = create_test_admin_service().await;
        
        let result = admin_service.ensure_default_roles().await;
        assert!(result.is_ok());

        // 檢查角色是否創建成功
        let super_admin_role = admin_service.role_repo.find_role_by_name("super_admin").await.unwrap();
        assert!(super_admin_role.is_some());
        
        let admin_role = admin_service.role_repo.find_role_by_name("admin").await.unwrap();
        assert!(admin_role.is_some());
    }

    #[tokio::test]
    async fn test_create_super_admin_user() {
        let admin_service = create_test_admin_service().await;
        
        // 先確保角色存在
        admin_service.ensure_default_roles().await.unwrap();
        
        let result = admin_service.create_super_admin_user(
            "testadmin",
            "<EMAIL>",
            "TestPassword123!",
            "測試總部"
        ).await;
        
        assert!(result.is_ok());
        let user = result.unwrap();
        assert_eq!(user.username, "testadmin");
        assert_eq!(user.email, "<EMAIL>");
    }

    #[tokio::test]
    async fn test_check_super_admin_exists() {
        let admin_service = create_test_admin_service().await;
        
        // 初始情況下應該沒有超級管理員
        let exists_before = admin_service.check_super_admin_exists().await.unwrap();
        assert!(!exists_before);
        
        // 創建超級管理員後應該存在
        admin_service.ensure_default_roles().await.unwrap();
        admin_service.create_super_admin_user(
            "testadmin",
            "<EMAIL>",
            "TestPassword123!",
            "測試總部"
        ).await.unwrap();
        
        let exists_after = admin_service.check_super_admin_exists().await.unwrap();
        assert!(exists_after);
    }

    #[tokio::test]
    async fn test_initialize_super_admin() {
        let admin_service = create_test_admin_service().await;
        
        let result = admin_service.initialize_super_admin().await;
        assert!(result.is_ok());
        
        // 檢查超級管理員是否創建成功
        let exists = admin_service.check_super_admin_exists().await.unwrap();
        assert!(exists);
        
        // 再次初始化應該跳過
        let result2 = admin_service.initialize_super_admin().await;
        assert!(result2.is_ok());
    }
}