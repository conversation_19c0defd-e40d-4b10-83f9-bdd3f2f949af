use std::sync::Arc;
use std::time::Instant;
use async_trait::async_trait;
use serde::{Serialize, Deserialize};
use tracing::{info, warn, error, debug};

use crate::error::{AppError, AppResult};
use crate::models::product::{Product, ProductFilter, ImportResult, ProductImportData};
use crate::repositories::product::ProductRepository;
use crate::services::file_processing::FileProcessingService;
use crate::logging::structured_logging;

#[async_trait]
pub trait ProductService: Send + Sync {
    async fn import_from_excel(&self, file_data: Vec<u8>) -> AppResult<ImportResult>;
    async fn import_from_csv(&self, file_data: Vec<u8>) -> AppResult<ImportResult>;
    async fn get_products(&self, filter: Option<ProductFilter>, page: Option<u32>, limit: Option<u32>) -> AppR<PERSON>ult<Vec<Product>>;
    async fn get_product_by_id(&self, id: i64) -> AppResult<Option<Product>>;
    async fn get_product_by_nhi_code(&self, nhi_code: &str) -> AppResult<Option<Product>>;
    async fn create_product(
        &self, 
        name: String, 
        nhi_code: Option<String>, 
        dosage_form: Option<String>,
        manufacturer: Option<String>,
        nhi_price: Option<f64>,
        unit_price: Option<f64>,
        stock_quantity: i32,
        unit: Option<String>,
        description: Option<String>,
        ingredients: Option<String>,
    ) -> AppResult<Product>;
    async fn update_product(
        &self,
        id: i64,
        name: Option<String>,
        nhi_code: Option<String>,
        dosage_form: Option<String>,
        manufacturer: Option<String>,
        nhi_price: Option<f64>,
        unit_price: Option<f64>,
        stock_quantity: Option<i32>,
        unit: Option<String>,
        description: Option<String>,
        ingredients: Option<String>,
    ) -> AppResult<Product>;
    async fn delete_product(&self, id: i64) -> AppResult<bool>;
    async fn update_stock(&self, product_id: i64, quantity: i32) -> AppResult<Product>;
    async fn get_low_stock_products(&self) -> AppResult<Vec<Product>>;
    async fn check_stock_availability(&self, product_id: i64, required_quantity: i32) -> AppResult<bool>;
    async fn get_stock_status(&self, product_id: i64) -> AppResult<StockStatus>;
    async fn bulk_update_stock(&self, updates: Vec<StockUpdate>) -> AppResult<Vec<Product>>;
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StockStatus {
    pub product_id: i64,
    pub current_stock: i32,
    pub is_available: bool,
    pub is_low_stock: bool,
    pub low_stock_threshold: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StockUpdate {
    pub product_id: i64,
    pub quantity: i32,
    pub operation: StockOperation,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StockOperation {
    Set,      // 設定絕對數量
    Add,      // 增加庫存
    Subtract, // 減少庫存
}

pub struct ProductServiceImpl {
    product_repository: Arc<dyn ProductRepository>,
    file_processing_service: Arc<dyn FileProcessingService>,
}

impl ProductServiceImpl {
    pub fn new(
        product_repository: Arc<dyn ProductRepository>,
        file_processing_service: Arc<dyn FileProcessingService>,
    ) -> Self {
        Self {
            product_repository,
            file_processing_service,
        }
    }

    /// 處理匯入結果並儲存到資料庫
    async fn process_import_data(&self, products: Vec<ProductImportData>) -> AppResult<ImportResult> {
        let start_time = Instant::now();
        
        if products.is_empty() {
            warn!("嘗試匯入空的產品資料");
            return Ok(ImportResult {
                total_rows: 0,
                imported_count: 0,
                error_count: 0,
                errors: Vec::new(),
            });
        }

        let total_rows = products.len();
        info!(
            total_products = total_rows,
            "開始處理產品匯入資料"
        );
        
        match self.product_repository.bulk_upsert(products).await {
            Ok(imported_count) => {
                let duration = start_time.elapsed();
                
                // 記錄資料庫操作
                structured_logging::log_database_operation(
                    "bulk_upsert",
                    "products",
                    duration.as_millis() as u64,
                    Some(imported_count as u64),
                    None,
                );
                
                info!(
                    imported_count = imported_count,
                    total_rows = total_rows,
                    duration_ms = duration.as_millis(),
                    "產品匯入成功完成"
                );
                
                Ok(ImportResult {
                    total_rows,
                    imported_count,
                    error_count: 0,
                    errors: Vec::new(),
                })
            }
            Err(e) => {
                let duration = start_time.elapsed();
                
                error!(
                    error = %e,
                    total_rows = total_rows,
                    duration_ms = duration.as_millis(),
                    "產品匯入失敗"
                );
                
                Ok(ImportResult {
                    total_rows,
                    imported_count: 0,
                    error_count: total_rows,
                    errors: vec![format!("資料庫匯入失敗: {}", e)],
                })
            }
        }
    }
}

#[async_trait]
impl ProductService for ProductServiceImpl {
    async fn import_from_excel(&self, file_data: Vec<u8>) -> AppResult<ImportResult> {
        let start_time = Instant::now();
        let file_size = file_data.len() as u64;
        
        info!(
            file_size = file_size,
            "開始 Excel 檔案匯入處理"
        );
        
        // 使用檔案處理服務解析 Excel 檔案
        let parse_result = self.file_processing_service.parse_excel_data(file_data).await;
        
        match parse_result {
            Ok(products) => {
                let parse_duration = start_time.elapsed();
                debug!(
                    parsed_products = products.len(),
                    parse_duration_ms = parse_duration.as_millis(),
                    "Excel 檔案解析完成"
                );
                
                // 處理匯入資料並儲存到資料庫
                let result = self.process_import_data(products).await;
                let total_duration = start_time.elapsed();
                
                // 記錄檔案處理操作
                structured_logging::log_file_processing(
                    "excel_import",
                    "excel_file",
                    Some(file_size),
                    total_duration.as_millis() as u64,
                    result.is_ok(),
                    result.as_ref().ok().map(|r| r.imported_count as u64),
                    result.as_ref().ok().map(|r| r.error_count as u64),
                    None,
                );
                
                result
            }
            Err(e) => {
                let duration = start_time.elapsed();
                
                // 記錄檔案處理失敗
                structured_logging::log_file_processing(
                    "excel_import",
                    "excel_file",
                    Some(file_size),
                    duration.as_millis() as u64,
                    false,
                    None,
                    None,
                    None,
                );
                
                error!(
                    error = %e,
                    file_size = file_size,
                    duration_ms = duration.as_millis(),
                    "Excel 檔案解析失敗"
                );
                
                Err(e)
            }
        }
    }

    async fn import_from_csv(&self, file_data: Vec<u8>) -> AppResult<ImportResult> {
        let start_time = Instant::now();
        let file_size = file_data.len() as u64;
        
        info!(
            file_size = file_size,
            "開始 CSV 檔案匯入處理"
        );
        
        // 使用檔案處理服務解析 CSV 檔案
        let parse_result = self.file_processing_service.parse_csv_data(file_data).await;
        
        match parse_result {
            Ok(products) => {
                let parse_duration = start_time.elapsed();
                debug!(
                    parsed_products = products.len(),
                    parse_duration_ms = parse_duration.as_millis(),
                    "CSV 檔案解析完成"
                );
                
                // 處理匯入資料並儲存到資料庫
                let result = self.process_import_data(products).await;
                let total_duration = start_time.elapsed();
                
                // 記錄檔案處理操作
                structured_logging::log_file_processing(
                    "csv_import",
                    "csv_file",
                    Some(file_size),
                    total_duration.as_millis() as u64,
                    result.is_ok(),
                    result.as_ref().ok().map(|r| r.imported_count as u64),
                    result.as_ref().ok().map(|r| r.error_count as u64),
                    None,
                );
                
                result
            }
            Err(e) => {
                let duration = start_time.elapsed();
                
                // 記錄檔案處理失敗
                structured_logging::log_file_processing(
                    "csv_import",
                    "csv_file",
                    Some(file_size),
                    duration.as_millis() as u64,
                    false,
                    None,
                    None,
                    None,
                );
                
                error!(
                    error = %e,
                    file_size = file_size,
                    duration_ms = duration.as_millis(),
                    "CSV 檔案解析失敗"
                );
                
                Err(e)
            }
        }
    }

    async fn get_products(&self, filter: Option<ProductFilter>, page: Option<u32>, limit: Option<u32>) -> AppResult<Vec<Product>> {
        let limit = limit.unwrap_or(50) as i32;
        let page = page.unwrap_or(1);
        let offset = ((page - 1) * limit as u32) as i32;

        self.product_repository.list(filter, Some(limit), Some(offset)).await
    }

    async fn get_product_by_id(&self, id: i64) -> AppResult<Option<Product>> {
        self.product_repository.find_by_id(id).await
    }

    async fn get_product_by_nhi_code(&self, nhi_code: &str) -> AppResult<Option<Product>> {
        self.product_repository.find_by_nhi_code(nhi_code).await
    }

    async fn create_product(
        &self, 
        name: String, 
        nhi_code: Option<String>, 
        dosage_form: Option<String>,
        manufacturer: Option<String>,
        _nhi_price: Option<f64>,
        unit_price: Option<f64>,
        stock_quantity: i32,
        unit: Option<String>,
        description: Option<String>,
        ingredients: Option<String>,
    ) -> AppResult<Product> {
        use crate::models::product::CreateProduct;

        let create_product = CreateProduct {
            nhi_code: nhi_code.unwrap_or_default(),
            name,
            english_name: None,
            ingredients,
            dosage_form,
            manufacturer: manufacturer.unwrap_or_default(),
            unit: unit.unwrap_or_else(|| "盒".to_string()),
            selling_price: unit_price.unwrap_or(0.0),
            stock_quantity,
            description,
        };

        self.product_repository.create(create_product).await
    }

    async fn update_product(
        &self,
        id: i64,
        name: Option<String>,
        nhi_code: Option<String>,
        dosage_form: Option<String>,
        manufacturer: Option<String>,
        _nhi_price: Option<f64>,
        unit_price: Option<f64>,
        stock_quantity: Option<i32>,
        unit: Option<String>,
        description: Option<String>,
        ingredients: Option<String>,
    ) -> AppResult<Product> {
        use crate::models::product::UpdateProduct;

        let update_product = UpdateProduct {
            nhi_code,
            name,
            english_name: None,
            ingredients,
            dosage_form,
            manufacturer,
            unit,
            selling_price: unit_price,
            stock_quantity,
            description,
            is_active: None,
        };

        self.product_repository.update(id, update_product).await
    }

    async fn delete_product(&self, id: i64) -> AppResult<bool> {
        self.product_repository.delete(id).await
    }

    async fn update_stock(&self, product_id: i64, quantity: i32) -> AppResult<Product> {
        if quantity < 0 {
            return Err(AppError::Validation("Stock quantity cannot be negative".to_string()));
        }

        self.product_repository.update_stock(product_id, quantity).await
    }

    async fn get_low_stock_products(&self) -> AppResult<Vec<Product>> {
        self.product_repository.find_low_stock().await
    }

    async fn check_stock_availability(&self, product_id: i64, required_quantity: i32) -> AppResult<bool> {
        let product = self.product_repository.find_by_id(product_id).await?
            .ok_or_else(|| AppError::NotFound(format!("Product with id {} not found", product_id)))?;
        
        Ok(product.has_sufficient_stock(required_quantity))
    }

    async fn get_stock_status(&self, product_id: i64) -> AppResult<StockStatus> {
        let product = self.product_repository.find_by_id(product_id).await?
            .ok_or_else(|| AppError::NotFound(format!("Product with id {} not found", product_id)))?;
        
        let low_stock_threshold = 10; // 可以從配置中讀取
        
        Ok(StockStatus {
            product_id,
            current_stock: product.stock_quantity,
            is_available: product.is_in_stock(),
            is_low_stock: product.is_low_stock(low_stock_threshold),
            low_stock_threshold,
        })
    }

    async fn bulk_update_stock(&self, updates: Vec<StockUpdate>) -> AppResult<Vec<Product>> {
        let mut updated_products = Vec::new();
        
        for update in updates {
            let product = self.product_repository.find_by_id(update.product_id).await?
                .ok_or_else(|| AppError::NotFound(format!("Product with id {} not found", update.product_id)))?;
            
            let new_quantity = match update.operation {
                StockOperation::Set => update.quantity,
                StockOperation::Add => product.stock_quantity + update.quantity,
                StockOperation::Subtract => {
                    let result = product.stock_quantity - update.quantity;
                    if result < 0 {
                        return Err(AppError::Validation(
                            format!("Insufficient stock for product {}: requested {}, available {}", 
                                update.product_id, update.quantity, product.stock_quantity)
                        ));
                    }
                    result
                }
            };
            
            if new_quantity < 0 {
                return Err(AppError::Validation("Stock quantity cannot be negative".to_string()));
            }
            
            let updated_product = self.product_repository.update_stock(update.product_id, new_quantity).await?;
            updated_products.push(updated_product);
        }
        
        Ok(updated_products)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::models::product::ProductImportData;
    use crate::repositories::product::ProductRepository;
    use crate::services::file_processing::FileProcessingService;
    use async_trait::async_trait;
    use mockall::mock;
    use rust_decimal_macros::dec;

    mock! {
        ProductRepo {}

        #[async_trait]
        impl ProductRepository for ProductRepo {
            async fn create(&self, product: crate::models::product::CreateProduct) -> Result<Product, AppError>;
            async fn find_by_id(&self, id: i64) -> Result<Option<Product>, AppError>;
            async fn find_by_nhi_code(&self, nhi_code: &str) -> Result<Option<Product>, AppError>;
            async fn update(&self, id: i64, product: crate::models::product::UpdateProduct) -> Result<Product, AppError>;
            async fn delete(&self, id: i64) -> Result<bool, AppError>;
            async fn list(&self, filter: Option<ProductFilter>, limit: Option<i32>, offset: Option<i32>) -> Result<Vec<Product>, AppError>;
            async fn update_stock(&self, id: i64, quantity: i32) -> Result<Product, AppError>;
            async fn find_low_stock(&self) -> Result<Vec<Product>, AppError>;
            async fn bulk_upsert(&self, products: Vec<ProductImportData>) -> Result<usize, AppError>;
        }
    }

    mock! {
        FileProcessingService {}

        #[async_trait]
        impl FileProcessingService for FileProcessingService {
            async fn process_excel_file(&self, file_data: Vec<u8>) -> AppResult<ImportResult>;
            async fn process_csv_file(&self, file_data: Vec<u8>) -> AppResult<ImportResult>;
            async fn parse_excel_data(&self, file_data: Vec<u8>) -> AppResult<Vec<ProductImportData>>;
            async fn parse_csv_data(&self, file_data: Vec<u8>) -> AppResult<Vec<ProductImportData>>;
        }
    }

    #[tokio::test]
    async fn test_import_from_csv_success() {
        let mut mock_repo = MockProductRepo::new();
        let mut mock_file_service = MockFileProcessingService::new();

        // 設定 mock 期望
        mock_file_service
            .expect_parse_csv_data()
            .times(1)
            .returning(|_| {
                Ok(vec![
                    ProductImportData {
                        nhi_code: "A001".to_string(),
                        name: "Product1".to_string(),
                        manufacturer: "Manufacturer1".to_string(),
                        unit: "盒".to_string(),
                        unit_price: dec!(50.0),
                        stock_quantity: 100,
                        description: Some("Description1".to_string()),
                    }
                ])
            });

        mock_repo
            .expect_bulk_upsert()
            .times(1)
            .returning(|products| Ok(products.len()));

        let service = ProductServiceImpl::new(
            Arc::new(mock_repo),
            Arc::new(mock_file_service),
        );

        let csv_data = "nhi_code,name,manufacturer,unit,unit_price,stock_quantity,description\nA001,Product1,Manufacturer1,盒,50.0,100,Description1".as_bytes().to_vec();
        
        let result = service.import_from_csv(csv_data).await;
        assert!(result.is_ok());
        
        let import_result = result.unwrap();
        assert_eq!(import_result.total_rows, 1);
        assert_eq!(import_result.imported_count, 1);
        assert_eq!(import_result.error_count, 0);
    }

    #[tokio::test]
    async fn test_import_from_excel_success() {
        let mut mock_repo = MockProductRepo::new();
        let mut mock_file_service = MockFileProcessingService::new();

        // 設定 mock 期望
        mock_file_service
            .expect_parse_excel_data()
            .times(1)
            .returning(|_| {
                Ok(vec![
                    ProductImportData {
                        nhi_code: "A001".to_string(),
                        name: "Product1".to_string(),
                        manufacturer: "Manufacturer1".to_string(),
                        unit: "盒".to_string(),
                        unit_price: dec!(50.0),
                        stock_quantity: 100,
                        description: Some("Description1".to_string()),
                    }
                ])
            });

        mock_repo
            .expect_bulk_upsert()
            .times(1)
            .returning(|products| Ok(products.len()));

        let service = ProductServiceImpl::new(
            Arc::new(mock_repo),
            Arc::new(mock_file_service),
        );

        let excel_data = vec![1, 2, 3, 4]; // 假的 Excel 資料
        
        let result = service.import_from_excel(excel_data).await;
        assert!(result.is_ok());
        
        let import_result = result.unwrap();
        assert_eq!(import_result.total_rows, 1);
        assert_eq!(import_result.imported_count, 1);
        assert_eq!(import_result.error_count, 0);
    }

    #[tokio::test]
    async fn test_update_stock_negative_quantity() {
        let mock_repo = MockProductRepo::new();
        let mock_file_service = MockFileProcessingService::new();

        let service = ProductServiceImpl::new(
            Arc::new(mock_repo),
            Arc::new(mock_file_service),
        );

        let result = service.update_stock(1, -10).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Stock quantity cannot be negative"));
    }

    #[tokio::test]
    async fn test_get_products_with_pagination() {
        let mut mock_repo = MockProductRepo::new();
        let mock_file_service = MockFileProcessingService::new();

        mock_repo
            .expect_list()
            .times(1)
            .withf(|filter, limit, offset| {
                filter.is_none() && *limit == Some(20) && *offset == Some(20)
            })
            .returning(|_, _, _| Ok(Vec::new()));

        let service = ProductServiceImpl::new(
            Arc::new(mock_repo),
            Arc::new(mock_file_service),
        );

        let result = service.get_products(None, Some(2), Some(20)).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_get_products_with_search_filter() {
        let mut mock_repo = MockProductRepo::new();
        let mock_file_service = MockFileProcessingService::new();

        let filter = ProductFilter {
            search: Some("阿司匹林".to_string()),
            manufacturer: None,
            is_active: Some(true),
            page: Some(1),
            limit: Some(10),
        };

        mock_repo
            .expect_list()
            .times(1)
            .withf(move |f, limit, offset| {
                if let Some(filter) = f {
                    filter.search.as_ref().unwrap() == "阿司匹林" &&
                    filter.is_active == Some(true) &&
                    *limit == Some(10) && *offset == Some(0)
                } else {
                    false
                }
            })
            .returning(|_, _, _| {
                Ok(vec![
                    Product {
                        id: 1,
                        nhi_code: "A001".to_string(),
                        name: "阿司匹林".to_string(),
                        manufacturer: "台廠".to_string(),
                        unit: "盒".to_string(),
                        unit_price: dec!(50.0),
                        stock_quantity: 100,
                        description: Some("止痛藥".to_string()),
                        is_active: true,
                        created_at: chrono::Utc::now(),
                        updated_at: chrono::Utc::now(),
                    }
                ])
            });

        let service = ProductServiceImpl::new(
            Arc::new(mock_repo),
            Arc::new(mock_file_service),
        );

        let result = service.get_products(Some(filter), Some(1), Some(10)).await;
        assert!(result.is_ok());
        let products = result.unwrap();
        assert_eq!(products.len(), 1);
        assert_eq!(products[0].name, "阿司匹林");
    }

    #[tokio::test]
    async fn test_get_products_with_manufacturer_filter() {
        let mut mock_repo = MockProductRepo::new();
        let mock_file_service = MockFileProcessingService::new();

        let filter = ProductFilter {
            search: None,
            manufacturer: Some("台廠".to_string()),
            is_active: None,
            page: None,
            limit: None,
        };

        mock_repo
            .expect_list()
            .times(1)
            .withf(move |f, limit, offset| {
                if let Some(filter) = f {
                    filter.manufacturer.as_ref().unwrap() == "台廠" &&
                    *limit == Some(50) && *offset == Some(0)
                } else {
                    false
                }
            })
            .returning(|_, _, _| Ok(Vec::new()));

        let service = ProductServiceImpl::new(
            Arc::new(mock_repo),
            Arc::new(mock_file_service),
        );

        let result = service.get_products(Some(filter), None, None).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_get_low_stock_products() {
        let mut mock_repo = MockProductRepo::new();
        let mock_file_service = MockFileProcessingService::new();

        mock_repo
            .expect_find_low_stock()
            .times(1)
            .returning(|| {
                Ok(vec![
                    Product {
                        id: 1,
                        nhi_code: "A001".to_string(),
                        name: "低庫存藥品".to_string(),
                        manufacturer: "台廠".to_string(),
                        unit: "盒".to_string(),
                        unit_price: dec!(50.0),
                        stock_quantity: 5, // 低庫存
                        description: None,
                        is_active: true,
                        created_at: chrono::Utc::now(),
                        updated_at: chrono::Utc::now(),
                    }
                ])
            });

        let service = ProductServiceImpl::new(
            Arc::new(mock_repo),
            Arc::new(mock_file_service),
        );

        let result = service.get_low_stock_products().await;
        assert!(result.is_ok());
        let products = result.unwrap();
        assert_eq!(products.len(), 1);
        assert_eq!(products[0].stock_quantity, 5);
    }

    #[tokio::test]
    async fn test_get_product_by_nhi_code() {
        let mut mock_repo = MockProductRepo::new();
        let mock_file_service = MockFileProcessingService::new();

        mock_repo
            .expect_find_by_nhi_code()
            .times(1)
            .with(mockall::predicate::eq("A001234567"))
            .returning(|_| {
                Ok(Some(Product {
                    id: 1,
                    nhi_code: "A001234567".to_string(),
                    name: "阿司匹林".to_string(),
                    manufacturer: "台廠".to_string(),
                    unit: "盒".to_string(),
                    unit_price: dec!(50.0),
                    stock_quantity: 100,
                    description: Some("止痛藥".to_string()),
                    is_active: true,
                    created_at: chrono::Utc::now(),
                    updated_at: chrono::Utc::now(),
                }))
            });

        let service = ProductServiceImpl::new(
            Arc::new(mock_repo),
            Arc::new(mock_file_service),
        );

        let result = service.get_product_by_nhi_code("A001234567").await;
        assert!(result.is_ok());
        let product = result.unwrap();
        assert!(product.is_some());
        assert_eq!(product.unwrap().nhi_code, "A001234567");
    }

    #[tokio::test]
    async fn test_check_stock_availability_sufficient() {
        let mut mock_repo = MockProductRepo::new();
        let mock_file_service = MockFileProcessingService::new();

        mock_repo
            .expect_find_by_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| {
                Ok(Some(Product {
                    id: 1,
                    nhi_code: "A001".to_string(),
                    name: "阿司匹林".to_string(),
                    manufacturer: "台廠".to_string(),
                    unit: "盒".to_string(),
                    unit_price: dec!(50.0),
                    stock_quantity: 100,
                    description: None,
                    is_active: true,
                    created_at: chrono::Utc::now(),
                    updated_at: chrono::Utc::now(),
                }))
            });

        let service = ProductServiceImpl::new(
            Arc::new(mock_repo),
            Arc::new(mock_file_service),
        );

        let result = service.check_stock_availability(1, 50).await;
        assert!(result.is_ok());
        assert!(result.unwrap());
    }

    #[tokio::test]
    async fn test_check_stock_availability_insufficient() {
        let mut mock_repo = MockProductRepo::new();
        let mock_file_service = MockFileProcessingService::new();

        mock_repo
            .expect_find_by_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| {
                Ok(Some(Product {
                    id: 1,
                    nhi_code: "A001".to_string(),
                    name: "阿司匹林".to_string(),
                    manufacturer: "台廠".to_string(),
                    unit: "盒".to_string(),
                    unit_price: dec!(50.0),
                    stock_quantity: 10,
                    description: None,
                    is_active: true,
                    created_at: chrono::Utc::now(),
                    updated_at: chrono::Utc::now(),
                }))
            });

        let service = ProductServiceImpl::new(
            Arc::new(mock_repo),
            Arc::new(mock_file_service),
        );

        let result = service.check_stock_availability(1, 50).await;
        assert!(result.is_ok());
        assert!(!result.unwrap());
    }

    #[tokio::test]
    async fn test_get_stock_status() {
        let mut mock_repo = MockProductRepo::new();
        let mock_file_service = MockFileProcessingService::new();

        mock_repo
            .expect_find_by_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| {
                Ok(Some(Product {
                    id: 1,
                    nhi_code: "A001".to_string(),
                    name: "阿司匹林".to_string(),
                    manufacturer: "台廠".to_string(),
                    unit: "盒".to_string(),
                    unit_price: dec!(50.0),
                    stock_quantity: 5, // 低庫存
                    description: None,
                    is_active: true,
                    created_at: chrono::Utc::now(),
                    updated_at: chrono::Utc::now(),
                }))
            });

        let service = ProductServiceImpl::new(
            Arc::new(mock_repo),
            Arc::new(mock_file_service),
        );

        let result = service.get_stock_status(1).await;
        assert!(result.is_ok());
        
        let status = result.unwrap();
        assert_eq!(status.product_id, 1);
        assert_eq!(status.current_stock, 5);
        assert!(status.is_available);
        assert!(status.is_low_stock);
        assert_eq!(status.low_stock_threshold, 10);
    }

    #[tokio::test]
    async fn test_bulk_update_stock_success() {
        let mut mock_repo = MockProductRepo::new();
        let mock_file_service = MockFileProcessingService::new();

        // 設定第一個產品的查詢
        mock_repo
            .expect_find_by_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| {
                Ok(Some(Product {
                    id: 1,
                    nhi_code: "A001".to_string(),
                    name: "阿司匹林".to_string(),
                    manufacturer: "台廠".to_string(),
                    unit: "盒".to_string(),
                    unit_price: dec!(50.0),
                    stock_quantity: 100,
                    description: None,
                    is_active: true,
                    created_at: chrono::Utc::now(),
                    updated_at: chrono::Utc::now(),
                }))
            });

        // 設定庫存更新
        mock_repo
            .expect_update_stock()
            .times(1)
            .with(mockall::predicate::eq(1), mockall::predicate::eq(80))
            .returning(|_, _| {
                Ok(Product {
                    id: 1,
                    nhi_code: "A001".to_string(),
                    name: "阿司匹林".to_string(),
                    manufacturer: "台廠".to_string(),
                    unit: "盒".to_string(),
                    unit_price: dec!(50.0),
                    stock_quantity: 80,
                    description: None,
                    is_active: true,
                    created_at: chrono::Utc::now(),
                    updated_at: chrono::Utc::now(),
                })
            });

        let service = ProductServiceImpl::new(
            Arc::new(mock_repo),
            Arc::new(mock_file_service),
        );

        let updates = vec![
            StockUpdate {
                product_id: 1,
                quantity: 20,
                operation: StockOperation::Subtract,
            }
        ];

        let result = service.bulk_update_stock(updates).await;
        assert!(result.is_ok());
        
        let updated_products = result.unwrap();
        assert_eq!(updated_products.len(), 1);
        assert_eq!(updated_products[0].stock_quantity, 80);
    }

    #[tokio::test]
    async fn test_bulk_update_stock_insufficient() {
        let mut mock_repo = MockProductRepo::new();
        let mock_file_service = MockFileProcessingService::new();

        mock_repo
            .expect_find_by_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| {
                Ok(Some(Product {
                    id: 1,
                    nhi_code: "A001".to_string(),
                    name: "阿司匹林".to_string(),
                    manufacturer: "台廠".to_string(),
                    unit: "盒".to_string(),
                    unit_price: dec!(50.0),
                    stock_quantity: 10, // 庫存不足
                    description: None,
                    is_active: true,
                    created_at: chrono::Utc::now(),
                    updated_at: chrono::Utc::now(),
                }))
            });

        let service = ProductServiceImpl::new(
            Arc::new(mock_repo),
            Arc::new(mock_file_service),
        );

        let updates = vec![
            StockUpdate {
                product_id: 1,
                quantity: 50, // 要求減少 50，但只有 10
                operation: StockOperation::Subtract,
            }
        ];

        let result = service.bulk_update_stock(updates).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Insufficient stock"));
    }
}