use async_trait::async_trait;
use chrono::{DateTime, Utc};
use tracing::{info, warn};
use validator::Validate;

use crate::{
    database::DbPool,
    error::AppError,
    models::{Promotion, CreatePromotionRequest, UpdatePromotionRequest, PromotionListResponse},
    repositories::{
        base::BaseRepository,
        promotion::{PromotionRepository, PostgresPromotionRepository},
        user::PostgresUserRepository,
    },
};

#[async_trait]
pub trait PromotionService: Send + Sync {
    async fn create_promotion(&self, request: CreatePromotionRequest, created_by: i64) -> Result<Promotion, AppError>;
    async fn update_promotion(&self, id: i64, request: UpdatePromotionRequest, user_id: i64) -> Result<Promotion, AppError>;
    async fn delete_promotion(&self, id: i64, user_id: i64) -> Result<bool, AppError>;
    async fn get_promotion(&self, id: i64) -> Result<Option<Promotion>, AppError>;
    async fn get_promotions_for_user(&self, user_id: i64, page: i64, limit: i64) -> Result<PromotionListResponse, AppError>;
    async fn get_active_promotions(&self, user_id: i64, page: i64, limit: i64) -> Result<PromotionListResponse, AppError>;
    async fn mark_as_read(&self, promotion_id: i64, user_id: i64) -> Result<(), AppError>;
    async fn get_unread_count(&self, user_id: i64) -> Result<i64, AppError>;
    async fn get_all_promotions(&self, page: i64, limit: i64) -> Result<PromotionListResponse, AppError>;
}

pub struct PromotionServiceImpl {
    promotion_repo: PostgresPromotionRepository,
    user_repo: PostgresUserRepository,
}

impl PromotionServiceImpl {
    pub fn new(pool: DbPool) -> Self {
        Self {
            promotion_repo: PostgresPromotionRepository::new(pool.clone()),
            user_repo: PostgresUserRepository::new(pool),
        }
    }

    async fn check_admin_permission(&self, user_id: i64) -> Result<(), AppError> {
        let user = self.user_repo.find_by_id(user_id).await
            .map_err(AppError::from)?
            .ok_or_else(|| AppError::NotFound("用戶不存在".to_string()))?;

        // 檢查用戶角色是否為管理員或超級管理員
        // 這裡假設角色 ID: 1 = super_admin, 2 = admin
        if user.role_id != 1 && user.role_id != 2 {
            return Err(AppError::Forbidden("權限不足：僅管理員可以操作促銷訊息".to_string()));
        }

        Ok(())
    }

    fn validate_promotion_dates(&self, starts_at: Option<DateTime<Utc>>, ends_at: Option<DateTime<Utc>>) -> Result<(), AppError> {
        if let (Some(start), Some(end)) = (starts_at, ends_at) {
            if start >= end {
                return Err(AppError::Validation("結束時間必須晚於開始時間".to_string()));
            }
        }
        Ok(())
    }
}

#[async_trait]
impl PromotionService for PromotionServiceImpl {
    async fn create_promotion(&self, request: CreatePromotionRequest, created_by: i64) -> Result<Promotion, AppError> {
        // 檢查權限
        self.check_admin_permission(created_by).await?;

        // 驗證日期
        self.validate_promotion_dates(request.starts_at, request.ends_at)?;

        // 驗證請求數據
        request.validate().map_err(|e| {
            warn!("促銷訊息創建請求驗證失敗: {}", e);
            AppError::Validation(format!("請求驗證失敗: {}", e))
        })?;

        let promotion = self.promotion_repo.create_promotion(request, created_by).await
            .map_err(AppError::from)?;

        info!("促銷訊息創建成功: ID {}, 創建者: {}", promotion.id, created_by);
        Ok(promotion)
    }

    async fn update_promotion(&self, id: i64, request: UpdatePromotionRequest, user_id: i64) -> Result<Promotion, AppError> {
        // 檢查權限
        self.check_admin_permission(user_id).await?;

        // 檢查促銷訊息是否存在
        let existing = self.promotion_repo.find_by_id(id).await
            .map_err(AppError::from)?
            .ok_or_else(|| AppError::NotFound("促銷訊息不存在".to_string()))?;

        // 驗證日期
        let starts_at = request.starts_at.or(existing.starts_at);
        let ends_at = request.ends_at.or(existing.ends_at);
        self.validate_promotion_dates(starts_at, ends_at)?;

        // 驗證請求數據
        request.validate().map_err(|e| {
            warn!("促銷訊息更新請求驗證失敗: {}", e);
            AppError::Validation(format!("請求驗證失敗: {}", e))
        })?;

        let promotion = self.promotion_repo.update_promotion(id, request).await
            .map_err(AppError::from)?;

        info!("促銷訊息更新成功: ID {}, 更新者: {}", promotion.id, user_id);
        Ok(promotion)
    }

    async fn delete_promotion(&self, id: i64, user_id: i64) -> Result<bool, AppError> {
        // 檢查權限
        self.check_admin_permission(user_id).await?;

        // 檢查促銷訊息是否存在
        self.promotion_repo.find_by_id(id).await
            .map_err(AppError::from)?
            .ok_or_else(|| AppError::NotFound("促銷訊息不存在".to_string()))?;

        let deleted = self.promotion_repo.delete_promotion(id).await
            .map_err(AppError::from)?;

        if deleted {
            info!("促銷訊息刪除成功: ID {}, 刪除者: {}", id, user_id);
        }

        Ok(deleted)
    }

    async fn get_promotion(&self, id: i64) -> Result<Option<Promotion>, AppError> {
        self.promotion_repo.find_by_id(id).await
            .map_err(AppError::from)
    }

    async fn get_promotions_for_user(&self, user_id: i64, page: i64, limit: i64) -> Result<PromotionListResponse, AppError> {
        let offset = (page - 1) * limit;
        
        let (promotions, total) = self.promotion_repo
            .find_promotions_with_read_status(user_id, Some(limit), Some(offset))
            .await
            .map_err(AppError::from)?;

        Ok(PromotionListResponse {
            promotions,
            total,
            page,
            limit,
        })
    }

    async fn get_active_promotions(&self, user_id: i64, page: i64, limit: i64) -> Result<PromotionListResponse, AppError> {
        let offset = (page - 1) * limit;
        
        let promotions = self.promotion_repo
            .find_active_promotions(user_id, Some(limit), Some(offset))
            .await
            .map_err(AppError::from)?;

        // 獲取總數（這裡簡化處理，實際可能需要單獨查詢活躍促銷的總數）
        let total = promotions.len() as i64;

        Ok(PromotionListResponse {
            promotions,
            total,
            page,
            limit,
        })
    }

    async fn mark_as_read(&self, promotion_id: i64, user_id: i64) -> Result<(), AppError> {
        // 檢查促銷訊息是否存在
        self.promotion_repo.find_by_id(promotion_id).await
            .map_err(AppError::from)?
            .ok_or_else(|| AppError::NotFound("促銷訊息不存在".to_string()))?;

        self.promotion_repo.mark_as_read(promotion_id, user_id).await
            .map_err(AppError::from)
    }

    async fn get_unread_count(&self, user_id: i64) -> Result<i64, AppError> {
        self.promotion_repo.get_unread_count(user_id).await
            .map_err(AppError::from)
    }

    async fn get_all_promotions(&self, page: i64, limit: i64) -> Result<PromotionListResponse, AppError> {
        let offset = (page - 1) * limit;
        
        // 對於管理員，使用一個假的用戶 ID 來獲取所有促銷訊息
        let (promotions, total) = self.promotion_repo
            .find_promotions_with_read_status(-1, Some(limit), Some(offset))
            .await
            .map_err(AppError::from)?;

        Ok(PromotionListResponse {
            promotions,
            total,
            page,
            limit,
        })
    }
}