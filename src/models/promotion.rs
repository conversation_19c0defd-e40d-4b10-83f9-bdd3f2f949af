use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use chrono::{DateTime, Utc};
use validator::Validate;

#[derive(Debug, Serialize, Deserialize, FromRow, Clone)]
pub struct Promotion {
    pub id: i64,
    pub title: String,
    pub content: String,
    pub promotion_type: String,
    pub priority: i32,
    pub target_audience: String,
    pub is_active: bool,
    pub starts_at: Option<DateTime<Utc>>,
    pub ends_at: Option<DateTime<Utc>>,
    pub created_by: i64,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct PromotionRead {
    pub id: i64,
    pub promotion_id: i64,
    pub user_id: i64,
    pub read_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct CreatePromotionRequest {
    #[validate(length(min = 1, max = 200, message = "標題長度必須在1到200個字符之間"))]
    pub title: String,
    
    #[validate(length(min = 1, message = "內容不能為空"))]
    pub content: String,
    
    #[serde(default = "default_promotion_type")]
    pub promotion_type: String,
    
    #[serde(default = "default_priority")]
    pub priority: i32,
    
    #[serde(default = "default_target_audience")]
    pub target_audience: String,
    
    pub starts_at: Option<DateTime<Utc>>,
    pub ends_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct UpdatePromotionRequest {
    #[validate(length(min = 1, max = 200, message = "標題長度必須在1到200個字符之間"))]
    pub title: Option<String>,
    
    #[validate(length(min = 1, message = "內容不能為空"))]
    pub content: Option<String>,
    
    pub promotion_type: Option<String>,
    pub priority: Option<i32>,
    pub target_audience: Option<String>,
    pub is_active: Option<bool>,
    pub starts_at: Option<DateTime<Utc>>,
    pub ends_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PromotionWithReadStatus {
    #[serde(flatten)]
    pub promotion: Promotion,
    pub is_read: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PromotionListResponse {
    pub promotions: Vec<PromotionWithReadStatus>,
    pub total: i64,
    pub page: i64,
    pub limit: i64,
}

// 預設值函數
fn default_promotion_type() -> String {
    "general".to_string()
}

fn default_priority() -> i32 {
    1
}

fn default_target_audience() -> String {
    "all".to_string()
}

// 促銷類型枚舉
#[derive(Debug, Serialize, Deserialize, PartialEq)]
pub enum PromotionType {
    General,    // 一般公告
    Discount,   // 優惠促銷
    Welcome,    // 歡迎訊息
    System,     // 系統通知
}

impl From<String> for PromotionType {
    fn from(s: String) -> Self {
        match s.as_str() {
            "discount" => PromotionType::Discount,
            "welcome" => PromotionType::Welcome,
            "system" => PromotionType::System,
            _ => PromotionType::General,
        }
    }
}

impl From<PromotionType> for String {
    fn from(pt: PromotionType) -> Self {
        match pt {
            PromotionType::General => "general".to_string(),
            PromotionType::Discount => "discount".to_string(),
            PromotionType::Welcome => "welcome".to_string(),
            PromotionType::System => "system".to_string(),
        }
    }
}

// 目標受眾枚舉
#[derive(Debug, Serialize, Deserialize, PartialEq)]
pub enum TargetAudience {
    All,        // 所有用戶
    Pharmacy,   // 藥局用戶
    Admin,      // 管理員
}

impl From<String> for TargetAudience {
    fn from(s: String) -> Self {
        match s.as_str() {
            "pharmacy" => TargetAudience::Pharmacy,
            "admin" => TargetAudience::Admin,
            _ => TargetAudience::All,
        }
    }
}

impl From<TargetAudience> for String {
    fn from(ta: TargetAudience) -> Self {
        match ta {
            TargetAudience::All => "all".to_string(),
            TargetAudience::Pharmacy => "pharmacy".to_string(),
            TargetAudience::Admin => "admin".to_string(),
        }
    }
}