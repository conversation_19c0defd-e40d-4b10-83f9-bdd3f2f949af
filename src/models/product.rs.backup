use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize, Serializer};
use crate::models::date_format;
// use crate::models::NhiPrice; // 暫時註釋，稍後使用

// 自定義序列化函數，將 Decimal 格式化為最多兩位小數
fn serialize_price<S>(price: &Decimal, serializer: S) -> Result<S::Ok, S::Error>
where
    S: Serializer,
{
    let formatted = format!("{:.2}", price);
    serializer.serialize_str(&formatted)
}

// 可選價格的序列化函數
fn serialize_optional_price<S>(price: &Option<Decimal>, serializer: S) -> Result<S::Ok, S::Error>
where
    S: Serializer,
{
    match price {
        Some(p) => {
            let formatted = format!("{:.2}", p);
            serializer.serialize_some(&formatted)
        },
        None => serializer.serialize_none(),
    }
}
use crate::models::validation::{
    Validate, ValidationError, ValidationResult,
    validate_required_string, validate_string_length, validate_nhi_code,
    validate_positive_decimal, validate_non_negative_i32
};

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct Product {
    pub id: i64,
    pub nhi_code: String,  // 健保代碼
    pub name: String,      // 中文品名
    pub english_name: Option<String>, // 英文品名
    pub ingredients: Option<String>, // 成分含量
    pub dosage_form: Option<String>, // 規格
    pub manufacturer: String,
    pub unit: String,
    #[serde(serialize_with = "serialize_price")]
    pub selling_price: Decimal,  // 改名：從 unit_price 改為 selling_price
    pub stock_quantity: i32,
    pub description: Option<String>,
    pub is_active: bool,
    #[serde(with = "date_format::date_only_format")]
    pub created_at: DateTime<Utc>,
    #[serde(with = "date_format::date_only_format")]
    pub updated_at: DateTime<Utc>,
}

// 包含健保價格資訊的產品結構
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProductWithNhiPrice {
    #[serde(flatten)]
    pub product: Product,
    #[serde(serialize_with = "serialize_optional_price")]
    pub nhi_price: Option<Decimal>,  // 從 NhiPrice 表獲取的健保給付價格
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProductFilter {
    pub search: Option<String>,
    pub manufacturer: Option<String>,
    pub is_active: Option<bool>,
    pub page: Option<u32>,
    pub limit: Option<u32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ImportResult {
    pub total_rows: usize,
    pub imported_count: usize,
    pub error_count: usize,
    pub errors: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProductImportData {
    pub nhi_code: String,
    pub name: String,
    pub english_name: Option<String>,
    pub ingredients: Option<String>,
    pub dosage_form: Option<String>,
    pub manufacturer: String,
    pub unit: String,
    pub selling_price: Decimal,
    pub stock_quantity: i32,
    pub description: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateProduct {
    pub nhi_code: String,
    pub name: String,
    pub english_name: Option<String>,
    pub ingredients: Option<String>,
    pub dosage_form: Option<String>,
    pub manufacturer: String,
    pub unit: String,
    pub selling_price: f64,  // 改名：從 unit_price 改為 selling_price
    pub stock_quantity: i32,
    pub description: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateProduct {
    pub nhi_code: Option<String>,
    pub name: Option<String>,
    pub english_name: Option<String>,
    pub ingredients: Option<String>,
    pub dosage_form: Option<String>,
    pub manufacturer: Option<String>,
    pub unit: Option<String>,
    pub selling_price: Option<f64>,  // 改名：從 unit_price 改為 selling_price
    pub stock_quantity: Option<i32>,
    pub description: Option<String>,
    pub is_active: Option<bool>,
}

#[allow(dead_code)]
impl Product {
    pub fn new(
        nhi_code: String,
        name: String,
        english_name: Option<String>,
        ingredients: Option<String>,
        dosage_form: Option<String>,
        manufacturer: String,
        unit: String,
        selling_price: Decimal,
        stock_quantity: i32,
        description: Option<String>,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // Will be set by database
            nhi_code,
            name,
            english_name,
            ingredients,
            dosage_form,
            manufacturer,
            unit,
            selling_price,
            stock_quantity,
            description,
            is_active: true,
            created_at: now,
            updated_at: now,
        }
    }

    pub fn update_stock(&mut self, new_quantity: i32) -> ValidationResult<()> {
        validate_non_negative_i32(new_quantity, "stock_quantity")?;
        self.stock_quantity = new_quantity;
        self.updated_at = Utc::now();
        Ok(())
    }

    pub fn adjust_stock(&mut self, adjustment: i32) -> ValidationResult<()> {
        let new_quantity = self.stock_quantity + adjustment;
        validate_non_negative_i32(new_quantity, "stock_quantity")?;
        self.stock_quantity = new_quantity;
        self.updated_at = Utc::now();
        Ok(())
    }

    pub fn is_in_stock(&self) -> bool {
        self.stock_quantity > 0 && self.is_active
    }

    pub fn has_sufficient_stock(&self, required_quantity: i32) -> bool {
        self.stock_quantity >= required_quantity && self.is_active
    }

    pub fn deactivate(&mut self) {
        self.is_active = false;
        self.updated_at = Utc::now();
    }

    pub fn activate(&mut self) {
        self.is_active = true;
        self.updated_at = Utc::now();
    }

    /// 檢查是否為低庫存 (庫存量小於指定閾值)
    pub fn is_low_stock(&self, threshold: i32) -> bool {
        self.stock_quantity < threshold && self.is_active
    }

    /// 預留庫存 (減少可用庫存但不實際出貨)
    pub fn reserve_stock(&mut self, quantity: i32) -> ValidationResult<()> {
        if !self.has_sufficient_stock(quantity) {
            return Err(ValidationError::InvalidValue {
                field: "stock_quantity".to_string(),
                value: format!("Insufficient stock: requested {}, available {}", quantity, self.stock_quantity),
            });
        }
        self.stock_quantity -= quantity;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 釋放預留庫存
    pub fn release_reserved_stock(&mut self, quantity: i32) -> ValidationResult<()> {
        validate_non_negative_i32(quantity, "quantity")?;
        self.stock_quantity += quantity;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 計算庫存價值
    pub fn calculate_stock_value(&self) -> Decimal {
        self.selling_price * Decimal::from(self.stock_quantity)
    }

    /// 檢查健保代碼格式是否有效
    pub fn validate_nhi_code_format(&self) -> ValidationResult<()> {
        validate_nhi_code(&self.nhi_code)
    }
}

impl Validate for Product {
    fn validate(&self) -> Result<(), Vec<ValidationError>> {
        let mut errors = Vec::new();

        if let Err(e) = validate_nhi_code(&self.nhi_code) {
            errors.push(e);
        }

        if let Err(e) = validate_required_string(&self.name, "name") {
            errors.push(e);
        }

        if let Err(e) = validate_string_length(&self.name, "name", 1, 200) {
            errors.push(e);
        }

        if let Err(e) = validate_required_string(&self.manufacturer, "manufacturer") {
            errors.push(e);
        }

        if let Err(e) = validate_string_length(&self.manufacturer, "manufacturer", 1, 100) {
            errors.push(e);
        }

        if let Err(e) = validate_required_string(&self.unit, "unit") {
            errors.push(e);
        }

        if let Err(e) = validate_string_length(&self.unit, "unit", 1, 20) {
            errors.push(e);
        }

        if let Err(e) = validate_positive_decimal(&self.unit_price, "unit_price") {
            errors.push(e);
        }

        if let Err(e) = validate_non_negative_i32(self.stock_quantity, "stock_quantity") {
            errors.push(e);
        }

        if let Some(ref desc) = self.description {
            if let Err(e) = validate_string_length(desc, "description", 0, 1000) {
                errors.push(e);
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

impl Validate for ProductImportData {
    fn validate(&self) -> Result<(), Vec<ValidationError>> {
        let mut errors = Vec::new();

        if let Err(e) = validate_nhi_code(&self.nhi_code) {
            errors.push(e);
        }

        if let Err(e) = validate_required_string(&self.name, "name") {
            errors.push(e);
        }

        if let Err(e) = validate_string_length(&self.name, "name", 1, 200) {
            errors.push(e);
        }

        if let Err(e) = validate_required_string(&self.manufacturer, "manufacturer") {
            errors.push(e);
        }

        if let Err(e) = validate_string_length(&self.manufacturer, "manufacturer", 1, 100) {
            errors.push(e);
        }

        if let Err(e) = validate_required_string(&self.unit, "unit") {
            errors.push(e);
        }

        if let Err(e) = validate_string_length(&self.unit, "unit", 1, 20) {
            errors.push(e);
        }

        if let Err(e) = validate_positive_decimal(&self.unit_price, "unit_price") {
            errors.push(e);
        }

        if let Err(e) = validate_non_negative_i32(self.stock_quantity, "stock_quantity") {
            errors.push(e);
        }

        if let Some(ref desc) = self.description {
            if let Err(e) = validate_string_length(desc, "description", 0, 1000) {
                errors.push(e);
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::Utc;
    use rust_decimal_macros::dec;

    #[test]
    fn test_product_new() {
        let product = Product::new(
            "A001".to_string(),
            "阿司匹林".to_string(),
            None,
            Some("Acetylsalicylic Acid 100mg".to_string()),
            Some("錠劑".to_string()),
            "台廠".to_string(),
            "盒".to_string(),
            dec!(50.0),
            100,
            Some("止痛藥".to_string()),
        );

        assert_eq!(product.nhi_code, "A001");
        assert_eq!(product.name, "阿司匹林");
        assert_eq!(product.manufacturer, "台廠");
        assert_eq!(product.unit, "盒");
        assert_eq!(product.unit_price, dec!(50.0));
        assert_eq!(product.stock_quantity, 100);
        assert_eq!(product.description, Some("止痛藥".to_string()));
        assert!(product.is_active);
    }

    #[test]
    fn test_product_validation_success() {
        let product = Product {
            id: 1,
            nhi_code: "A001".to_string(),
            name: "阿司匹林".to_string(),
            english_name: None,
            ingredients: Some("Acetylsalicylic Acid 100mg".to_string()),
            dosage_form: Some("錠劑".to_string()),
            manufacturer: "台廠".to_string(),
            unit: "盒".to_string(),
            unit_price: dec!(50.0),
            nhi_code_ref: None,
            stock_quantity: 100,
            description: Some("止痛藥".to_string()),
            is_active: true,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        assert!(product.validate().is_ok());
    }

    #[test]
    fn test_product_validation_failure() {
        let product = Product {
            id: 1,
            nhi_code: "123".to_string(), // 無效 NHI 代碼
            name: "".to_string(), // 空名稱
            english_name: None,
            ingredients: None,
            dosage_form: None,
            manufacturer: "".to_string(), // 空製造商
            unit: "".to_string(), // 空單位
            unit_price: dec!(-10.0), // 負價格
            nhi_code_ref: None,
            stock_quantity: -5, // 負庫存
            description: None,
            is_active: true,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        let errors = product.validate().unwrap_err();
        assert!(errors.len() >= 5);
    }

    #[test]
    fn test_product_stock_management() {
        let mut product = Product::new(
            "A001".to_string(),
            "阿司匹林".to_string(),
            None,
            Some("Acetylsalicylic Acid 100mg".to_string()),
            Some("錠劑".to_string()),
            "台廠".to_string(),
            "盒".to_string(),
            dec!(50.0),
            100,
            None,
        );

        // 測試庫存更新
        assert!(product.update_stock(80).is_ok());
        assert_eq!(product.stock_quantity, 80);

        // 測試庫存調整
        assert!(product.adjust_stock(20).is_ok());
        assert_eq!(product.stock_quantity, 100);

        // 測試負數調整
        assert!(product.adjust_stock(-30).is_ok());
        assert_eq!(product.stock_quantity, 70);

        // 測試無效庫存
        assert!(product.update_stock(-10).is_err());
    }

    #[test]
    fn test_product_stock_status() {
        let mut product = Product::new(
            "A001".to_string(),
            "阿司匹林".to_string(),
            None,
            Some("Acetylsalicylic Acid 100mg".to_string()),
            Some("錠劑".to_string()),
            "台廠".to_string(),
            "盒".to_string(),
            dec!(50.0),
            100,
            None,
        );

        assert!(product.is_in_stock());
        assert!(product.has_sufficient_stock(50));
        assert!(product.has_sufficient_stock(100));
        assert!(!product.has_sufficient_stock(150));

        // 測試無庫存
        product.update_stock(0).unwrap();
        assert!(!product.is_in_stock());
        assert!(!product.has_sufficient_stock(1));

        // 測試停用產品
        product.deactivate();
        assert!(!product.is_in_stock());
        assert!(!product.has_sufficient_stock(1));

        // 測試重新啟用
        product.activate();
        product.update_stock(50).unwrap();
        assert!(product.is_in_stock());
    }

    #[test]
    fn test_product_import_data_validation() {
        let valid_data = ProductImportData {
            nhi_code: "A001".to_string(),
            name: "阿司匹林".to_string(),
            manufacturer: "台廠".to_string(),
            unit: "盒".to_string(),
            unit_price: dec!(50.0),
            stock_quantity: 100,
            description: Some("止痛藥".to_string()),
        };

        assert!(valid_data.validate().is_ok());

        let invalid_data = ProductImportData {
            nhi_code: "123".to_string(), // 無效 NHI 代碼
            name: "".to_string(), // 空名稱
            manufacturer: "".to_string(), // 空製造商
            unit: "".to_string(), // 空單位
            unit_price: dec!(-10.0), // 負價格
            stock_quantity: -5, // 負庫存
            description: None,
        };

        let errors = invalid_data.validate().unwrap_err();
        assert!(errors.len() >= 5);
    }

    #[test]
    fn test_product_low_stock_detection() {
        let mut product = Product::new(
            "A001".to_string(),
            "阿司匹林".to_string(),
            None,
            Some("Acetylsalicylic Acid 100mg".to_string()),
            Some("錠劑".to_string()),
            "台廠".to_string(),
            "盒".to_string(),
            dec!(50.0),
            15,
            None,
        );

        // 測試低庫存檢測
        assert!(product.is_low_stock(20)); // 15 < 20
        assert!(!product.is_low_stock(10)); // 15 >= 10
        assert!(!product.is_low_stock(15)); // 15 >= 15

        // 停用產品不應該被視為低庫存
        product.deactivate();
        assert!(!product.is_low_stock(20));
    }

    #[test]
    fn test_product_stock_reservation() {
        let mut product = Product::new(
            "A001".to_string(),
            "阿司匹林".to_string(),
            None,
            Some("Acetylsalicylic Acid 100mg".to_string()),
            Some("錠劑".to_string()),
            "台廠".to_string(),
            "盒".to_string(),
            dec!(50.0),
            100,
            None,
        );

        // 測試正常預留庫存
        assert!(product.reserve_stock(30).is_ok());
        assert_eq!(product.stock_quantity, 70);

        // 測試預留超過可用庫存
        assert!(product.reserve_stock(80).is_err());
        assert_eq!(product.stock_quantity, 70); // 庫存不應該改變

        // 測試釋放預留庫存
        assert!(product.release_reserved_stock(20).is_ok());
        assert_eq!(product.stock_quantity, 90);

        // 測試釋放負數庫存
        assert!(product.release_reserved_stock(-10).is_err());
    }

    #[test]
    fn test_product_stock_value_calculation() {
        let product = Product::new(
            "A001".to_string(),
            "阿司匹林".to_string(),
            None,
            Some("Acetylsalicylic Acid 100mg".to_string()),
            Some("錠劑".to_string()),
            "台廠".to_string(),
            "盒".to_string(),
            dec!(25.50),
            100,
            None,
        );

        let expected_value = dec!(25.50) * Decimal::from(100);
        assert_eq!(product.calculate_stock_value(), expected_value);

        // 測試零庫存
        let mut zero_stock_product = product.clone();
        zero_stock_product.stock_quantity = 0;
        assert_eq!(zero_stock_product.calculate_stock_value(), Decimal::ZERO);
    }

    #[test]
    fn test_nhi_code_validation() {
        let mut product = Product::new(
            "A001234567".to_string(),
            "阿司匹林".to_string(),
            None,
            Some("Acetylsalicylic Acid 100mg".to_string()),
            Some("錠劑".to_string()),
            "台廠".to_string(),
            "盒".to_string(),
            dec!(50.0),
            100,
            None,
        );

        // 測試有效的健保代碼
        assert!(product.validate_nhi_code_format().is_ok());

        // 測試無效的健保代碼
        product.nhi_code = "invalid".to_string();
        assert!(product.validate_nhi_code_format().is_err());

        product.nhi_code = "123".to_string(); // 太短
        assert!(product.validate_nhi_code_format().is_err());

        product.nhi_code = "A001234567890123".to_string(); // 太長
        assert!(product.validate_nhi_code_format().is_err());
    }

    #[test]
    fn test_product_comprehensive_validation() {
        // 測試所有欄位的驗證
        let product = Product {
            id: 1,
            nhi_code: "A001234567".to_string(),
            name: "阿司匹林錠劑".to_string(),
            english_name: Some("Aspirin Tablet".to_string()),
            ingredients: Some("Acetylsalicylic Acid 500mg".to_string()),
            dosage_form: Some("錠劑".to_string()),
            manufacturer: "台灣製藥公司".to_string(),
            unit: "盒".to_string(),
            unit_price: dec!(125.50),
            nhi_code_ref: None,
            stock_quantity: 50,
            description: Some("用於緩解輕至中度疼痛".to_string()),
            is_active: true,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        assert!(product.validate().is_ok());

        // 測試邊界值
        let boundary_product = Product {
            id: 1,
            nhi_code: "ABCD".to_string(), // 最短有效長度
            name: "A".to_string(), // 最短有效名稱
            english_name: None,
            ingredients: None,
            dosage_form: None,
            manufacturer: "M".to_string(), // 最短有效製造商
            unit: "U".to_string(), // 最短有效單位
            unit_price: dec!(0.01), // 最小正數價格
            nhi_code_ref: None,
            stock_quantity: 0, // 最小有效庫存
            description: None,
            is_active: true,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        assert!(boundary_product.validate().is_ok());
    }

    #[test]
    fn test_product_edge_cases() {
        let mut product = Product::new(
            "A001".to_string(),
            "阿司匹林".to_string(),
            None,
            Some("Acetylsalicylic Acid 100mg".to_string()),
            Some("錠劑".to_string()),
            "台廠".to_string(),
            "盒".to_string(),
            dec!(50.0),
            1, // 只有1個庫存
            None,
        );

        // 測試剛好足夠的庫存
        assert!(product.has_sufficient_stock(1));
        assert!(!product.has_sufficient_stock(2));

        // 測試預留最後一個庫存
        assert!(product.reserve_stock(1).is_ok());
        assert_eq!(product.stock_quantity, 0);
        assert!(!product.is_in_stock());

        // 測試釋放庫存後恢復
        assert!(product.release_reserved_stock(1).is_ok());
        assert_eq!(product.stock_quantity, 1);
        assert!(product.is_in_stock());
    }
}