use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct Message {
    pub id: i32,
    pub title: String,
    pub content: String,
    pub message_type: String,
    pub status: String,
    pub target_audience: Option<String>,
    pub priority: Option<i32>,
    pub starts_at: Option<DateTime<Utc>>,
    pub ends_at: Option<DateTime<Utc>>,
    pub created_by: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct MessageRead {
    pub id: i32,
    pub message_id: i32,
    pub user_id: i32,
    pub read_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateMessageRequest {
    pub title: String,
    pub content: String,
    pub message_type: String,
    pub target_audience: Option<String>,
    pub priority: Option<i32>,
    pub status: Option<String>,
    pub starts_at: Option<DateTime<Utc>>,
    pub ends_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MessageResponse {
    pub id: i32,
    pub title: String,
    pub content: String,
    pub message_type: String,
    pub status: String,
    pub target_audience: Option<String>,
    pub priority: Option<i32>,
    pub starts_at: Option<DateTime<Utc>>,
    pub ends_at: Option<DateTime<Utc>>,
    pub created_by: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub is_read: bool,
}