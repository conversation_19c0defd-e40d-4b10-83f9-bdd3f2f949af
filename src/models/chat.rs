use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct ChatMessage {
    pub id: i64,
    pub user_id: String,
    pub user_name: String,
    pub admin_id: Option<String>,
    pub admin_name: Option<String>,
    pub message: String,
    pub message_type: String, // 'user' or 'admin'
    pub replied: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub status: String, // 'sent', 'read', 'resolved'
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SendMessageRequest {
    pub message: String,
    pub message_type: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AdminReplyRequest {
    pub user_id: String,
    pub message: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateMessageStatusRequest {
    pub status: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ChatMessageResponse {
    pub id: i64,
    pub user_id: String,
    pub user_name: String,
    pub admin_id: Option<String>,
    pub admin_name: Option<String>,
    pub message: String,
    pub message_type: String,
    pub replied: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub status: String,
}

impl From<ChatMessage> for ChatMessageResponse {
    fn from(chat_message: ChatMessage) -> Self {
        Self {
            id: chat_message.id,
            user_id: chat_message.user_id,
            user_name: chat_message.user_name,
            admin_id: chat_message.admin_id,
            admin_name: chat_message.admin_name,
            message: chat_message.message,
            message_type: chat_message.message_type,
            replied: chat_message.replied,
            created_at: chat_message.created_at,
            updated_at: chat_message.updated_at,
            status: chat_message.status,
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ChatStats {
    pub total_conversations: i64,
    pub unread_messages: i64,
    pub pending_replies: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ChatConversation {
    pub user_id: String,
    pub user_name: String,
    pub last_message: String,
    pub last_message_time: DateTime<Utc>,
    pub unread_count: i64,
    pub status: String,
}