use chrono::{DateTime, Utc};
use serde::{Deserialize, Deserializer, Serializer};

/// 自定義日期序列化格式：只顯示日期部分 (YYYY-MM-DD)
pub mod date_only_format {
    use super::*;

    pub fn serialize<S>(date: &DateTime<Utc>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        let date_string = date.format("%Y-%m-%d").to_string();
        serializer.serialize_str(&date_string)
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<DateTime<Utc>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        // 如果只有日期，添加時間部分
        let full_datetime = if s.contains('T') {
            s
        } else {
            format!("{}T00:00:00Z", s)
        };
        
        DateTime::parse_from_rfc3339(&full_datetime)
            .map(|dt| dt.with_timezone(&Utc))
            .map_err(serde::de::Error::custom)
    }
}

/// 可選日期的序列化格式
pub mod optional_date_only_format {
    use super::*;

    #[allow(dead_code)]
    pub fn serialize<S>(date: &Option<DateTime<Utc>>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        match date {
            Some(d) => {
                let date_string = d.format("%Y-%m-%d").to_string();
                serializer.serialize_some(&date_string)
            }
            None => serializer.serialize_none(),
        }
    }

    #[allow(dead_code)]
    pub fn deserialize<'de, D>(deserializer: D) -> Result<Option<DateTime<Utc>>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let opt = Option::<String>::deserialize(deserializer)?;
        match opt {
            Some(s) => {
                let full_datetime = if s.contains('T') {
                    s
                } else {
                    format!("{}T00:00:00Z", s)
                };
                
                DateTime::parse_from_rfc3339(&full_datetime)
                    .map(|dt| Some(dt.with_timezone(&Utc)))
                    .map_err(serde::de::Error::custom)
            }
            None => Ok(None),
        }
    }
}