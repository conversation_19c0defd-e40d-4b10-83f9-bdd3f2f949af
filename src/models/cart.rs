use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use crate::models::validation::{
    Validate, ValidationError, ValidationResult,
    validate_positive_decimal, validate_non_negative_i32
};
use crate::models::date_format;

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct Cart {
    pub id: i64,
    pub user_id: i64,
    #[serde(with = "date_format::date_only_format")]
    pub created_at: DateTime<Utc>,
    #[serde(with = "date_format::date_only_format")]
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, sqlx::FromRow)]
pub struct CartItem {
    pub id: i64,
    pub cart_id: i64,
    pub product_id: i64,
    pub quantity: i32,
    pub unit_price: Decimal,
    pub subtotal: Decimal,
    #[serde(with = "date_format::date_only_format")]
    pub created_at: DateTime<Utc>,
    #[serde(with = "date_format::date_only_format")]
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CartDetails {
    pub cart: Cart,
    pub items: Vec<CartItemDetails>,
    pub total_amount: Decimal,
    pub total_items: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CartItemDetails {
    pub item: CartItem,
    pub product_name: String,
    pub product_nhi_code: String,
    pub available_stock: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AddToCartRequest {
    pub product_id: i64,
    pub quantity: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateCartItemRequest {
    pub quantity: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CartSummary {
    pub total_items: i32,
    pub total_amount: Decimal,
    pub items_count: usize,
}

#[allow(dead_code)]
impl Cart {
    pub fn new(user_id: i64) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // Will be set by database
            user_id,
            created_at: now,
            updated_at: now,
        }
    }

    pub fn touch(&mut self) {
        self.updated_at = Utc::now();
    }
}

#[allow(dead_code)]
impl CartItem {
    pub fn new(cart_id: i64, product_id: i64, quantity: i32, unit_price: Decimal) -> ValidationResult<Self> {
        validate_non_negative_i32(quantity, "quantity")?;
        if quantity == 0 {
            return Err(ValidationError::Required {
                field: "quantity".to_string(),
            });
        }

        validate_positive_decimal(&unit_price, "unit_price")?;

        let subtotal = (unit_price * Decimal::from(quantity)).round_dp(0);
        let now = Utc::now();

        Ok(Self {
            id: 0, // Will be set by database
            cart_id,
            product_id,
            quantity,
            unit_price,
            subtotal,
            created_at: now,
            updated_at: now,
        })
    }

    pub fn update_quantity(&mut self, new_quantity: i32) -> ValidationResult<()> {
        validate_non_negative_i32(new_quantity, "quantity")?;
        if new_quantity == 0 {
            return Err(ValidationError::Required {
                field: "quantity".to_string(),
            });
        }

        self.quantity = new_quantity;
        self.subtotal = (self.unit_price * Decimal::from(new_quantity)).round_dp(0);
        self.updated_at = Utc::now();
        Ok(())
    }

    pub fn update_price(&mut self, new_price: Decimal) -> ValidationResult<()> {
        validate_positive_decimal(&new_price, "unit_price")?;
        self.unit_price = new_price;
        self.subtotal = (new_price * Decimal::from(self.quantity)).round_dp(0);
        self.updated_at = Utc::now();
        Ok(())
    }

    pub fn add_quantity(&mut self, additional_quantity: i32) -> ValidationResult<()> {
        validate_non_negative_i32(additional_quantity, "additional_quantity")?;
        let new_quantity = self.quantity + additional_quantity;
        self.update_quantity(new_quantity)
    }
}

impl CartDetails {
    pub fn calculate_totals(&mut self) {
        self.total_amount = self.items.iter().map(|item| item.item.subtotal).sum();
        self.total_items = self.items.iter().map(|item| item.item.quantity).sum();
    }

    pub fn get_summary(&self) -> CartSummary {
        CartSummary {
            total_items: self.total_items,
            total_amount: self.total_amount,
            items_count: self.items.len(),
        }
    }

    #[allow(dead_code)]
    pub fn has_insufficient_stock(&self) -> bool {
        self.items.iter().any(|item| item.item.quantity > item.available_stock)
    }

    #[allow(dead_code)]
    pub fn get_insufficient_stock_items(&self) -> Vec<&CartItemDetails> {
        self.items.iter()
            .filter(|item| item.item.quantity > item.available_stock)
            .collect()
    }
}

impl Validate for Cart {
    fn validate(&self) -> Result<(), Vec<ValidationError>> {
        let mut errors = Vec::new();

        if self.user_id <= 0 {
            errors.push(ValidationError::Required {
                field: "user_id".to_string(),
            });
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

impl Validate for CartItem {
    fn validate(&self) -> Result<(), Vec<ValidationError>> {
        let mut errors = Vec::new();

        if self.cart_id <= 0 {
            errors.push(ValidationError::Required {
                field: "cart_id".to_string(),
            });
        }

        if self.product_id <= 0 {
            errors.push(ValidationError::Required {
                field: "product_id".to_string(),
            });
        }

        if let Err(e) = validate_non_negative_i32(self.quantity, "quantity") {
            errors.push(e);
        }

        if self.quantity == 0 {
            errors.push(ValidationError::Required {
                field: "quantity".to_string(),
            });
        }

        if let Err(e) = validate_positive_decimal(&self.unit_price, "unit_price") {
            errors.push(e);
        }

        if let Err(e) = validate_positive_decimal(&self.subtotal, "subtotal") {
            errors.push(e);
        }

        // Verify subtotal calculation (with rounding)
        let expected_subtotal = (self.unit_price * Decimal::from(self.quantity)).round_dp(0);
        if (self.subtotal - expected_subtotal).abs() > Decimal::from_f64_retain(0.01).unwrap() {
            errors.push(ValidationError::InvalidValue {
                field: "subtotal".to_string(),
                value: self.subtotal.to_string(),
            });
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

impl Validate for AddToCartRequest {
    fn validate(&self) -> Result<(), Vec<ValidationError>> {
        let mut errors = Vec::new();

        if self.product_id <= 0 {
            errors.push(ValidationError::Required {
                field: "product_id".to_string(),
            });
        }

        if let Err(e) = validate_non_negative_i32(self.quantity, "quantity") {
            errors.push(e);
        }

        if self.quantity == 0 {
            errors.push(ValidationError::Required {
                field: "quantity".to_string(),
            });
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

impl Validate for UpdateCartItemRequest {
    fn validate(&self) -> Result<(), Vec<ValidationError>> {
        let mut errors = Vec::new();

        if let Err(e) = validate_non_negative_i32(self.quantity, "quantity") {
            errors.push(e);
        }

        if self.quantity == 0 {
            errors.push(ValidationError::Required {
                field: "quantity".to_string(),
            });
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal_macros::dec;

    #[test]
    fn test_cart_new() {
        let cart = Cart::new(1);
        assert_eq!(cart.user_id, 1);
        assert_eq!(cart.id, 0); // Will be set by database
    }

    #[test]
    fn test_cart_touch() {
        let mut cart = Cart::new(1);
        let original_time = cart.updated_at;
        
        // 等待一小段時間確保時間戳不同
        std::thread::sleep(std::time::Duration::from_millis(1));
        cart.touch();
        
        assert!(cart.updated_at > original_time);
    }

    #[test]
    fn test_cart_item_new() {
        let item = CartItem::new(1, 2, 3, dec!(50.0)).unwrap();
        assert_eq!(item.cart_id, 1);
        assert_eq!(item.product_id, 2);
        assert_eq!(item.quantity, 3);
        assert_eq!(item.unit_price, dec!(50.0));
        assert_eq!(item.subtotal, dec!(150)); // 3 * 50
    }

    #[test]
    fn test_cart_item_new_invalid() {
        // 測試零數量
        assert!(CartItem::new(1, 2, 0, dec!(50.0)).is_err());
        
        // 測試負數量
        assert!(CartItem::new(1, 2, -1, dec!(50.0)).is_err());
        
        // 測試負價格
        assert!(CartItem::new(1, 2, 3, dec!(-50.0)).is_err());
    }

    #[test]
    fn test_cart_item_update_quantity() {
        let mut item = CartItem::new(1, 2, 3, dec!(50.0)).unwrap();
        
        // 測試有效數量更新
        assert!(item.update_quantity(5).is_ok());
        assert_eq!(item.quantity, 5);
        assert_eq!(item.subtotal, dec!(250)); // 5 * 50

        // 測試無效數量更新
        assert!(item.update_quantity(0).is_err());
        assert!(item.update_quantity(-1).is_err());
    }

    #[test]
    fn test_cart_item_update_price() {
        let mut item = CartItem::new(1, 2, 3, dec!(50.0)).unwrap();
        
        // 測試有效價格更新
        assert!(item.update_price(dec!(60.0)).is_ok());
        assert_eq!(item.unit_price, dec!(60.0));
        assert_eq!(item.subtotal, dec!(180)); // 3 * 60

        // 測試無效價格更新
        assert!(item.update_price(dec!(-10.0)).is_err());
    }

    #[test]
    fn test_cart_item_add_quantity() {
        let mut item = CartItem::new(1, 2, 3, dec!(50.0)).unwrap();
        
        // 測試增加數量
        assert!(item.add_quantity(2).is_ok());
        assert_eq!(item.quantity, 5);
        assert_eq!(item.subtotal, dec!(250)); // 5 * 50

        // 測試增加負數量
        assert!(item.add_quantity(-1).is_err());
    }

    #[test]
    fn test_cart_details_calculate_totals() {
        let cart = Cart::new(1);
        let items = vec![
            CartItemDetails {
                item: CartItem::new(1, 2, 3, dec!(50.0)).unwrap(),
                product_name: "Product 1".to_string(),
                product_nhi_code: "A001".to_string(),
                available_stock: 100,
            },
            CartItemDetails {
                item: CartItem::new(1, 3, 2, dec!(30.0)).unwrap(),
                product_name: "Product 2".to_string(),
                product_nhi_code: "A002".to_string(),
                available_stock: 50,
            },
        ];

        let mut cart_details = CartDetails {
            cart,
            items,
            total_amount: Decimal::ZERO,
            total_items: 0,
        };

        cart_details.calculate_totals();
        assert_eq!(cart_details.total_amount, dec!(210)); // 150 + 60
        assert_eq!(cart_details.total_items, 5); // 3 + 2
    }

    #[test]
    fn test_cart_details_get_summary() {
        let cart = Cart::new(1);
        let items = vec![
            CartItemDetails {
                item: CartItem::new(1, 2, 3, dec!(50.0)).unwrap(),
                product_name: "Product 1".to_string(),
                product_nhi_code: "A001".to_string(),
                available_stock: 100,
            },
        ];

        let mut cart_details = CartDetails {
            cart,
            items,
            total_amount: Decimal::ZERO,
            total_items: 0,
        };

        cart_details.calculate_totals();
        let summary = cart_details.get_summary();
        
        assert_eq!(summary.total_items, 3);
        assert_eq!(summary.total_amount, dec!(150));
        assert_eq!(summary.items_count, 1);
    }

    #[test]
    fn test_cart_details_insufficient_stock() {
        let cart = Cart::new(1);
        let items = vec![
            CartItemDetails {
                item: CartItem::new(1, 2, 10, dec!(50.0)).unwrap(), // 要求 10 個
                product_name: "Product 1".to_string(),
                product_nhi_code: "A001".to_string(),
                available_stock: 5, // 只有 5 個庫存
            },
            CartItemDetails {
                item: CartItem::new(1, 3, 2, dec!(30.0)).unwrap(), // 要求 2 個
                product_name: "Product 2".to_string(),
                product_nhi_code: "A002".to_string(),
                available_stock: 10, // 有 10 個庫存
            },
        ];

        let cart_details = CartDetails {
            cart,
            items,
            total_amount: dec!(560),
            total_items: 12,
        };

        assert!(cart_details.has_insufficient_stock());
        
        let insufficient_items = cart_details.get_insufficient_stock_items();
        assert_eq!(insufficient_items.len(), 1);
        assert_eq!(insufficient_items[0].item.product_id, 2);
    }

    #[test]
    fn test_cart_validation() {
        let cart = Cart::new(1);
        assert!(cart.validate().is_ok());

        let invalid_cart = Cart {
            id: 1,
            user_id: 0, // 無效的使用者ID
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };
        assert!(invalid_cart.validate().is_err());
    }

    #[test]
    fn test_cart_item_validation() {
        let item = CartItem::new(1, 2, 3, dec!(50.0)).unwrap();
        assert!(item.validate().is_ok());

        // 測試小計計算錯誤的情況
        let invalid_item = CartItem {
            id: 1,
            cart_id: 1,
            product_id: 2,
            quantity: 3,
            unit_price: dec!(50.0),
            subtotal: dec!(140.0), // 錯誤的小計 (應該是 150.0)
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };
        assert!(invalid_item.validate().is_err());
    }

    #[test]
    fn test_add_to_cart_request_validation() {
        let request = AddToCartRequest {
            product_id: 1,
            quantity: 2,
        };
        assert!(request.validate().is_ok());

        let invalid_request = AddToCartRequest {
            product_id: 0, // 無效產品ID
            quantity: 0,   // 零數量
        };
        assert!(invalid_request.validate().is_err());
    }

    #[test]
    fn test_update_cart_item_request_validation() {
        let request = UpdateCartItemRequest { quantity: 5 };
        assert!(request.validate().is_ok());

        let invalid_request = UpdateCartItemRequest { quantity: 0 };
        assert!(invalid_request.validate().is_err());
    }

    #[test]
    fn test_cart_item_precision() {
        // 測試小數計算精度
        let item = CartItem::new(1, 2, 3, dec!(33.33)).unwrap();
        assert_eq!(item.subtotal, dec!(100)); // 3 * 33.33 rounded
        
        // 測試價格更新後的精度
        let mut item = CartItem::new(1, 2, 7, dec!(14.29)).unwrap();
        assert_eq!(item.subtotal, dec!(100)); // 7 * 14.29 rounded
        
        item.update_price(dec!(14.28)).unwrap();
        assert_eq!(item.subtotal, dec!(100)); // 7 * 14.28 rounded
    }

    #[test]
    fn test_empty_cart_details() {
        let cart = Cart::new(1);
        let mut cart_details = CartDetails {
            cart,
            items: vec![],
            total_amount: Decimal::ZERO,
            total_items: 0,
        };

        cart_details.calculate_totals();
        assert_eq!(cart_details.total_amount, Decimal::ZERO);
        assert_eq!(cart_details.total_items, 0);
        
        let summary = cart_details.get_summary();
        assert_eq!(summary.items_count, 0);
        assert!(!cart_details.has_insufficient_stock());
    }

    #[test]
    fn test_cart_item_edge_cases() {
        // 測試最小有效值
        let item = CartItem::new(1, 2, 1, dec!(0.01)).unwrap();
        assert_eq!(item.subtotal, dec!(0.01));
        assert!(item.validate().is_ok());

        // 測試大數量
        let item = CartItem::new(1, 2, 1000, dec!(1.50)).unwrap();
        assert_eq!(item.subtotal, dec!(1500));
        assert!(item.validate().is_ok());
    }
}