use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use crate::models::date_format;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, sqlx::FromRow)]
pub struct Favorite {
    pub id: i64,
    pub user_id: i64,
    pub product_id: i64,
    #[serde(with = "date_format::date_only_format")]
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct FavoriteRequest {
    pub product_id: i64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct FavoriteResponse {
    pub success: bool,
    pub message: String,
    pub is_favorite: bool,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct FavoriteProduct {
    pub id: i64,
    pub name: String,
    pub ingredients: Option<String>,
    pub dosage_form: Option<String>,
    pub strength: Option<String>,
    pub price: Option<rust_decimal::Decimal>,
    pub insurance_code: Option<String>,
    pub manufacturer: Option<String>,
    pub favorite_id: i64,
    #[serde(with = "date_format::date_only_format")]
    pub favorited_at: DateTime<Utc>,
}

impl Favorite {
    #[allow(dead_code)]
    pub fn new(user_id: i64, product_id: i64) -> Self {
        Self {
            id: 0, // Will be set by database
            user_id,
            product_id,
            created_at: Utc::now(),
        }
    }
}