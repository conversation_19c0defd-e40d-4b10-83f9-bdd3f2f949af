use serde::{Deserialize, Serialize};
use crate::models::{Order, User};

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailNotification {
    pub to: String,
    pub subject: String,
    pub body: String,
    pub html_body: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LineNotification {
    pub user_id: String,
    pub message: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NotificationTemplate {
    pub template_type: NotificationType,
    pub subject: String,
    pub body: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum NotificationType {
    OrderConfirmation,
    OrderStatusUpdate,
    StockAlert,
    SystemMaintenance,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NotificationContext {
    pub order: Option<Order>,
    pub user: Option<User>,
    pub additional_data: std::collections::HashMap<String, String>,
}