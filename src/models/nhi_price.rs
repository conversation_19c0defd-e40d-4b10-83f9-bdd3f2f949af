use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use rust_decimal::Decimal;
use crate::models::date_format;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct NhiPrice {
    pub nhi_code: String,
    pub nhi_price: Decimal,
    pub selling_price: Decimal,
    #[serde(with = "date_format::date_only_format")]
    pub created_at: DateTime<Utc>,
    #[serde(with = "date_format::date_only_format")]
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CreateNhiPriceRequest {
    pub nhi_code: String,
    pub nhi_price: Decimal,
    pub selling_price: Decimal,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateNhiPriceRequest {
    pub selling_price: Decimal,
}

#[allow(dead_code)]
impl NhiPrice {
    pub fn new(nhi_code: String, nhi_price: Decimal, selling_price: Decimal) -> Self {
        let now = Utc::now();
        Self {
            nhi_code,
            nhi_price,
            selling_price,
            created_at: now,
            updated_at: now,
        }
    }
}