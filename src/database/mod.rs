pub mod connection;
pub mod error;
pub mod migration;
pub mod fixtures;

pub use connection::*;
pub use error::*;
pub use migration::*;
// pub use fixtures::*;  // 暫時註解，因為未使用

// Re-export commonly used types for PostgreSQL
#[allow(unused_imports)]
pub use sqlx::{Row, Transaction, Postgres};

use std::sync::Arc;
use tracing::info;

/// 主要的資料庫管理結構
#[derive(Debug, Clone)]
pub struct Database {
    connection_manager: ConnectionManager,
    migration_manager: Arc<MigrationManager>,
}

#[allow(dead_code)]
impl Database {
    /// 使用預設配置建立資料庫
    pub async fn new(database_url: &str) -> DatabaseResult<Self> {
        let config = ConnectionConfig {
            database_url: database_url.to_string(),
            ..Default::default()
        };
        Self::with_config(config).await
    }

    /// 使用自訂配置建立資料庫
    pub async fn with_config(config: ConnectionConfig) -> DatabaseResult<Self> {
        info!("Initializing database with configuration");
        
        // 建立連線管理器
        let connection_manager = ConnectionManager::new(config).await?;
        
        // 建立遷移管理器
        let pool = connection_manager.pool().as_ref().clone();
        let migration_manager = Arc::new(MigrationManager::new(pool));

        let database = Self {
            connection_manager,
            migration_manager,
        };

        // 執行初始化
        database.initialize().await?;

        info!("Database initialization completed successfully");
        Ok(database)
    }

    /// 初始化資料庫
    async fn initialize(&self) -> DatabaseResult<()> {
        info!("Starting database initialization...");

        // 執行遷移
        self.migration_manager.run_migrations().await?;

        // 執行健康檢查
        self.connection_manager.health_check().await?;

        // 暫時跳過 schema 驗證，因為可能有時序問題
        // TODO: 可以考慮加上延遲重試機制
        /*
        let validation = self.migration_manager.validate_schema().await?;
        if !validation.is_valid {
            tracing::error!("Database schema validation failed: missing tables={:?}, missing indexes={:?}", 
                   validation.missing_tables, validation.missing_indexes);
            return Err(DatabaseError::Validation("Database schema is invalid".to_string()));
        }
        */

        info!("Database initialization completed");
        Ok(())
    }

    /// 取得連線池
    pub fn pool(&self) -> &DbPool {
        self.connection_manager.pool()
    }

    /// 執行健康檢查
    pub async fn health_check(&self) -> DatabaseResult<()> {
        self.connection_manager.health_check().await
    }

    /// 取得連線統計
    pub fn connection_stats(&self) -> ConnectionStats {
        self.connection_manager.stats()
    }

    /// 檢查遷移狀態
    pub async fn migration_status(&self) -> DatabaseResult<MigrationStatus> {
        self.migration_manager.check_migration_status().await
    }

    /// 驗證資料庫結構
    pub async fn validate_schema(&self) -> DatabaseResult<SchemaValidation> {
        self.migration_manager.validate_schema().await
    }

    /// 開始交易
    pub async fn begin_transaction(&self) -> DatabaseResult<sqlx::Transaction<'_, sqlx::Postgres>> {
        self.pool().begin()
            .await
            .map_err(|e| DatabaseError::Transaction(format!("Failed to begin transaction: {}", e)))
    }

    /// 執行資料庫備份 (PostgreSQL)
    pub async fn backup_to_file(&self, backup_path: &str) -> DatabaseResult<()> {
        info!("Creating PostgreSQL database backup to: {}", backup_path);
        
        // PostgreSQL 備份應該使用 pg_dump 工具
        // 這個方法主要用於應用程序內部的邏輯備份
        Err(DatabaseError::Backup("PostgreSQL backup should use pg_dump command-line tool. This method is for application-level backup only.".to_string()))
    }

    /// 重置資料庫
    pub async fn reset(&self) -> DatabaseResult<()> {
        info!("Resetting database...");
        self.migration_manager.reset_database().await?;
        self.migration_manager.run_migrations().await?;
        info!("Database reset completed");
        Ok(())
    }

    /// 關閉資料庫連線
    pub async fn close(&self) {
        info!("Closing database connections...");
        self.connection_manager.close().await;
        info!("Database connections closed");
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;

    #[tokio::test]
    async fn test_database_creation() {
        // Note: This test requires a running PostgreSQL instance
        let database_url = "postgresql://postgres:password@localhost:5432/test_db";
        
        // let database = Database::new(database_url).await;
        // assert!(database.is_ok());
    }

    #[tokio::test]
    async fn test_database_health_check() {
        // Note: This test requires a running PostgreSQL instance
        let database_url = "postgresql://postgres:password@localhost:5432/test_db";
        
        // let database = Database::new(database_url).await.unwrap();
        // let result = database.health_check().await;
        // assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_connection_stats() {
        // Note: This test requires a running PostgreSQL instance
        let database_url = "postgresql://postgres:password@localhost:5432/test_db";
        
        // let database = Database::new(database_url).await.unwrap();
        // let stats = database.connection_stats();
        // assert!(stats.max_connections > 0);
    }

    #[tokio::test]
    async fn test_migration_status() {
        // Note: This test requires a running PostgreSQL instance
        let database_url = "postgresql://postgres:password@localhost:5432/test_db";
        
        // let database = Database::new(database_url).await.unwrap();
        // let status = database.migration_status().await.unwrap();
        // assert_eq!(status, MigrationStatus::UpToDate);
    }

    #[tokio::test]
    async fn test_schema_validation() {
        // Note: This test requires a running PostgreSQL instance
        let database_url = "postgresql://postgres:password@localhost:5432/test_db";
        
        // let database = Database::new(database_url).await.unwrap();
        // let validation = database.validate_schema().await.unwrap();
        // assert!(validation.is_valid);
    }
}