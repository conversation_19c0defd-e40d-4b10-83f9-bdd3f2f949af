#![allow(dead_code)]

use crate::{
    models::{User, Product},
    database::{DatabaseResult, DatabaseError, DbPool},
    repositories::{user::CreateUser, base::RepositoryBase}
};
use chrono::Utc;
use rust_decimal::{Decimal, prelude::ToPrimitive};
use sqlx::Row;
use tracing::{info, debug};

/// 測試資料 fixtures 管理器
#[derive(Debug)]
#[allow(dead_code)]
pub struct FixtureManager {
    base: RepositoryBase,
}

impl FixtureManager {
    /// 建立新的 fixture 管理器
    pub fn new(pool: DbPool) -> Self {
        Self {
            base: RepositoryBase::new(pool),
        }
    }

    /// 清理所有測試資料
    pub async fn cleanup_all(&self) -> DatabaseResult<()> {
        info!("Cleaning up all test data...");
        
        let tables = vec![
            "order_items",
            "orders", 
            "notifications",
            "notification_preferences",
            "backup_logs",
            "products",
            "users"
        ];

        for table in tables {
            // Check if table exists before trying to delete from it
            let table_exists: i64 = self.base.execute_query(
                &format!("check table {} exists", table),
                sqlx::query_scalar("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = $1")
                    .bind(table)
                    .fetch_one(self.base.pool().as_ref())
            ).await.unwrap_or(0);

            if table_exists > 0 {
                self.base.execute_query(
                    &format!("cleanup {}", table),
                    sqlx::query(&format!("DELETE FROM {}", table))
                        .execute(self.base.pool().as_ref())
                ).await?;
            }
        }

        info!("Test data cleanup completed");
        Ok(())
    }

    /// 建立測試使用者
    pub async fn create_test_users(&self) -> DatabaseResult<Vec<User>> {
        info!("Creating test users...");
        
        let test_users = vec![
            CreateUser {
                username: "admin".to_string(),
                email: "<EMAIL>".to_string(),
                password_hash: "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uIoO".to_string(), // "password"
                pharmacy_name: "Central Pharmacy".to_string(),
                phone: Some("02-1234-5678".to_string()),
                line_user_id: Some("admin_line".to_string()),
                role_id: Some(2), // admin 角色
                contact_person: Some("管理員".to_string()),
                mobile: Some("0912-345-678".to_string()),
                institution_code: Some("ADM001".to_string()),
                address: Some("台北市信義區信義路五段7號".to_string()),
            },
            CreateUser {
                username: "pharmacy1".to_string(),
                email: "<EMAIL>".to_string(),
                password_hash: "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uIoO".to_string(),
                pharmacy_name: "Health Pharmacy".to_string(),
                phone: Some("02-2345-6789".to_string()),
                line_user_id: Some("pharmacy1_line".to_string()),
                role_id: Some(3), // pharmacy 角色
                contact_person: Some("藥師一".to_string()),
                mobile: Some("0923-456-789".to_string()),
                institution_code: Some("PH001".to_string()),
                address: Some("台北市大安區復興南路一段390號".to_string()),
            },
            CreateUser {
                username: "pharmacy2".to_string(),
                email: "<EMAIL>".to_string(),
                password_hash: "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uIoO".to_string(),
                pharmacy_name: "Care Pharmacy".to_string(),
                phone: Some("02-3456-7890".to_string()),
                role_id: Some(3), // pharmacy 角色
                line_user_id: None,
                contact_person: Some("藥師二".to_string()),
                mobile: Some("0934-567-890".to_string()),
                institution_code: Some("PH002".to_string()),
                address: Some("台北市中山區中山北路二段48號".to_string()),
            },
        ];

        let mut created_users = Vec::new();
        
        for user_data in test_users {
            let user_id: i64 = sqlx::query_scalar(
                r#"
                INSERT INTO users (username, email, password_hash, pharmacy_name, phone, line_user_id, 
                                 notification_email, notification_line, role_id)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                RETURNING id
                "#
            )
            .bind(&user_data.username)
            .bind(&user_data.email)
            .bind(&user_data.password_hash)
            .bind(&user_data.pharmacy_name)
            .bind(&user_data.phone)
            .bind(&user_data.line_user_id)
            .bind(true) // notification_email
            .bind(false) // notification_line
            .bind(3) // role_id (pharmacy)
            .fetch_one(self.base.pool().as_ref())
            .await
            .map_err(|e| {
                tracing::error!("Failed to create test user: {}", e);
                DatabaseError::from(e)
            })?;

            let user = self.get_user_by_id(user_id).await?;
            created_users.push(user);
        }

        info!("Created {} test users", created_users.len());
        Ok(created_users)
    }

    /// 建立測試產品
    pub async fn create_test_products(&self) -> DatabaseResult<Vec<Product>> {
        info!("Creating test products...");
        
        let test_products = vec![
            ("A001234567", "普拿疼錠 500毫克", Some("Paracetamol 500mg"), Some("Paracetamol 500mg"), Some("錠劑"), "ABC Pharma", "tablet", "10.50", 100),
            ("B002345678", "布洛芬錠 400毫克", Some("Ibuprofen 400mg"), Some("Ibuprofen 400mg"), Some("錠劑"), "XYZ Medical", "tablet", "15.75", 50),
            ("C003456789", "安莫西林膠囊 250毫克", Some("Amoxicillin 250mg"), Some("Amoxicillin 250mg"), Some("膠囊"), "DEF Labs", "capsule", "25.00", 75),
            ("D004567890", "歐美拉唑膠囊 20毫克", Some("Omeprazole 20mg"), Some("Omeprazole 20mg"), Some("膠囊"), "GHI Pharma", "capsule", "30.25", 30),
            ("E005678901", "二甲雙胍錠 500毫克", Some("Metformin 500mg"), Some("Metformin 500mg"), Some("錠劑"), "JKL Medical", "tablet", "12.80", 200),
            ("F006789012", "立普妥錠 10毫克", Some("Atorvastatin 10mg"), Some("Atorvastatin 10mg"), Some("錠劑"), "MNO Labs", "tablet", "45.60", 25),
            ("G007890123", "洛薩坦錠 50毫克", Some("Losartan 50mg"), Some("Losartan 50mg"), Some("錠劑"), "PQR Pharma", "tablet", "22.30", 80),
            ("H008901234", "安普羅錠 5毫克", Some("Amlodipine 5mg"), Some("Amlodipine 5mg"), Some("錠劑"), "STU Medical", "tablet", "18.90", 60),
        ];

        let mut created_products = Vec::new();
        
        for (nhi_code, name, english_name, ingredients, dosage_form, manufacturer, unit, price, stock) in test_products {
            let product_id: i64 = sqlx::query_scalar(
                r#"
                INSERT INTO products (nhi_code, name, english_name, ingredients, dosage_form, manufacturer, unit, unit_price, stock_quantity, description, is_active)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                RETURNING id
                "#
            )
            .bind(nhi_code)
            .bind(name)
            .bind(english_name)
            .bind(ingredients)
            .bind(dosage_form)
            .bind(manufacturer)
            .bind(unit)
            .bind(price.parse::<Decimal>().unwrap())
            .bind(stock)
            .bind(format!("Test product: {}", name))
            .bind(true) // is_active
            .fetch_one(self.base.pool().as_ref())
            .await
            .map_err(|e| {
                tracing::error!("Failed to create test product: {}", e);
                DatabaseError::from(e)
            })?;

            let product = self.get_product_by_id(product_id).await?;
            created_products.push(product);
        }

        info!("Created {} test products", created_products.len());
        Ok(created_products)
    }

    /// 建立完整的測試資料集
    pub async fn create_full_test_dataset(&self) -> DatabaseResult<TestDataset> {
        info!("Creating full test dataset...");
        
        // 先清理現有資料
        self.cleanup_all().await?;
        
        // 建立測試使用者和產品
        let users = self.create_test_users().await?;
        let products = self.create_test_products().await?;
        
        // 建立一些測試訂單
        let orders = self.create_test_orders(&users, &products).await?;
        
        let dataset = TestDataset {
            users,
            products,
            orders,
        };
        
        info!("Full test dataset created successfully");
        Ok(dataset)
    }

    /// 建立測試訂單
    async fn create_test_orders(&self, users: &[User], products: &[Product]) -> DatabaseResult<Vec<TestOrder>> {
        info!("Creating test orders...");
        
        let mut orders = Vec::new();
        
        // 為第一個使用者建立一個訂單
        if let (Some(user), Some(product1), Some(product2)) = (users.get(0), products.get(0), products.get(1)) {
            let order_number = format!("ORD{}{:04}", 
                Utc::now().format("%Y%m%d"), 
                1
            );
            
            let total_amount = Decimal::new(2650, 2); // 26.50
            
            let order_id: i64 = sqlx::query_scalar(
                r#"
                INSERT INTO orders (order_number, user_id, status, total_amount)
                VALUES ($1, $2, $3, $4)
                RETURNING id
                "#
            )
            .bind(&order_number)
            .bind(user.id)
            .bind("pending")
            .bind(total_amount.to_f64().unwrap_or(0.0))
            .fetch_one(self.base.pool().as_ref())
            .await
            .map_err(|e| {
                tracing::error!("Failed to create test order: {}", e);
                DatabaseError::from(e)
            })?;

            // 建立訂單項目
            let items = vec![
                (product1.id, 1, product1.selling_price),
                (product2.id, 1, product2.selling_price),
            ];

            for (product_id, quantity, unit_price) in items {
                let total_price = unit_price * Decimal::from(quantity);
                
                sqlx::query(
                    r#"
                    INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price)
                    VALUES ($1, $2, $3, $4, $5)
                    "#
                )
                .bind(order_id)
                .bind(product_id)
                .bind(quantity)
                .bind(unit_price.to_f64().unwrap_or(0.0))
                .bind(total_price.to_f64().unwrap_or(0.0))
                .execute(self.base.pool().as_ref())
                .await
                .map_err(|e| {
                    tracing::error!("Failed to create test order item: {}", e);
                    DatabaseError::from(e)
                })?;
            }

            orders.push(TestOrder {
                id: order_id,
                order_number,
                user_id: user.id,
                status: "pending".to_string(),
                total_amount,
            });
        }
        
        info!("Created {} test orders", orders.len());
        Ok(orders)
    }

    /// 根據 ID 取得使用者
    async fn get_user_by_id(&self, id: i64) -> DatabaseResult<User> {
        let row = sqlx::query(
            r#"
            SELECT id, username, email, password_hash, pharmacy_name, phone, line_user_id, 
                   notification_email, notification_line, role_id, contact_person, mobile,
                   institution_code, address, created_at, updated_at
            FROM users WHERE id = $1
            "#
        )
        .bind(id)
        .fetch_one(self.base.pool().as_ref())
        .await
        .map_err(|e| {
            tracing::error!("Failed to get user by id: {}", e);
            DatabaseError::from(e)
        })?;

        Ok(User {
            id: row.get("id"),
            username: row.get("username"),
            email: row.get("email"),
            password_hash: row.get("password_hash"),
            pharmacy_name: row.get("pharmacy_name"),
            phone: row.get("phone"),
            line_user_id: row.get("line_user_id"),
            notification_email: row.get("notification_email"),
            notification_line: row.get("notification_line"),
            role_id: row.get("role_id"),
            contact_person: row.get("contact_person"),
            mobile: row.get("mobile"),
            institution_code: row.get("institution_code"),
            address: row.get("address"),
            status: row.get("status"),
            approved_at: row.get("approved_at"),
            approved_by: row.get("approved_by"),
            rejection_reason: row.get("rejection_reason"),
            submitted_at: row.get("submitted_at"),
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
        })
    }

    /// 根據 ID 取得產品
    async fn get_product_by_id(&self, id: i64) -> DatabaseResult<Product> {
        let row = sqlx::query(
            r#"
            SELECT id, nhi_code, name, english_name, ingredients, dosage_form, manufacturer, unit, unit_price, stock_quantity, 
                   description, is_active, created_at, updated_at
            FROM products WHERE id = $1
            "#
        )
        .bind(id)
        .fetch_one(self.base.pool().as_ref())
        .await
        .map_err(|e| {
            tracing::error!("Failed to get product by id: {}", e);
            DatabaseError::from(e)
        })?;

        // PostgreSQL stores DECIMAL natively
        let unit_price: Decimal = row.get("unit_price");

        Ok(Product {
            id: row.get("id"),
            nhi_code: row.get("nhi_code"),
            name: row.get("name"),
            english_name: row.get("english_name"),
            ingredients: row.get("ingredients"),
            dosage_form: row.get("dosage_form"),
            manufacturer: row.get("manufacturer"),
            unit: row.get("unit"),
            selling_price: unit_price,
            nhi_price: Some(unit_price),
            stock_quantity: row.get("stock_quantity"),
            description: row.get("description"),
            is_active: row.get("is_active"),
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
        })
    }

    /// 驗證測試資料的完整性
    pub async fn validate_test_data(&self) -> DatabaseResult<ValidationResult> {
        debug!("Validating test data...");
        
        let mut result = ValidationResult::new();

        // 檢查使用者數量
        let user_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM users")
            .fetch_one(self.base.pool().as_ref())
            .await
            .map_err(|e| {
                tracing::error!("Failed to count users: {}", e);
                DatabaseError::from(e)
            })?;
        result.user_count = user_count as u32;

        // 檢查產品數量
        let product_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM products")
            .fetch_one(self.base.pool().as_ref())
            .await
            .map_err(|e| {
                tracing::error!("Failed to count products: {}", e);
                DatabaseError::from(e)
            })?;
        result.product_count = product_count as u32;

        // 檢查訂單數量
        let order_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM orders")
            .fetch_one(self.base.pool().as_ref())
            .await
            .map_err(|e| {
                tracing::error!("Failed to count orders: {}", e);
                DatabaseError::from(e)
            })?;
        result.order_count = order_count as u32;

        // 檢查訂單項目數量
        let order_item_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM order_items")
            .fetch_one(self.base.pool().as_ref())
            .await
            .map_err(|e| {
                tracing::error!("Failed to count order items: {}", e);
                DatabaseError::from(e)
            })?;
        result.order_item_count = order_item_count as u32;

        result.is_valid = result.user_count > 0 && result.product_count > 0;

        debug!("Test data validation completed: {:?}", result);
        Ok(result)
    }
}

/// 測試資料集
#[derive(Debug)]
pub struct TestDataset {
    pub users: Vec<User>,
    pub products: Vec<Product>,
    pub orders: Vec<TestOrder>,
}

/// 測試訂單
#[derive(Debug)]
pub struct TestOrder {
    pub id: i64,
    pub order_number: String,
    pub user_id: i64,
    pub status: String,
    pub total_amount: Decimal,
}

/// 驗證結果
#[derive(Debug)]
pub struct ValidationResult {
    pub is_valid: bool,
    pub user_count: u32,
    pub product_count: u32,
    pub order_count: u32,
    pub order_item_count: u32,
}

impl ValidationResult {
    fn new() -> Self {
        Self {
            is_valid: false,
            user_count: 0,
            product_count: 0,
            order_count: 0,
            order_item_count: 0,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;
    use crate::database::Database;

    async fn create_test_fixture_manager() -> FixtureManager {
        // 注意：這需要一個運行中的 PostgreSQL 實例
        let database_url = "postgresql://postgres:password@localhost:5432/test_fixtures_db";
        let database = Database::new(&database_url).await.unwrap();
        FixtureManager::new(database.pool().clone())
    }

    #[tokio::test]
    async fn test_create_test_users() {
        let fixture_manager = create_test_fixture_manager().await;
        
        let users = fixture_manager.create_test_users().await.unwrap();
        assert_eq!(users.len(), 3);
        assert_eq!(users[0].username, "admin");
        assert_eq!(users[1].username, "pharmacy1");
        assert_eq!(users[2].username, "pharmacy2");
    }

    #[tokio::test]
    async fn test_create_test_products() {
        let fixture_manager = create_test_fixture_manager().await;
        
        let products = fixture_manager.create_test_products().await.unwrap();
        assert_eq!(products.len(), 8);
        assert_eq!(products[0].nhi_code, "A001234567");
        assert_eq!(products[0].name, "Paracetamol 500mg");
    }

    #[tokio::test]
    async fn test_create_full_test_dataset() {
        let fixture_manager = create_test_fixture_manager().await;
        
        let dataset = fixture_manager.create_full_test_dataset().await.unwrap();
        assert_eq!(dataset.users.len(), 3);
        assert_eq!(dataset.products.len(), 8);
        assert_eq!(dataset.orders.len(), 1);
    }

    #[tokio::test]
    async fn test_validate_test_data() {
        let fixture_manager = create_test_fixture_manager().await;
        
        // 建立測試資料
        fixture_manager.create_full_test_dataset().await.unwrap();
        
        // 驗證資料
        let validation = fixture_manager.validate_test_data().await.unwrap();
        assert!(validation.is_valid);
        assert_eq!(validation.user_count, 3);
        assert_eq!(validation.product_count, 8);
        assert_eq!(validation.order_count, 1);
    }

    #[tokio::test]
    async fn test_cleanup_all() {
        let fixture_manager = create_test_fixture_manager().await;
        
        // 建立測試資料
        fixture_manager.create_full_test_dataset().await.unwrap();
        
        // 清理資料
        fixture_manager.cleanup_all().await.unwrap();
        
        // 驗證資料已清理
        let validation = fixture_manager.validate_test_data().await.unwrap();
        assert!(!validation.is_valid);
        assert_eq!(validation.user_count, 0);
        assert_eq!(validation.product_count, 0);
        assert_eq!(validation.order_count, 0);
    }
}