use thiserror::Error;
use sqlx::{Error as SqlxError, migrate::MigrateError};

/// 資料庫相關錯誤類型
#[derive(Debug, Error)]
pub enum DatabaseError {
    #[error("Connection error: {0}")]
    Connection(#[from] SqlxError),
    
    #[error("Migration error: {0}")]
    Migration(#[from] MigrateError),
    
    #[error("Configuration error: {0}")]
    Configuration(String),
    
    #[error("Health check failed: {0}")]
    HealthCheck(String),
    
    #[error("Transaction error: {0}")]
    Transaction(String),
    
    #[error("Backup error: {0}")]
    Backup(String),
    
    #[error("Restore error: {0}")]
    #[allow(dead_code)]
    Restore(String),
    
    #[error("Validation error: {0}")]
    #[allow(dead_code)]
    Validation(String),
    
    #[error("Not found: {0}")]
    NotFound(String),
    
    #[error("Constraint violation: {0}")]
    ConstraintViolation(String),
}

impl DatabaseError {
    /// 檢查是否為連線錯誤
    #[allow(dead_code)]
    pub fn is_connection_error(&self) -> bool {
        matches!(self, DatabaseError::Connection(_))
    }

    /// 檢查是否為約束違反錯誤
    #[allow(dead_code)]
    pub fn is_constraint_violation(&self) -> bool {
        match self {
            DatabaseError::ConstraintViolation(_) => true,
            DatabaseError::Connection(sqlx_error) => {
                matches!(sqlx_error, SqlxError::Database(db_error) if db_error.is_unique_violation())
            }
            _ => false,
        }
    }

    /// 檢查是否為找不到記錄錯誤
    #[allow(dead_code)]
    pub fn is_not_found(&self) -> bool {
        match self {
            DatabaseError::NotFound(_) => true,
            DatabaseError::Connection(SqlxError::RowNotFound) => true,
            _ => false,
        }
    }

    /// 從 SqlxError 轉換為更具體的 DatabaseError
    pub fn from_sqlx_error(error: SqlxError, context: &str) -> Self {
        match error {
            SqlxError::RowNotFound => DatabaseError::NotFound(context.to_string()),
            SqlxError::Database(db_error) if db_error.is_unique_violation() => {
                DatabaseError::ConstraintViolation(format!("{}: {}", context, db_error))
            }
            SqlxError::Database(db_error) if db_error.is_foreign_key_violation() => {
                DatabaseError::ConstraintViolation(format!("{}: {}", context, db_error))
            }
            other => DatabaseError::Connection(other),
        }
    }
}

/// 資料庫操作結果類型
pub type DatabaseResult<T> = Result<T, DatabaseError>;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_classification() {
        let conn_error = DatabaseError::Connection(SqlxError::RowNotFound);
        assert!(conn_error.is_connection_error());

        let not_found_error = DatabaseError::NotFound("User not found".to_string());
        assert!(not_found_error.is_not_found());

        let constraint_error = DatabaseError::ConstraintViolation("Unique constraint".to_string());
        assert!(constraint_error.is_constraint_violation());
    }

    #[test]
    fn test_from_sqlx_error() {
        let sqlx_error = SqlxError::RowNotFound;
        let db_error = DatabaseError::from_sqlx_error(sqlx_error, "Finding user");
        assert!(db_error.is_not_found());
    }
}