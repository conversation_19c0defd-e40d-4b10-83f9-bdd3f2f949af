use sqlx::PgPool;
use tracing::{info, error, debug};

use super::error::{DatabaseError, DatabaseResult};

/// 資料庫遷移管理器
#[derive(Debug)]
pub struct MigrationManager {
    pool: PgPool,
}

impl MigrationManager {
    /// 建立新的遷移管理器
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 清理舊的遷移記錄
    pub async fn clean_migrations(&self) -> DatabaseResult<()> {
        info!("Cleaning old migration records...");
        
        // 刪除舊的遷移表
        sqlx::query("DROP TABLE IF EXISTS _sqlx_migrations CASCADE")
            .execute(&self.pool)
            .await
            .map_err(|e| DatabaseError::from_sqlx_error(e, "Dropping old migrations table"))?;
        
        info!("Old migration records cleaned");
        Ok(())
    }

    /// 執行所有待執行的遷移
    pub async fn run_migrations(&self) -> DatabaseResult<()> {
        info!("Starting database migrations...");
        
        // 先清理舊的遷移記錄
        self.clean_migrations().await?;
        
        // 使用標準的 sqlx 遷移，Neon 支援所有 PostgreSQL 功能
        sqlx::migrate!("./migrations")
            .run(&self.pool)
            .await
            .map_err(|e| {
                error!("Database migration failed: {}", e);
                DatabaseError::Migration(e)
            })?;
        
        info!("Database migrations completed successfully");
        Ok(())
    }

    /// 檢查遷移狀態
    pub async fn check_migration_status(&self) -> DatabaseResult<MigrationStatus> {
        debug!("Checking migration status...");
        
        // 檢查 _sqlx_migrations 表是否存在
        let table_exists: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = '_sqlx_migrations'"
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| DatabaseError::from_sqlx_error(e, "Checking migrations table"))?;

        if table_exists == 0 {
            return Ok(MigrationStatus::NotInitialized);
        }

        // 取得已執行的遷移數量
        let applied_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM _sqlx_migrations")
            .fetch_one(&self.pool)
            .await
            .map_err(|e| DatabaseError::from_sqlx_error(e, "Counting applied migrations"))?;

        // 取得可用的遷移數量（這裡簡化處理，實際應該讀取 migrations 目錄）
        let available_count = self.count_available_migrations().await?;

        let status = if applied_count == available_count {
            MigrationStatus::UpToDate
        } else if applied_count < available_count {
            MigrationStatus::PendingMigrations {
                applied: applied_count as u32,
                available: available_count as u32,
            }
        } else {
            MigrationStatus::Unknown
        };

        debug!("Migration status: {:?}", status);
        Ok(status)
    }

    /// 重置資料庫（刪除所有表格）
    pub async fn reset_database(&self) -> DatabaseResult<()> {
        info!("Resetting database...");
        
        // 取得所有使用者建立的表格
        let tables: Vec<String> = sqlx::query_scalar(
            "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE'"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| DatabaseError::from_sqlx_error(e, "Fetching table names"))?;

        // 刪除所有表格
        for table in tables {
            let drop_sql = format!("DROP TABLE IF EXISTS {}", table);
            sqlx::query(&drop_sql)
                .execute(&self.pool)
                .await
                .map_err(|e| DatabaseError::from_sqlx_error(e, &format!("Dropping table {}", table)))?;
        }

        info!("Database reset completed");
        Ok(())
    }

    /// 驗證資料庫結構
    pub async fn validate_schema(&self) -> DatabaseResult<SchemaValidation> {
        debug!("Validating database schema...");
        
        let mut validation = SchemaValidation::new();

        // 檢查必要的表格
        let required_tables = vec!["users", "products", "orders", "order_items"];
        
        for table in required_tables {
            let exists: i64 = sqlx::query_scalar(
                "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = $1 AND table_schema = 'public'"
            )
            .bind(table)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| DatabaseError::from_sqlx_error(e, "Checking table existence"))?;

            if exists == 0 {
                validation.missing_tables.push(table.to_string());
            } else {
                validation.existing_tables.push(table.to_string());
            }
        }

        // 檢查索引
        let required_indexes = vec![
            "idx_products_nhi_code",
            "idx_orders_user_id",
            "idx_order_items_order_id",
        ];

        for index in required_indexes {
            let exists: i64 = sqlx::query_scalar(
                "SELECT COUNT(*) FROM pg_indexes WHERE indexname = $1"
            )
            .bind(index)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| DatabaseError::from_sqlx_error(e, "Checking index existence"))?;

            if exists == 0 {
                validation.missing_indexes.push(index.to_string());
            } else {
                validation.existing_indexes.push(index.to_string());
            }
        }

        validation.is_valid = validation.missing_tables.is_empty() && validation.missing_indexes.is_empty();

        debug!("Schema validation completed: valid={}", validation.is_valid);
        Ok(validation)
    }

    /// 計算可用的遷移數量
    async fn count_available_migrations(&self) -> DatabaseResult<i64> {
        // 這裡簡化處理，實際應該讀取 migrations 目錄中的檔案
        // 目前假設有一個初始遷移檔案
        Ok(1)
    }
}

/// 遷移狀態
#[derive(Debug, PartialEq)]
pub enum MigrationStatus {
    NotInitialized,
    UpToDate,
    PendingMigrations { applied: u32, available: u32 },
    Unknown,
}

/// 資料庫結構驗證結果
#[derive(Debug)]
pub struct SchemaValidation {
    pub is_valid: bool,
    pub existing_tables: Vec<String>,
    pub missing_tables: Vec<String>,
    pub existing_indexes: Vec<String>,
    pub missing_indexes: Vec<String>,
}

impl SchemaValidation {
    fn new() -> Self {
        Self {
            is_valid: false,
            existing_tables: Vec::new(),
            missing_tables: Vec::new(),
            existing_indexes: Vec::new(),
            missing_indexes: Vec::new(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use sqlx::PgPool;
    use tempfile::NamedTempFile;

    async fn create_test_pool() -> PgPool {
        // 注意：這需要一個運行中的 PostgreSQL 實例
        let database_url = "postgresql://postgres:password@localhost:5432/test_migration_db";
        PgPool::connect(&database_url).await.unwrap()
    }

    #[tokio::test]
    async fn test_migration_manager_creation() {
        let pool = create_test_pool().await;
        let manager = MigrationManager::new(pool);
        
        // 測試基本功能
        let status = manager.check_migration_status().await.unwrap();
        assert_eq!(status, MigrationStatus::NotInitialized);
    }

    #[tokio::test]
    async fn test_schema_validation() {
        let pool = create_test_pool().await;
        let manager = MigrationManager::new(pool);
        
        let validation = manager.validate_schema().await;
        // This test might fail if the database file can't be created, which is expected
        // in some test environments
        if let Ok(validation) = validation {
            assert!(!validation.is_valid);
            assert!(!validation.missing_tables.is_empty());
        }
    }
}