use tracing::{info, warn, error, debug, Level};
use tracing_subscriber::{
    fmt::{self, format::FmtSpan},
    layer::SubscriberExt,
    util::SubscriberInitExt,
    EnvFilter,
};
use std::env;

/// 日誌配置
#[derive(Debug, Clone)]
pub struct LoggingConfig {
    pub level: Level,
    pub json_format: bool,
    pub include_file_line: bool,
    pub include_thread_ids: bool,
    pub gcp_project_id: Option<String>,
    pub service_name: String,
    pub service_version: String,
}

impl Default for LoggingConfig {
    fn default() -> Self {
        Self {
            level: Level::INFO,
            json_format: env::var("LOG_FORMAT").unwrap_or_default() == "json",
            include_file_line: env::var("LOG_INCLUDE_FILE_LINE").unwrap_or_default() == "true",
            include_thread_ids: env::var("LOG_INCLUDE_THREAD_IDS").unwrap_or_default() == "true",
            gcp_project_id: env::var("GCP_PROJECT_ID").ok(),
            service_name: env::var("SERVICE_NAME").unwrap_or_else(|_| "pharmacy-system".to_string()),
            service_version: env::var("SERVICE_VERSION").unwrap_or_else(|_| "0.1.0".to_string()),
        }
    }
}

/// 初始化日誌系統
pub fn init_logging(config: LoggingConfig) -> Result<(), Box<dyn std::error::Error>> {
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new(config.level.to_string()));

    let registry = tracing_subscriber::registry().with(env_filter);

    if config.json_format {
        // JSON 格式日誌，適合 GCP Cloud Logging
        let json_layer = fmt::layer()
            .with_file(config.include_file_line)
            .with_line_number(config.include_file_line)
            .with_thread_ids(config.include_thread_ids)
            .with_target(true);

        registry.with(json_layer).init();
    } else {
        // 人類可讀格式，適合開發環境
        let pretty_layer = fmt::layer()
            .pretty()
            .with_file(config.include_file_line)
            .with_line_number(config.include_file_line)
            .with_thread_ids(config.include_thread_ids)
            .with_target(true)
            .with_span_events(FmtSpan::CLOSE);

        registry.with(pretty_layer).init();
    }

    info!(
        service_name = config.service_name,
        service_version = config.service_version,
        log_level = %config.level,
        json_format = config.json_format,
        gcp_project_id = config.gcp_project_id,
        "日誌系統初始化完成"
    );

    Ok(())
}

/// 結構化日誌巨集
pub mod structured_logging {
    use super::*;


    /// 記錄使用者操作
    #[allow(dead_code)]
    pub fn log_user_action(
        user_id: i64,
        action: &str,
        resource: Option<&str>,
        request_id: Option<&str>,
        additional_fields: Option<serde_json::Value>,
    ) {
        let span = tracing::info_span!(
            "user_action",
            user_id = user_id,
            action = action,
            resource = resource,
            request_id = request_id.unwrap_or("unknown"),
        );

        let _enter = span.enter();
        
        if let Some(fields) = additional_fields {
            info!(
                additional_data = %fields,
                "使用者操作記錄"
            );
        } else {
            info!("使用者操作記錄");
        }
    }

    /// 記錄 API 請求
    pub fn log_api_request(
        method: &str,
        path: &str,
        user_id: Option<i64>,
        request_id: &str,
        duration_ms: Option<u64>,
        status_code: Option<u16>,
    ) {
        let span = tracing::info_span!(
            "api_request",
            method = method,
            path = path,
            user_id = user_id,
            request_id = request_id,
            duration_ms = duration_ms,
            status_code = status_code,
        );

        let _enter = span.enter();
        info!("API 請求記錄");
    }

    /// 記錄資料庫操作
    pub fn log_database_operation(
        operation: &str,
        table: &str,
        duration_ms: u64,
        affected_rows: Option<u64>,
        request_id: Option<&str>,
    ) {
        let span = tracing::debug_span!(
            "database_operation",
            operation = operation,
            table = table,
            duration_ms = duration_ms,
            affected_rows = affected_rows,
            request_id = request_id,
        );

        let _enter = span.enter();
        debug!("資料庫操作記錄");
    }

    /// 記錄外部服務呼叫
    #[allow(dead_code)]
    pub fn log_external_service_call(
        service: &str,
        endpoint: &str,
        method: &str,
        duration_ms: u64,
        status_code: Option<u16>,
        success: bool,
        request_id: Option<&str>,
    ) {
        let span = tracing::info_span!(
            "external_service_call",
            service = service,
            endpoint = endpoint,
            method = method,
            duration_ms = duration_ms,
            status_code = status_code,
            success = success,
            request_id = request_id,
        );

        let _enter = span.enter();
        
        if success {
            info!("外部服務呼叫成功");
        } else {
            warn!("外部服務呼叫失敗");
        }
    }

    /// 記錄檔案處理操作
    pub fn log_file_processing(
        operation: &str,
        file_name: &str,
        file_size: Option<u64>,
        duration_ms: u64,
        success: bool,
        processed_records: Option<u64>,
        error_count: Option<u64>,
        request_id: Option<&str>,
    ) {
        let span = tracing::info_span!(
            "file_processing",
            operation = operation,
            file_name = file_name,
            file_size = file_size,
            duration_ms = duration_ms,
            success = success,
            processed_records = processed_records,
            error_count = error_count,
            request_id = request_id,
        );

        let _enter = span.enter();
        
        if success {
            info!("檔案處理完成");
        } else {
            error!("檔案處理失敗");
        }
    }

    /// 記錄通知發送
    pub fn log_notification_sent(
        notification_type: &str,
        recipient: &str,
        success: bool,
        duration_ms: u64,
        error_message: Option<&str>,
        request_id: Option<&str>,
    ) {
        let span = tracing::info_span!(
            "notification_sent",
            notification_type = notification_type,
            recipient = recipient,
            success = success,
            duration_ms = duration_ms,
            error_message = error_message,
            request_id = request_id,
        );

        let _enter = span.enter();
        
        if success {
            info!("通知發送成功");
        } else {
            error!("通知發送失敗");
        }
    }

    /// 記錄備份操作
    pub fn log_backup_operation(
        operation: &str,
        backup_type: &str,
        file_size: Option<u64>,
        duration_ms: u64,
        success: bool,
        backup_location: Option<&str>,
        error_message: Option<&str>,
    ) {
        let span = tracing::info_span!(
            "backup_operation",
            operation = operation,
            backup_type = backup_type,
            file_size = file_size,
            duration_ms = duration_ms,
            success = success,
            backup_location = backup_location,
            error_message = error_message,
        );

        let _enter = span.enter();
        
        if success {
            info!("備份操作完成");
        } else {
            error!("備份操作失敗");
        }
    }

    /// 記錄系統健康檢查
    pub fn log_health_check(
        component: &str,
        status: &str,
        response_time_ms: u64,
        additional_info: Option<serde_json::Value>,
    ) {
        let span = tracing::debug_span!(
            "health_check",
            component = component,
            status = status,
            response_time_ms = response_time_ms,
        );

        let _enter = span.enter();
        
        if let Some(info) = additional_info {
            debug!(
                additional_info = %info,
                "健康檢查記錄"
            );
        } else {
            debug!("健康檢查記錄");
        }
    }
}

/// 效能監控
#[allow(dead_code)]
pub mod performance_monitoring {
    use super::*;
    use std::time::Instant;
    use std::collections::HashMap;
    use tokio::sync::RwLock;
    use std::sync::Arc;

    /// 效能指標
    #[derive(Debug, Clone)]
    pub struct PerformanceMetrics {
        pub operation: String,
        pub duration: std::time::Duration,
        pub success: bool,
        pub timestamp: chrono::DateTime<chrono::Utc>,
        pub additional_data: std::collections::HashMap<String, String>,
    }

    /// 效能監控器
    #[derive(Debug)]
    pub struct PerformanceMonitor {
        metrics: Arc<RwLock<Vec<PerformanceMetrics>>>,
        max_metrics: usize,
    }

    impl PerformanceMonitor {
        pub fn new(max_metrics: usize) -> Self {
            Self {
                metrics: Arc::new(RwLock::new(Vec::new())),
                max_metrics,
            }
        }

        /// 記錄效能指標
        pub async fn record_metric(&self, metric: PerformanceMetrics) {
            let mut metrics = self.metrics.write().await;
            
            // 記錄到日誌
            info!(
                operation = metric.operation,
                duration_ms = metric.duration.as_millis() as u64,
                success = metric.success,
                timestamp = %metric.timestamp,
                "效能指標記錄"
            );

            metrics.push(metric);

            // 保持指標數量在限制內
            if metrics.len() > self.max_metrics {
                metrics.remove(0);
            }
        }

        /// 取得平均回應時間
        pub async fn get_average_response_time(&self, operation: &str) -> Option<std::time::Duration> {
            let metrics = self.metrics.read().await;
            let operation_metrics: Vec<_> = metrics
                .iter()
                .filter(|m| m.operation == operation)
                .collect();

            if operation_metrics.is_empty() {
                return None;
            }

            let total_duration: std::time::Duration = operation_metrics
                .iter()
                .map(|m| m.duration)
                .sum();

            Some(total_duration / operation_metrics.len() as u32)
        }

        /// 取得成功率
        pub async fn get_success_rate(&self, operation: &str) -> Option<f64> {
            let metrics = self.metrics.read().await;
            let operation_metrics: Vec<_> = metrics
                .iter()
                .filter(|m| m.operation == operation)
                .collect();

            if operation_metrics.is_empty() {
                return None;
            }

            let success_count = operation_metrics
                .iter()
                .filter(|m| m.success)
                .count();

            Some(success_count as f64 / operation_metrics.len() as f64)
        }

        /// 清除舊指標
        pub async fn clear_old_metrics(&self, older_than: chrono::Duration) {
            let mut metrics = self.metrics.write().await;
            let cutoff_time = chrono::Utc::now() - older_than;
            
            metrics.retain(|m| m.timestamp > cutoff_time);
            
            info!(
                remaining_metrics = metrics.len(),
                cutoff_time = %cutoff_time,
                "清除舊效能指標"
            );
        }
    }

    /// 效能監控裝飾器
    pub struct PerformanceTimer {
        operation: String,
        start_time: Instant,
        additional_data: std::collections::HashMap<String, String>,
    }

    impl PerformanceTimer {
        pub fn new(operation: impl Into<String>) -> Self {
            Self {
                operation: operation.into(),
                start_time: Instant::now(),
                additional_data: HashMap::new(),
            }
        }

        pub fn add_data(&mut self, key: impl Into<String>, value: impl Into<String>) {
            self.additional_data.insert(key.into(), value.into());
        }

        pub async fn finish(self, monitor: &PerformanceMonitor, success: bool) {
            let duration = self.start_time.elapsed();
            
            let metric = PerformanceMetrics {
                operation: self.operation,
                duration,
                success,
                timestamp: chrono::Utc::now(),
                additional_data: self.additional_data,
            };

            monitor.record_metric(metric).await;
        }
    }
}

/// GCP Cloud Logging 整合
pub mod gcp_logging {
    use super::*;
    use serde_json::{json, Value};

    /// GCP 日誌嚴重程度
    #[derive(Debug, Clone, Copy)]
    #[allow(dead_code)]
    pub enum GcpSeverity {
        #[allow(dead_code)]
        Default,
        Debug,
        Info,
        #[allow(dead_code)]
        Notice,
        Warning,
        Error,
        #[allow(dead_code)]
        Critical,
        #[allow(dead_code)]
        Alert,
        #[allow(dead_code)]
        Emergency,
    }

    impl From<Level> for GcpSeverity {
        fn from(level: Level) -> Self {
            match level {
                Level::TRACE | Level::DEBUG => GcpSeverity::Debug,
                Level::INFO => GcpSeverity::Info,
                Level::WARN => GcpSeverity::Warning,
                Level::ERROR => GcpSeverity::Error,
            }
        }
    }

    impl GcpSeverity {
        #[allow(dead_code)]
        pub fn as_str(&self) -> &'static str {
            match self {
                GcpSeverity::Default => "DEFAULT",
                GcpSeverity::Debug => "DEBUG",
                GcpSeverity::Info => "INFO",
                GcpSeverity::Notice => "NOTICE",
                GcpSeverity::Warning => "WARNING",
                GcpSeverity::Error => "ERROR",
                GcpSeverity::Critical => "CRITICAL",
                GcpSeverity::Alert => "ALERT",
                GcpSeverity::Emergency => "EMERGENCY",
            }
        }
    }

    /// 建立 GCP 格式的日誌條目
    #[allow(dead_code)]
    pub fn create_gcp_log_entry(
        severity: GcpSeverity,
        message: &str,
        labels: Option<Value>,
        source_location: Option<Value>,
        http_request: Option<Value>,
        operation: Option<Value>,
    ) -> Value {
        let mut entry = json!({
            "severity": severity.as_str(),
            "message": message,
            "timestamp": chrono::Utc::now().to_rfc3339(),
        });

        if let Some(labels) = labels {
            entry["labels"] = labels;
        }

        if let Some(source_location) = source_location {
            entry["sourceLocation"] = source_location;
        }

        if let Some(http_request) = http_request {
            entry["httpRequest"] = http_request;
        }

        if let Some(operation) = operation {
            entry["operation"] = operation;
        }

        entry
    }

    /// 記錄 HTTP 請求到 GCP
    #[allow(dead_code)]
    pub fn log_http_request_gcp(
        method: &str,
        url: &str,
        status: u16,
        response_size: Option<u64>,
        duration: std::time::Duration,
        user_agent: Option<&str>,
        remote_ip: Option<&str>,
        request_id: &str,
    ) {
        let http_request = json!({
            "requestMethod": method,
            "requestUrl": url,
            "status": status,
            "responseSize": response_size,
            "latency": format!("{}s", duration.as_secs_f64()),
            "userAgent": user_agent,
            "remoteIp": remote_ip,
        });

        let labels = json!({
            "request_id": request_id,
            "service": "pharmacy-system",
        });

        let entry = create_gcp_log_entry(
            if status >= 500 {
                GcpSeverity::Error
            } else if status >= 400 {
                GcpSeverity::Warning
            } else {
                GcpSeverity::Info
            },
            &format!("{} {} - {}", method, url, status),
            Some(labels),
            None,
            Some(http_request),
            None,
        );

        // 輸出 JSON 格式日誌，GCP Cloud Logging 會自動解析
        println!("{}", entry);
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;
    use std::collections::HashMap;
    use tokio::time::{sleep, Duration as TokioDuration};

    #[test]
    fn test_logging_config_default() {
        let config = LoggingConfig::default();
        assert_eq!(config.level, Level::INFO);
        assert_eq!(config.service_name, "pharmacy-system");
        assert_eq!(config.service_version, "0.1.0");
    }

    #[tokio::test]
    async fn test_performance_monitor() {
        let monitor = performance_monitoring::PerformanceMonitor::new(100);
        
        let metric = performance_monitoring::PerformanceMetrics {
            operation: "test_operation".to_string(),
            duration: std::time::Duration::from_millis(100),
            success: true,
            timestamp: chrono::Utc::now(),
            additional_data: std::collections::HashMap::new(),
        };

        monitor.record_metric(metric).await;

        let avg_time = monitor.get_average_response_time("test_operation").await;
        assert!(avg_time.is_some());
        assert_eq!(avg_time.unwrap(), std::time::Duration::from_millis(100));

        let success_rate = monitor.get_success_rate("test_operation").await;
        assert!(success_rate.is_some());
        assert_eq!(success_rate.unwrap(), 1.0);
    }

    #[tokio::test]
    async fn test_performance_timer() {
        let monitor = performance_monitoring::PerformanceMonitor::new(100);
        
        let mut timer = performance_monitoring::PerformanceTimer::new("test_timer");
        timer.add_data("test_key", "test_value");
        
        sleep(TokioDuration::from_millis(10)).await;
        
        timer.finish(&monitor, true).await;

        let avg_time = monitor.get_average_response_time("test_timer").await;
        assert!(avg_time.is_some());
        assert!(avg_time.unwrap() >= std::time::Duration::from_millis(10));
    }

    #[test]
    fn test_gcp_severity_conversion() {
        assert_eq!(gcp_logging::GcpSeverity::from(Level::DEBUG).as_str(), "DEBUG");
        assert_eq!(gcp_logging::GcpSeverity::from(Level::INFO).as_str(), "INFO");
        assert_eq!(gcp_logging::GcpSeverity::from(Level::WARN).as_str(), "WARNING");
        assert_eq!(gcp_logging::GcpSeverity::from(Level::ERROR).as_str(), "ERROR");
    }

    #[test]
    fn test_gcp_log_entry_creation() {
        let entry = gcp_logging::create_gcp_log_entry(
            gcp_logging::GcpSeverity::Info,
            "測試訊息",
            Some(serde_json::json!({"service": "test"})),
            None,
            None,
            None,
        );

        assert_eq!(entry["severity"], "INFO");
        assert_eq!(entry["message"], "測試訊息");
        assert_eq!(entry["labels"]["service"], "test");
        assert!(entry["timestamp"].is_string());
    }
}