#!/bin/bash

echo "🏥 啟動藥品採購系統..."

# 檢查 .env 檔案是否存在
if [ ! -f .env ]; then
    echo "❌ 找不到 .env 檔案，請先複製 .env.example 並設定環境變數"
    echo "   cp .env.example .env"
    echo "   然後編輯 .env 檔案設定你的配置"
    exit 1
fi

# 載入環境變數
export $(cat .env | grep -v '^#' | xargs)

echo "📦 編譯專案..."
cargo build --release

if [ $? -ne 0 ]; then
    echo "❌ 編譯失敗"
    exit 1
fi

echo "🗄️  初始化資料庫..."
# 這裡可以加入資料庫遷移命令

echo "🚀 啟動伺服器..."
echo "   🌐 Web 介面: http://localhost:${PORT:-8080}/"
echo "   📡 API 端點: http://localhost:${PORT:-8080}/api"
echo "   ❤️  健康檢查: http://localhost:${PORT:-8080}/health"
echo "   按 Ctrl+C 停止伺服器"
echo ""

cargo run --release --bin pharmacy-system