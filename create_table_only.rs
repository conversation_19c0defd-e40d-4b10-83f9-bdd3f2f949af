use std::env;
use sqlx::postgres::PgPoolOptions;
use dotenvy::dotenv;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenv().ok();
    
    let database_url = env::var("DATABASE_URL")
        .expect("DATABASE_URL must be set");
    
    println!("🚀 連接資料庫並創建 favorites 表...");
    
    let pool = PgPoolOptions::new()
        .max_connections(5)
        .connect(&database_url)
        .await?;
    
    println!("✅ 資料庫連接成功");
    
    // 檢查表是否已存在
    let table_exists: bool = sqlx::query_scalar(
        "SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'favorites'
        )"
    )
    .fetch_one(&pool)
    .await?;
    
    if table_exists {
        println!("ℹ️  favorites 表已存在，跳過創建");
        return Ok(());
    }
    
    // 創建表
    sqlx::query(
        r#"
        CREATE TABLE favorites (
            id BIGSERIAL PRIMARY KEY,
            user_id BIGINT NOT NULL,
            product_id BIGINT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(user_id, product_id)
        )
        "#
    )
    .execute(&pool)
    .await?;
    
    println!("✅ favorites 表創建成功");
    
    // 創建索引
    sqlx::query("CREATE INDEX idx_favorites_user_id ON favorites(user_id)")
        .execute(&pool)
        .await?;
    
    sqlx::query("CREATE INDEX idx_favorites_product_id ON favorites(product_id)")
        .execute(&pool)
        .await?;
    
    sqlx::query("CREATE INDEX idx_favorites_created_at ON favorites(created_at)")
        .execute(&pool)
        .await?;
    
    println!("✅ 索引創建成功");
    
    println!("🎉 favorites 表創建完成！");
    
    Ok(())
}