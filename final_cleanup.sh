#!/bin/bash

echo "🧹 最終清理 - 移除測試和修復腳本"
echo "================================="

# 創建備份目錄
BACKUP_DIR="final_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📦 創建備份目錄: $BACKUP_DIR"

# 定義要清理的檔案
declare -a TEST_SCRIPTS=(
    "test_api.sh"
    "test_backend_rounding.sh"
    "test_cart_rounding.sh"
    "test_checkout_directly.sh"
    "test_compile.sh"
    "test_with_correct_user.sh"
)

declare -a FIX_SCRIPTS=(
    "fix_checkout_issues.sh"
    "fix_database_and_roles.sh"
)

declare -a SETUP_SCRIPTS=(
    "setup_db_simple.sh"
)

declare -a OTHER_TEMP_FILES=(
    "test_api_structure"
    "test_price_queries.rs"
    "complete_checkout_fix.sh"
    "cleanup_migration.sh"
    "quick_setup.sh"
    "quick_test.sh"
)

# 函數：備份並刪除檔案
backup_and_remove_file() {
    local file="$1"
    local category="$2"
    
    if [ -f "$file" ]; then
        echo "  📄 備份並刪除: $file"
        cp "$file" "$BACKUP_DIR/"
        rm "$file"
        return 0
    else
        echo "  ⚠️  檔案不存在: $file"
        return 1
    fi
}

# 清理測試腳本
echo ""
echo "🧪 清理測試腳本..."
removed_count=0
for file in "${TEST_SCRIPTS[@]}"; do
    if backup_and_remove_file "$file" "test"; then
        ((removed_count++))
    fi
done
echo "   清理了 $removed_count 個測試腳本"

# 清理修復腳本
echo ""
echo "🔧 清理修復腳本..."
removed_count=0
for file in "${FIX_SCRIPTS[@]}"; do
    if backup_and_remove_file "$file" "fix"; then
        ((removed_count++))
    fi
done
echo "   清理了 $removed_count 個修復腳本"

# 清理設置腳本
echo ""
echo "⚙️  清理設置腳本..."
removed_count=0
for file in "${SETUP_SCRIPTS[@]}"; do
    if backup_and_remove_file "$file" "setup"; then
        ((removed_count++))
    fi
done
echo "   清理了 $removed_count 個設置腳本"

# 清理其他臨時檔案
echo ""
echo "🗑️  清理其他臨時檔案..."
removed_count=0
for file in "${OTHER_TEMP_FILES[@]}"; do
    if backup_and_remove_file "$file" "other"; then
        ((removed_count++))
    fi
done
echo "   清理了 $removed_count 個其他檔案"

# 清理剩餘的 Python 檔案
echo ""
echo "🐍 清理剩餘的 Python 檔案..."
remaining_py_files=$(find . -name "*.py" -not -path "./migrations/*" -not -path "./$BACKUP_DIR/*")
py_count=0
for file in $remaining_py_files; do
    if [ -f "$file" ]; then
        echo "  🐍 備份並刪除: $file"
        cp "$file" "$BACKUP_DIR/"
        rm "$file"
        ((py_count++))
    fi
done
echo "   清理了 $py_count 個剩餘的 Python 檔案"

echo ""
echo "✅ 最終清理完成！"
echo ""
echo "📊 清理總結："
echo "   - 所有檔案已備份到: $BACKUP_DIR"
echo "   - 專案現在更加整潔"
echo ""
echo "🗂️  保留的重要檔案："
echo "   - Cargo.toml, Cargo.lock (Rust 專案)"
echo "   - src/ (原始碼)"
echo "   - web/ (前端檔案)"
echo "   - migrations/ (資料庫遷移)"
echo "   - .env* (環境設定)"
echo "   - deploy.sh (部署腳本)"
echo "   - start.sh, dev.sh (啟動腳本)"
echo "   - README.md, DEPLOYMENT.md (文件)"
echo ""
echo "⚠️  如果確認不需要備份檔案，可以執行："
echo "   rm -rf $BACKUP_DIR"

# 顯示最終的專案結構
echo ""
echo "📋 最終專案結構："
echo "主要目錄："
ls -la | grep "^d" | grep -v "^\.$" | grep -v "^\.\.$" | awk '{print "   " $9}'
echo ""
echo "主要檔案："
ls -la | grep "^-" | grep -E "\.(toml|md|sh|yml|dockerfile)$|^[A-Z]" | awk '{print "   " $9}'