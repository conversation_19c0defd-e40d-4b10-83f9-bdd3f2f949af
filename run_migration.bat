@echo off
echo 🚀 開始執行資料庫遷移...

REM 檢查是否有.env檔案
if not exist .env (
    echo ❌ 找不到 .env 檔案，請先創建並設置 DATABASE_URL
    pause
    exit /b 1
)

echo ✅ 找到 .env 檔案

REM 編譯並執行遷移
echo 🔨 編譯遷移工具...
cargo build --bin simple_create_favorites

if %errorlevel% equ 0 (
    echo ✅ 編譯成功
    echo 🚀 執行遷移...
    cargo run --bin simple_create_favorites
    
    if %errorlevel% equ 0 (
        echo 🎉 遷移執行成功！
        echo ✅ favorites 表已創建，現在可以使用我的最愛功能了
    ) else (
        echo ❌ 遷移執行失敗
        pause
        exit /b 1
    )
) else (
    echo ❌ 編譯失敗
    pause
    exit /b 1
)

pause