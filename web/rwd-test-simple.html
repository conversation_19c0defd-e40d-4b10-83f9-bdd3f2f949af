<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RWD 測試頁面 - 簡化版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            font-size: 16px;
        }

        .container {
            max-width: 1920px;
            margin: 0 auto;
            padding: 20px;
        }

        .test-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
        
        .breakpoint-indicator {
            background: #667eea;
            color: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
            text-align: center;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: white;
        }

        /* 導航標籤 */
        .nav-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #eee;
            flex-wrap: wrap;
        }

        .nav-tab {
            padding: 15px 25px;
            background: none;
            border: none;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .nav-tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .nav-tab:hover {
            color: #667eea;
        }

        /* 搜尋列 */
        .search-controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .search-bar {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .search-bar label {
            font-weight: 600;
            color: #495057;
            min-width: 80px;
        }

        .search-bar input,
        .search-bar select {
            flex: 1;
            min-width: 200px;
            padding: 10px 15px;
            border: 2px solid #dee2e6;
            border-radius: 5px;
            font-size: 16px;
            background: white;
        }

        .search-bar select {
            min-width: 150px;
            cursor: pointer;
        }

        /* 按鈕樣式 */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        /* 產品網格樣式 */
        .products-grid-container {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow-x: auto;
            margin-bottom: 20px;
            width: 100%;
        }

        .products-grid-header {
            display: grid;
            grid-template-columns: 180px 2fr 150px 100px 120px 150px 120px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 2px solid #dee2e6;
            min-width: 1000px;
        }

        .grid-header-item {
            padding: 12px 8px;
            font-weight: 600;
            color: #495057;
            text-align: center;
            border-right: 1px solid #dee2e6;
            white-space: nowrap;
        }

        .grid-header-item:last-child {
            border-right: none;
        }

        .products-grid-body {
            background: white;
        }

        .product-card {
            display: grid;
            grid-template-columns: 180px 2fr 150px 100px 120px 150px 120px;
            border-bottom: 1px solid #dee2e6;
            transition: background-color 0.2s;
            min-width: 1000px;
        }

        .product-card:hover {
            background-color: #f8f9fa;
        }

        .product-card:last-child {
            border-bottom: none;
        }

        .product-card > div {
            padding: 12px 8px;
            border-right: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .product-card > div:last-child {
            border-right: none;
        }

        .product-name {
            text-align: left !important;
            justify-content: flex-start !important;
            flex-direction: column;
            align-items: flex-start !important;
        }

        .product-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .product-ingredients {
            font-size: 14px;
            color: #666;
        }

        .nhi-details {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .nhi-code {
            font-family: 'Courier New', monospace;
            font-weight: 600;
            color: #495057;
        }

        .nhi-level {
            font-size: 14px;
            color: #28a745;
        }

        .price-value {
            font-weight: 600;
            color: #28a745;
            font-size: 18px;
        }

        .price-unit {
            font-size: 14px;
            color: #666;
        }

        .quantity-input {
            width: 50px;
            padding: 4px;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            text-align: center;
            font-size: 14px;
        }

        .quantity-label {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }

        .action-btn {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            padding: 4px;
            border-radius: 3px;
            transition: all 0.2s;
            margin: 0 2px;
        }

        .action-btn.cart {
            color: #667eea;
        }

        .action-btn.favorite {
            color: #e74c3c;
        }

        .action-btn:hover {
            background-color: rgba(0,0,0,0.1);
            transform: scale(1.1);
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-badge.in-stock {
            background: #d4edda;
            color: #155724;
        }

        .status-badge.low-stock {
            background: #fff3cd;
            color: #856404;
        }

        .status-badge.out-of-stock {
            background: #f8d7da;
            color: #721c24;
        }

        /* 訊息提示 */
        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 5px;
            font-weight: 500;
            z-index: 1001;
            animation: slideIn 0.3s ease-out;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .nav-tabs {
                flex-wrap: wrap;
            }

            .nav-tab {
                flex: 1;
                min-width: 100px;
                font-size: 14px;
                padding: 8px 4px;
            }

            .products-grid-header {
                display: none;
            }

            .product-card {
                display: block;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                margin-bottom: 15px;
                padding: 15px;
                min-width: auto;
            }

            .product-card > div {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
                border-right: none;
                border-bottom: 1px solid #eee;
                text-align: left;
            }

            .product-card > div:last-child {
                border-bottom: none;
            }

            .product-card > div::before {
                content: attr(data-label) ": ";
                font-weight: 600;
                color: #495057;
                min-width: 80px;
            }

            .search-bar {
                flex-direction: column;
                gap: 10px;
            }
            
            .search-bar input {
                min-width: auto;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="test-info">
        螢幕寬度: <span id="screen-width"></span>px
    </div>

    <div class="container">
        <div class="breakpoint-indicator">
            <h2>RWD 響應式測試頁面 - 簡化版</h2>
            <p>調整瀏覽器寬度來測試不同螢幕尺寸的效果</p>
        </div>

        <!-- 測試導航標籤 -->
        <div class="test-section">
            <h3>導航標籤測試</h3>
            <nav class="nav-tabs">
                <button class="nav-tab active">產品管理</button>
                <button class="nav-tab">🛒 購物車</button>
                <button class="nav-tab">訂單管理</button>
                <button class="nav-tab">❤️ 我的最愛</button>
                <button class="nav-tab">個人資料</button>
                <button class="nav-tab">📢 訊息</button>
                <button class="nav-tab">💬 與我聯絡</button>
            </nav>
        </div>

        <!-- 測試搜尋欄 -->
        <div class="test-section">
            <h3>搜尋欄測試</h3>
            <div class="search-controls">
                <div class="search-bar">
                    <label>搜尋條件：</label>
                    <input type="text" id="search-input" placeholder="輸入產品名稱、健保代碼或製造商...">
                    <button class="btn btn-primary" onclick="filterProducts()">查詢</button>
                    <button class="btn btn-secondary" onclick="clearFilter()">清除</button>
                </div>
                <div class="search-bar">
                    <label>狀態篩選：</label>
                    <select id="status-filter" onchange="filterProducts()">
                        <option value="">全部狀態</option>
                        <option value="in-stock">供貨中</option>
                        <option value="low-stock">庫存不足</option>
                        <option value="out-of-stock">缺貨</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 測試產品網格 -->
        <div class="test-section">
            <h3>產品網格測試</h3>
            <div class="products-grid-container">
                <div class="products-grid-header">
                    <div class="grid-header-item">健保資訊</div>
                    <div class="grid-header-item">品名 / 成分</div>
                    <div class="grid-header-item">規格</div>
                    <div class="grid-header-item">單價</div>
                    <div class="grid-header-item">數量</div>
                    <div class="grid-header-item">功能</div>
                    <div class="grid-header-item">狀態</div>
                </div>
                <div class="products-grid-body">
                    <div class="product-card">
                        <div class="nhi-info" data-label="健保資訊">
                            <div class="nhi-details">
                                <div class="nhi-code">A123456789</div>
                                <div class="nhi-level">健保價: 25.5</div>
                            </div>
                        </div>
                        <div class="product-name" data-label="品名/成分">
                            <div class="product-title">測試藥品名稱</div>
                            <div class="product-ingredients">主要成分說明</div>
                        </div>
                        <div class="product-dosage" data-label="規格">
                            <div class="dosage-form">錠劑 100mg</div>
                        </div>
                        <div class="product-price" data-label="單價">
                            <div class="price-value">30</div>
                            <div class="price-unit">/元</div>
                        </div>
                        <div class="product-quantity" data-label="數量">
                            <input type="number" class="quantity-input" value="1" min="1">
                            <div class="quantity-label">訂量</div>
                        </div>
                        <div class="product-actions" data-label="功能">
                            <button class="action-btn cart" title="加入購物車">🛒</button>
                            <button class="action-btn favorite" title="收藏">❤️</button>
                        </div>
                        <div class="product-status" data-label="狀態">
                            <span class="status-badge in-stock">供貨中</span>
                        </div>
                    </div>
                    
                    <div class="product-card">
                        <div class="nhi-info" data-label="健保資訊">
                            <div class="nhi-details">
                                <div class="nhi-code">B987654321</div>
                                <div class="nhi-level">健保價: 45.0</div>
                            </div>
                        </div>
                        <div class="product-name" data-label="品名/成分">
                            <div class="product-title">維他命 B 複合錠</div>
                            <div class="product-ingredients">維生素 B1, B2, B6, B12</div>
                        </div>
                        <div class="product-dosage" data-label="規格">
                            <div class="dosage-form">膠囊 50mg</div>
                        </div>
                        <div class="product-price" data-label="單價">
                            <div class="price-value">65</div>
                            <div class="price-unit">/元</div>
                        </div>
                        <div class="product-quantity" data-label="數量">
                            <input type="number" class="quantity-input" value="1" min="1">
                            <div class="quantity-label">訂量</div>
                        </div>
                        <div class="product-actions" data-label="功能">
                            <button class="action-btn cart" title="加入購物車">🛒</button>
                            <button class="action-btn favorite" title="收藏">❤️</button>
                        </div>
                        <div class="product-status" data-label="狀態">
                            <span class="status-badge low-stock">庫存不足</span>
                        </div>
                    </div>
                    
                    <div class="product-card">
                        <div class="nhi-info" data-label="健保資訊">
                            <div class="nhi-details">
                                <div class="nhi-code">C456789123</div>
                                <div class="nhi-level">健保價: 120.5</div>
                            </div>
                        </div>
                        <div class="product-name" data-label="品名/成分">
                            <div class="product-title">感冒糖漿</div>
                            <div class="product-ingredients">對乙醯氨基酚、右美沙芬</div>
                        </div>
                        <div class="product-dosage" data-label="規格">
                            <div class="dosage-form">糖漿 120ml</div>
                        </div>
                        <div class="product-price" data-label="單價">
                            <div class="price-value">150</div>
                            <div class="price-unit">/元</div>
                        </div>
                        <div class="product-quantity" data-label="數量">
                            <input type="number" class="quantity-input" value="1" min="1">
                            <div class="quantity-label">訂量</div>
                        </div>
                        <div class="product-actions" data-label="功能">
                            <button class="action-btn cart" title="加入購物車">🛒</button>
                            <button class="action-btn favorite" title="收藏">❤️</button>
                        </div>
                        <div class="product-status" data-label="狀態">
                            <span class="status-badge out-of-stock">缺貨</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 測試按鈕 -->
        <div class="test-section">
            <h3>按鈕測試</h3>
            <button class="btn btn-primary">主要按鈕</button>
            <button class="btn btn-secondary">次要按鈕</button>
        </div>
    </div>

    <script>
        function updateScreenWidth() {
            document.getElementById('screen-width').textContent = window.innerWidth;
        }
        
        window.addEventListener('resize', updateScreenWidth);
        updateScreenWidth();

        // 處理導航標籤點擊
        document.addEventListener('DOMContentLoaded', function() {
            // 導航標籤功能
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    navTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    showMessage('已切換到：' + this.textContent, 'success');
                });
            });

            // 購物車按鈕功能
            const cartButtons = document.querySelectorAll('.action-btn.cart');
            cartButtons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const productCard = this.closest('.product-card');
                    const productName = productCard.querySelector('.product-title').textContent;
                    showMessage('已將 "' + productName + '" 加入購物車', 'success');
                });
            });

            // 我的最愛按鈕功能
            const favoriteButtons = document.querySelectorAll('.action-btn.favorite');
            favoriteButtons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const productCard = this.closest('.product-card');
                    const productName = productCard.querySelector('.product-title').textContent;
                    
                    if (this.style.color === 'red') {
                        this.style.color = '#e74c3c';
                        showMessage('已從最愛移除 "' + productName + '"', 'success');
                    } else {
                        this.style.color = 'red';
                        showMessage('已將 "' + productName + '" 加入最愛', 'success');
                    }
                });
            });

            // 查詢按鈕功能
            const searchButtons = document.querySelectorAll('.btn-primary');
            searchButtons.forEach(btn => {
                if (btn.textContent.includes('查詢')) {
                    btn.addEventListener('click', function(e) {
                        e.preventDefault();
                        const searchInput = this.parentElement.querySelector('input');
                        const searchValue = searchInput ? searchInput.value : '';
                        showMessage('執行搜尋：' + (searchValue || '全部產品'), 'success');
                    });
                }
            });

            // 清除按鈕功能
            const clearButtons = document.querySelectorAll('.btn-secondary');
            clearButtons.forEach(btn => {
                if (btn.textContent.includes('清除')) {
                    btn.addEventListener('click', function(e) {
                        e.preventDefault();
                        const searchInput = this.parentElement.querySelector('input');
                        if (searchInput) {
                            searchInput.value = '';
                        }
                        showMessage('已清除搜尋條件', 'success');
                    });
                }
            });
        });

        // 顯示訊息函數
        function showMessage(text, type = 'success') {
            const existingMessage = document.querySelector('.message');
            if (existingMessage) {
                existingMessage.remove();
            }

            const message = document.createElement('div');
            message.className = `message ${type}`;
            message.textContent = text;
            document.body.appendChild(message);

            setTimeout(() => {
                if (message.parentNode) {
                    message.remove();
                }
            }, 3000);
        }

        // 產品篩選功能
        function filterProducts() {
            const searchInput = document.getElementById('search-input');
            const statusFilter = document.getElementById('status-filter');
            const productCards = document.querySelectorAll('.product-card');
            
            const searchTerm = searchInput.value.toLowerCase();
            const statusValue = statusFilter.value;
            
            let visibleCount = 0;
            
            productCards.forEach(card => {
                const productTitle = card.querySelector('.product-title').textContent.toLowerCase();
                const nhiCode = card.querySelector('.nhi-code').textContent.toLowerCase();
                const statusBadge = card.querySelector('.status-badge');
                const statusClass = Array.from(statusBadge.classList).find(cls => cls.includes('-stock'));
                
                const matchesSearch = !searchTerm || 
                    productTitle.includes(searchTerm) || 
                    nhiCode.includes(searchTerm);
                    
                const matchesStatus = !statusValue || statusClass === statusValue;
                
                if (matchesSearch && matchesStatus) {
                    card.style.display = '';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });
            
            showMessage(`找到 ${visibleCount} 個符合條件的產品`, 'success');
        }

        // 清除篩選
        function clearFilter() {
            document.getElementById('search-input').value = '';
            document.getElementById('status-filter').value = '';
            
            const productCards = document.querySelectorAll('.product-card');
            productCards.forEach(card => {
                card.style.display = '';
            });
            
            showMessage('已清除所有篩選條件', 'success');
        }
    </script>
</body>
</html>