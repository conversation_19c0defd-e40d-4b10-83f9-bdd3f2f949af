<!DOCTYPE html>
<html lang="zh-TW">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>RWD 測試頁面</title>
    <link rel="stylesheet" href="css/style.css?v=rwd-test" />
    <style>
      .test-info {
        position: fixed;
        top: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px;
        border-radius: 5px;
        font-size: 12px;
        z-index: 1000;
      }

      .breakpoint-indicator {
        background: #667eea;
        color: white;
        padding: 5px 10px;
        margin: 10px 0;
        border-radius: 5px;
        text-align: center;
      }

      .test-section {
        margin: 20px 0;
        padding: 20px;
        border: 2px solid #ddd;
        border-radius: 8px;
      }
    </style>
  </head>
  <body>
    <div class="test-info">螢幕寬度: <span id="screen-width"></span>px</div>

    <div class="container">
      <div class="breakpoint-indicator">
        <h2>RWD 響應式測試頁面</h2>
        <p>調整瀏覽器寬度來測試不同螢幕尺寸的效果</p>
      </div>

      <!-- 測試導航標籤 -->
      <div class="test-section">
        <h3>導航標籤測試</h3>
        <nav class="nav-tabs">
          <button class="nav-tab active">產品管理</button>
          <button class="nav-tab">🛒 購物車</button>
          <button class="nav-tab">訂單管理</button>
          <button class="nav-tab">❤️ 我的最愛</button>
          <button class="nav-tab">個人資料</button>
          <button class="nav-tab">📢 訊息</button>
          <button class="nav-tab">💬 與我聯絡</button>
        </nav>
      </div>

      <!-- 測試搜尋欄 -->
      <div class="test-section">
        <h3>搜尋欄測試</h3>
        <div class="search-controls">
          <div class="search-bar">
            <label>搜尋條件：</label>
            <input
              type="text"
              placeholder="輸入產品名稱、健保代碼或製造商..."
            />
            <button class="btn btn-primary">查詢</button>
            <button class="btn btn-secondary">清除</button>
          </div>
        </div>
      </div>

      <!-- 測試表單 -->
      <div class="test-section">
        <h3>表單測試</h3>
        <div class="form-row">
          <div class="form-group-inline">
            <label>帳號:</label>
            <input type="text" placeholder="輸入帳號" />
          </div>
          <div class="form-group-inline">
            <label>密碼:</label>
            <input type="password" placeholder="輸入密碼" />
          </div>
          <div class="form-group-inline">
            <label>電子郵件:</label>
            <input type="email" placeholder="輸入電子郵件" />
          </div>
        </div>
      </div>

      <!-- 測試產品網格 -->
      <div class="test-section">
        <h3>產品網格測試</h3>
        <div class="products-grid-container">
          <div class="products-grid-header">
            <div class="grid-header-item">健保資訊</div>
            <div class="grid-header-item">品名 / 成分</div>
            <div class="grid-header-item">規格</div>
            <div class="grid-header-item">單價</div>
            <div class="grid-header-item">數量</div>
            <div class="grid-header-item">功能</div>
            <div class="grid-header-item">狀態</div>
          </div>
          <div class="products-grid-body">
            <div class="product-card">
              <div class="nhi-info" data-label="健保資訊">
                <div class="nhi-details">
                  <div class="nhi-code">A123456789</div>
                  <div class="nhi-level">健保價: 25.5</div>
                </div>
              </div>
              <div class="product-name" data-label="品名/成分">
                <div class="product-title">測試藥品名稱</div>
                <div class="product-ingredients">主要成分說明</div>
              </div>
              <div class="product-dosage" data-label="規格">
                <div class="dosage-form">錠劑 100mg</div>
              </div>
              <div class="product-price" data-label="單價">
                <div class="price-value">30</div>
                <div class="price-unit">/元</div>
              </div>
              <div class="product-quantity" data-label="數量">
                <input type="number" class="quantity-input" value="1" min="1" />
                <div class="quantity-label">訂量</div>
              </div>
              <div class="product-actions" data-label="功能">
                <button class="action-btn cart" title="加入購物車">🛒</button>
                <button class="action-btn favorite" title="收藏">❤️</button>
              </div>
              <div class="product-status" data-label="狀態">
                <span class="status-badge in-stock">供貨中</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 測試按鈕 -->
      <div class="test-section">
        <h3>按鈕測試</h3>
        <button class="btn btn-primary">主要按鈕</button>
        <button class="btn btn-secondary">次要按鈕</button>
        <button class="btn btn-outline">外框按鈕</button>
      </div>
    </div>

    <script>
      function updateScreenWidth() {
        document.getElementById("screen-width").textContent = window.innerWidth;
      }

      window.addEventListener("resize", updateScreenWidth);
      updateScreenWidth();

      // 處理導航標籤點擊
      document.addEventListener("DOMContentLoaded", function () {
        // 導航標籤功能
        const navTabs = document.querySelectorAll(".nav-tab");
        navTabs.forEach((tab) => {
          tab.addEventListener("click", function () {
            // 移除所有 active 類別
            navTabs.forEach((t) => t.classList.remove("active"));
            // 添加 active 到當前標籤
            this.classList.add("active");

            // 顯示訊息
            showMessage("已切換到：" + this.textContent, "success");
          });
        });

        // 購物車按鈕功能
        const cartButtons = document.querySelectorAll(".action-btn.cart");
        cartButtons.forEach((btn) => {
          btn.addEventListener("click", function (e) {
            e.preventDefault();
            const productCard = this.closest(".product-card");
            const productName =
              productCard.querySelector(".product-title").textContent;
            showMessage('已將 "' + productName + '" 加入購物車', "success");
          });
        });

        // 我的最愛按鈕功能
        const favoriteButtons = document.querySelectorAll(
          ".action-btn.favorite"
        );
        favoriteButtons.forEach((btn) => {
          btn.addEventListener("click", function (e) {
            e.preventDefault();
            const productCard = this.closest(".product-card");
            const productName =
              productCard.querySelector(".product-title").textContent;

            // 切換愛心狀態
            if (this.style.color === "red") {
              this.style.color = "#e74c3c";
              showMessage('已從最愛移除 "' + productName + '"', "success");
            } else {
              this.style.color = "red";
              showMessage('已將 "' + productName + '" 加入最愛', "success");
            }
          });
        });

        // 查詢按鈕功能
        const searchButtons = document.querySelectorAll(".btn-primary");
        searchButtons.forEach((btn) => {
          if (btn.textContent.includes("查詢")) {
            btn.addEventListener("click", function (e) {
              e.preventDefault();
              const searchInput = this.parentElement.querySelector("input");
              const searchValue = searchInput ? searchInput.value : "";
              showMessage(
                "執行搜尋：" + (searchValue || "全部產品"),
                "success"
              );
            });
          }
        });

        // 清除按鈕功能
        const clearButtons = document.querySelectorAll(".btn-secondary");
        clearButtons.forEach((btn) => {
          if (btn.textContent.includes("清除")) {
            btn.addEventListener("click", function (e) {
              e.preventDefault();
              const searchInput = this.parentElement.querySelector("input");
              if (searchInput) {
                searchInput.value = "";
              }
              showMessage("已清除搜尋條件", "success");
            });
          }
        });
      });

      // 顯示訊息函數
      function showMessage(text, type = "success") {
        // 移除現有訊息
        const existingMessage = document.querySelector(".message");
        if (existingMessage) {
          existingMessage.remove();
        }

        // 創建新訊息
        const message = document.createElement("div");
        message.className = `message ${type}`;
        message.textContent = text;
        document.body.appendChild(message);

        // 3秒後自動移除
        setTimeout(() => {
          if (message.parentNode) {
            message.remove();
          }
        }, 3000);
      }
    </script>
  </body>
</html>
