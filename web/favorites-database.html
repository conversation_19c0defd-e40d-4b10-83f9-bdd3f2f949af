<!DOCTYPE html>
<html lang="zh-TW">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>完整我的最愛功能 - 資料庫版</title>
    <link rel="stylesheet" href="css/style.css?v=favorites-db" />
    <style>
      /* 額外的樣式增強 */
      .favorites-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
      }

      .favorites-stats {
        display: flex;
        gap: 20px;
        align-items: center;
      }

      .stat-item {
        text-align: center;
      }

      .stat-number {
        font-size: 1.5rem;
        font-weight: bold;
        display: block;
      }

      .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
      }

      .action-btn.favorite {
        transition: all 0.3s ease;
        transform-origin: center;
      }

      .action-btn.favorite:not(.active) {
        color: #ccc;
      }

      .action-btn.favorite.active {
        color: #e74c3c;
        transform: scale(1.1);
      }

      .action-btn.favorite:hover {
        color: #e74c3c;
        transform: scale(1.2);
      }

      .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .btn.loading {
        opacity: 0.7;
        pointer-events: none;
      }

      .sync-status {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px 15px;
        border-radius: 20px;
        font-size: 14px;
        z-index: 1000;
      }

      .sync-status.syncing {
        background: rgba(102, 126, 234, 0.9);
      }

      .sync-status.success {
        background: rgba(40, 167, 69, 0.9);
      }

      .sync-status.error {
        background: rgba(220, 53, 69, 0.9);
      }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- 標題區域 -->
      <header>
        <div class="header-content">
          <div class="logo-title">
            <img src="images/logo.png" alt="公司標誌" class="header-logo" />
            <div class="title-section">
              <h1>台南社區藥局藥品供應平台</h1>
              <p class="subtitle">完整我的最愛功能 - 資料庫整合版</p>
            </div>
          </div>
          <div id="user-info" class="user-info">
            <span id="user-display-name">使用者</span>
            <button id="logout-btn" class="btn btn-secondary">登出</button>
          </div>
        </div>
      </header>

      <!-- 導航標籤 -->
      <nav class="nav-tabs">
        <button class="nav-tab active" data-tab="products">📦 產品管理</button>
        <button class="nav-tab" data-tab="favorites">
          ❤️ 我的最愛 (<span id="favorites-count">0</span>)
        </button>
        <button class="nav-tab" data-tab="cart">
          🛒 購物車 (<span id="cart-count">0</span>)
        </button>
      </nav>

      <!-- 產品管理標籤 -->
      <div id="products-tab" class="tab-content active">
        <div class="product-header">
          <h3>產品訂購目錄</h3>
        </div>

        <!-- 搜尋控制 -->
        <div class="search-controls">
          <div class="search-bar">
            <label>搜尋條件：</label>
            <input
              type="text"
              id="search-input"
              placeholder="輸入產品名稱、健保代碼或製造商..."
            />
            <button class="btn btn-primary" onclick="searchProducts()">
              查詢
            </button>
            <button class="btn btn-secondary" onclick="clearSearch()">
              清除
            </button>
          </div>
          <div class="filter-options">
            <select id="category-filter" onchange="filterProducts()">
              <option value="">全部分類</option>
              <option value="prescription">處方藥</option>
              <option value="otc">成藥</option>
              <option value="supplement">保健品</option>
            </select>
            <select id="status-filter" onchange="filterProducts()">
              <option value="">全部狀態</option>
              <option value="available">有庫存</option>
              <option value="low_stock">庫存不足</option>
              <option value="out_of_stock">缺貨</option>
            </select>
          </div>
        </div>

        <!-- 產品表格 -->
        <div class="products-table-container">
          <table class="products-table">
            <thead>
              <tr>
                <th>健保代碼</th>
                <th>產品名稱</th>
                <th>製造商</th>
                <th>規格</th>
                <th>健保價</th>
                <th>售價</th>
                <th>庫存</th>
                <th>數量</th>
                <th>功能</th>
                <th>狀態</th>
              </tr>
            </thead>
            <tbody id="products-table-body">
              <tr>
                <td colspan="10" style="text-align: center; padding: 40px">
                  <div class="loading-spinner"></div>
                  <p style="margin-top: 10px">載入產品資料中...</p>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分頁控制 -->
        <div class="table-pagination">
          <div class="pagination-info" id="pagination-info">載入中...</div>
          <div class="pagination-controls">
            <button
              class="btn btn-secondary"
              onclick="previousPage()"
              id="prev-btn"
              disabled
            >
              上一頁
            </button>
            <div class="page-input-group">
              <span>第</span>
              <input
                type="number"
                id="page-input"
                value="1"
                min="1"
                onchange="goToPage(this.value)"
              />
              <span>頁</span>
            </div>
            <button
              class="btn btn-secondary"
              onclick="nextPage()"
              id="next-btn"
            >
              下一頁
            </button>
          </div>
        </div>
      </div>

      <!-- 我的最愛標籤 -->
      <div id="favorites-tab" class="tab-content">
        <div class="favorites-header">
          <div>
            <h2>❤️ 我的最愛</h2>
            <p>您收藏的產品列表</p>
          </div>
          <div class="favorites-stats">
            <div class="stat-item">
              <span class="stat-number" id="total-favorites">0</span>
              <span class="stat-label">收藏產品</span>
            </div>
            <button class="btn btn-danger" onclick="clearAllFavorites()">
              清空所有最愛
            </button>
          </div>
        </div>

        <div class="products-table-container">
          <table class="products-table">
            <thead>
              <tr>
                <th>健保代碼</th>
                <th>產品名稱</th>
                <th>製造商</th>
                <th>規格</th>
                <th>健保價</th>
                <th>售價</th>
                <th>庫存</th>
                <th>數量</th>
                <th>功能</th>
                <th>狀態</th>
              </tr>
            </thead>
            <tbody id="favorites-table-body">
              <tr>
                <td colspan="10" style="text-align: center; padding: 40px">
                  <div class="loading-spinner"></div>
                  <p style="margin-top: 10px">載入我的最愛...</p>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 購物車標籤 -->
      <div id="cart-tab" class="tab-content">
        <div class="card">
          <h2>🛒 購物車</h2>
          <div id="cart-items">
            <p style="text-align: center; padding: 40px; color: #666">
              購物車功能開發中...
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 同步狀態指示器 -->
    <div id="sync-status" class="sync-status" style="display: none">
      <span id="sync-message">同步中...</span>
    </div>

    <!-- 引入配置和主要腳本 -->
    <script src="js/config.js"></script>
    <script>
      // 全域變數
      let currentProducts = [];
      let currentFavorites = [];
      let currentPage = 1;
      let pageSize = 20;
      let totalPages = 1;
      let isLoading = false;

      // API 基礎設定
      const API_BASE = window.CONFIG.API_BASE;
      let authToken = localStorage.getItem("authToken");
      let currentUser = JSON.parse(localStorage.getItem("currentUser") || "{}");

      // 初始化
      document.addEventListener("DOMContentLoaded", function () {
        console.log("初始化我的最愛功能...");
        console.log("API_BASE:", API_BASE);
        console.log("authToken:", authToken ? "已設定" : "未設定");

        setupEventListeners();
        loadProducts();
        updateFavoritesCount();
      });

      // 設置事件監聽器
      function setupEventListeners() {
        // 標籤切換
        document.querySelectorAll(".nav-tab").forEach((tab) => {
          tab.addEventListener("click", function () {
            const tabName = this.dataset.tab;
            switchTab(tabName);
          });
        });
      }

      // 標籤切換
      function switchTab(tabName) {
        // 更新標籤狀態
        document.querySelectorAll(".nav-tab").forEach((tab) => {
          tab.classList.remove("active");
        });
        document
          .querySelector(`[data-tab="${tabName}"]`)
          .classList.add("active");

        // 更新內容
        document.querySelectorAll(".tab-content").forEach((content) => {
          content.classList.remove("active");
        });
        document.getElementById(`${tabName}-tab`).classList.add("active");

        // 載入對應內容
        switch (tabName) {
          case "products":
            loadProducts();
            break;
          case "favorites":
            loadFavorites();
            break;
          case "cart":
            // 購物車功能待實作
            break;
        }
      }

      // API 請求函數
      async function apiRequest(url, options = {}) {
        const fullUrl = `${API_BASE}${url}`;
        console.log("發送 API 請求到:", fullUrl);

        const defaultOptions = {
          headers: {
            "Content-Type": "application/json",
            ...(authToken && { Authorization: `Bearer ${authToken}` }),
          },
        };

        const finalOptions = {
          ...defaultOptions,
          ...options,
          headers: {
            ...defaultOptions.headers,
            ...options.headers,
          },
        };

        try {
          showSyncStatus("syncing", "同步中...");
          const response = await fetch(fullUrl, finalOptions);

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();
          showSyncStatus("success", "同步完成");
          return data;
        } catch (error) {
          console.error("API 請求失敗:", error);
          showSyncStatus("error", "同步失敗");
          throw error;
        }
      }

      // 載入產品列表
      async function loadProducts() {
        if (isLoading) return;
        isLoading = true;

        try {
          const tableBody = document.getElementById("products-table-body");
          tableBody.innerHTML = `
                    <tr>
                        <td colspan="10" style="text-align: center; padding: 40px;">
                            <div class="loading-spinner"></div>
                            <p style="margin-top: 10px;">載入產品資料中...</p>
                        </td>
                    </tr>
                `;

          // 構建查詢參數
          const params = new URLSearchParams({
            page: currentPage,
            page_size: pageSize,
          });

          const searchTerm = document.getElementById("search-input")?.value;
          if (searchTerm) {
            params.append("search", searchTerm);
          }

          const data = await apiRequest(`/api/products/?${params.toString()}`);

          if (data.success) {
            currentProducts = data.data || [];
            totalPages = Math.ceil((data.total || 0) / pageSize);

            await renderProductsTable();
            updatePaginationInfo();

            // 載入我的最愛狀態
            await loadFavoriteStatus();
          } else {
            throw new Error(data.message || "載入產品失敗");
          }
        } catch (error) {
          console.error("載入產品失敗:", error);
          const tableBody = document.getElementById("products-table-body");
          tableBody.innerHTML = `
                    <tr>
                        <td colspan="10" style="text-align: center; padding: 40px; color: #e74c3c;">
                            <p>載入失敗：${error.message}</p>
                            <button class="btn btn-primary" onclick="loadProducts()">重新載入</button>
                        </td>
                    </tr>
                `;
        } finally {
          isLoading = false;
        }
      }

      // 渲染產品表格
      async function renderProductsTable() {
        const tableBody = document.getElementById("products-table-body");

        if (currentProducts.length === 0) {
          tableBody.innerHTML = `
                    <tr>
                        <td colspan="10" style="text-align: center; padding: 40px; color: #666;">
                            <p>沒有找到符合條件的產品</p>
                        </td>
                    </tr>
                `;
          return;
        }

        tableBody.innerHTML = currentProducts
          .map(
            (product) => `
                <tr data-product-id="${product.id}">
                    <td class="product-code">${
                      product.insurance_code || "N/A"
                    }</td>
                    <td class="product-name-cell">
                        <strong>${product.name}</strong>
                        ${
                          product.ingredients
                            ? `<br><small style="color: #666;">${product.ingredients}</small>`
                            : ""
                        }
                    </td>
                    <td>${product.manufacturer || "N/A"}</td>
                    <td>${product.dosage_form || "N/A"}</td>
                    <td class="product-price">$${
                      product.insurance_price || 0
                    }</td>
                    <td class="product-price">$${product.price || 0}</td>
                    <td class="stock-quantity ${getStockClass(
                      product.stock_quantity
                    )}">${product.stock_quantity || 0}</td>
                    <td>
                        <input type="number" class="quantity-input-cart" value="1" min="1" max="99">
                    </td>
                    <td class="product-actions">
                        <button class="btn-cart-icon" onclick="addToCart(${
                          product.id
                        })" title="加入購物車">
                            🛒
                        </button>
                        <button class="btn-cart-icon favorite-btn" 
                                data-product-id="${product.id}"
                                onclick="toggleFavorite(${product.id})" 
                                title="加入我的最愛">
                            ❤️
                        </button>
                    </td>
                    <td>
                        <span class="product-status ${getStatusClass(
                          product.stock_quantity
                        )}">${getStatusText(product.stock_quantity)}</span>
                    </td>
                </tr>
            `
          )
          .join("");
      }

      // 載入我的最愛列表
      async function loadFavorites() {
        try {
          const tableBody = document.getElementById("favorites-table-body");
          tableBody.innerHTML = `
                    <tr>
                        <td colspan="10" style="text-align: center; padding: 40px;">
                            <div class="loading-spinner"></div>
                            <p style="margin-top: 10px;">載入我的最愛...</p>
                        </td>
                    </tr>
                `;

          const data = await apiRequest("/api/favorites/");

          if (data.success) {
            currentFavorites = data.data || [];
            await renderFavoritesTable();
            updateFavoritesCount();
          } else {
            throw new Error(data.message || "載入我的最愛失敗");
          }
        } catch (error) {
          console.error("載入我的最愛失敗:", error);
          const tableBody = document.getElementById("favorites-table-body");

          if (error.message.includes("404")) {
            tableBody.innerHTML = `
                        <tr>
                            <td colspan="10" style="text-align: center; padding: 40px; color: #666;">
                                <p>❤️ 我的最愛功能</p>
                                <p>此功能需要後端 API 支援</p>
                                <p>目前為展示模式</p>
                            </td>
                        </tr>
                    `;
          } else {
            tableBody.innerHTML = `
                        <tr>
                            <td colspan="10" style="text-align: center; padding: 40px; color: #e74c3c;">
                                <p>載入失敗：${error.message}</p>
                                <button class="btn btn-primary" onclick="loadFavorites()">重新載入</button>
                            </td>
                        </tr>
                    `;
          }
        }
      }

      // 渲染我的最愛表格
      async function renderFavoritesTable() {
        const tableBody = document.getElementById("favorites-table-body");

        if (currentFavorites.length === 0) {
          tableBody.innerHTML = `
                    <tr>
                        <td colspan="10" style="text-align: center; padding: 40px; color: #666;">
                            <div style="font-size: 3rem; margin-bottom: 20px;">❤️</div>
                            <h3>還沒有收藏任何產品</h3>
                            <p>在產品列表中點擊愛心圖標來收藏您喜歡的產品</p>
                        </td>
                    </tr>
                `;
          return;
        }

        tableBody.innerHTML = currentFavorites
          .map(
            (product) => `
                <tr data-product-id="${product.id}">
                    <td class="product-code">${
                      product.insurance_code || "N/A"
                    }</td>
                    <td class="product-name-cell">
                        <strong>${product.name}</strong>
                        ${
                          product.ingredients
                            ? `<br><small style="color: #666;">${product.ingredients}</small>`
                            : ""
                        }
                    </td>
                    <td>${product.manufacturer || "N/A"}</td>
                    <td>${product.dosage_form || "N/A"}</td>
                    <td class="product-price">$${
                      product.insurance_price || 0
                    }</td>
                    <td class="product-price">$${product.price || 0}</td>
                    <td class="stock-quantity ${getStockClass(
                      product.stock_quantity
                    )}">${product.stock_quantity || 0}</td>
                    <td>
                        <input type="number" class="quantity-input-cart" value="1" min="1" max="99">
                    </td>
                    <td class="product-actions">
                        <button class="btn-cart-icon" onclick="addToCart(${
                          product.id
                        })" title="加入購物車">
                            🛒
                        </button>
                        <button class="btn-cart-icon favorite-btn active" 
                                data-product-id="${product.id}"
                                onclick="toggleFavorite(${product.id})" 
                                title="移除我的最愛">
                            ❤️
                        </button>
                    </td>
                    <td>
                        <span class="product-status ${getStatusClass(
                          product.stock_quantity
                        )}">${getStatusText(product.stock_quantity)}</span>
                    </td>
                </tr>
            `
          )
          .join("");
      }

      // 載入我的最愛狀態
      async function loadFavoriteStatus() {
        try {
          const productIds = currentProducts.map((p) => p.id);
          if (productIds.length === 0) return;

          // 批量檢查我的最愛狀態
          for (const productId of productIds) {
            try {
              const data = await apiRequest(
                `/api/favorites/check/${productId}`
              );
              const favoriteBtn = document.querySelector(
                `[data-product-id="${productId}"].favorite-btn`
              );

              if (favoriteBtn) {
                if (data.success && data.is_favorite) {
                  favoriteBtn.classList.add("active");
                } else {
                  favoriteBtn.classList.remove("active");
                }
              }
            } catch (error) {
              console.warn(`檢查產品 ${productId} 最愛狀態失敗:`, error);
            }
          }
        } catch (error) {
          console.error("載入我的最愛狀態失敗:", error);
        }
      }

      // 切換我的最愛狀態
      async function toggleFavorite(productId) {
        try {
          const favoriteBtn = document.querySelector(
            `[data-product-id="${productId}"].favorite-btn`
          );
          const product =
            currentProducts.find((p) => p.id === productId) ||
            currentFavorites.find((p) => p.id === productId);

          if (!product) {
            showMessage("找不到產品資訊", "error");
            return;
          }

          // 顯示載入狀態
          if (favoriteBtn) {
            favoriteBtn.classList.add("loading");
            favoriteBtn.style.opacity = "0.5";
          }

          const data = await apiRequest("/api/favorites/toggle", {
            method: "POST",
            body: JSON.stringify({
              product_id: productId,
            }),
          });

          if (data.success) {
            // 更新按鈕狀態
            if (favoriteBtn) {
              if (data.is_favorite) {
                favoriteBtn.classList.add("active");
              } else {
                favoriteBtn.classList.remove("active");
              }
            }

            // 顯示成功訊息
            const action = data.is_favorite ? "加入" : "移除";
            showMessage(`已${action}我的最愛：${product.name}`, "success");

            // 更新計數
            updateFavoritesCount();

            // 如果當前在我的最愛頁面，重新載入
            if (
              document
                .getElementById("favorites-tab")
                .classList.contains("active")
            ) {
              setTimeout(() => loadFavorites(), 500);
            }
          } else {
            throw new Error(data.message || "操作失敗");
          }
        } catch (error) {
          console.error("切換我的最愛狀態失敗:", error);

          if (error.message.includes("404")) {
            showMessage("我的最愛功能需要後端 API 支援", "info");
          } else {
            showMessage(`操作失敗：${error.message}`, "error");
          }
        } finally {
          // 恢復按鈕狀態
          const favoriteBtn = document.querySelector(
            `[data-product-id="${productId}"].favorite-btn`
          );
          if (favoriteBtn) {
            favoriteBtn.classList.remove("loading");
            favoriteBtn.style.opacity = "";
          }
        }
      }

      // 清空所有我的最愛
      async function clearAllFavorites() {
        if (!confirm("確定要清空所有我的最愛嗎？此操作無法復原。")) {
          return;
        }

        try {
          const data = await apiRequest("/api/favorites/clear", {
            method: "DELETE",
          });

          if (data.success) {
            showMessage("已清空所有我的最愛", "success");

            // 重新載入我的最愛列表
            loadFavorites();

            // 更新產品列表中的愛心狀態
            document.querySelectorAll(".favorite-btn").forEach((btn) => {
              btn.classList.remove("active");
            });

            updateFavoritesCount();
          } else {
            throw new Error(data.message || "清空失敗");
          }
        } catch (error) {
          console.error("清空我的最愛失敗:", error);
          showMessage(`清空失敗：${error.message}`, "error");
        }
      }

      // 更新我的最愛計數
      async function updateFavoritesCount() {
        try {
          const data = await apiRequest("/api/favorites/count");
          const count = data.success ? data.count || 0 : 0;

          document.getElementById("favorites-count").textContent = count;
          document.getElementById("total-favorites").textContent = count;
        } catch (error) {
          console.warn("更新我的最愛計數失敗:", error);
          document.getElementById("favorites-count").textContent = "0";
          document.getElementById("total-favorites").textContent = "0";
        }
      }

      // 加入購物車
      function addToCart(productId) {
        const product =
          currentProducts.find((p) => p.id === productId) ||
          currentFavorites.find((p) => p.id === productId);

        if (product) {
          showMessage(`已將 "${product.name}" 加入購物車`, "success");
          // TODO: 實作購物車功能
        }
      }

      // 搜尋產品
      function searchProducts() {
        currentPage = 1;
        loadProducts();
      }

      // 清除搜尋
      function clearSearch() {
        document.getElementById("search-input").value = "";
        document.getElementById("category-filter").value = "";
        document.getElementById("status-filter").value = "";
        currentPage = 1;
        loadProducts();
      }

      // 篩選產品
      function filterProducts() {
        currentPage = 1;
        loadProducts();
      }

      // 分頁功能
      function previousPage() {
        if (currentPage > 1) {
          currentPage--;
          loadProducts();
        }
      }

      function nextPage() {
        if (currentPage < totalPages) {
          currentPage++;
          loadProducts();
        }
      }

      function goToPage(page) {
        const pageNum = parseInt(page);
        if (pageNum >= 1 && pageNum <= totalPages) {
          currentPage = pageNum;
          loadProducts();
        }
      }

      // 更新分頁資訊
      function updatePaginationInfo() {
        const info = document.getElementById("pagination-info");
        const prevBtn = document.getElementById("prev-btn");
        const nextBtn = document.getElementById("next-btn");
        const pageInput = document.getElementById("page-input");

        const start = (currentPage - 1) * pageSize + 1;
        const end = Math.min(currentPage * pageSize, currentProducts.length);

        info.textContent = `顯示第 ${start}-${end} 筆，第 ${currentPage} 頁，共 ${totalPages} 頁`;

        prevBtn.disabled = currentPage <= 1;
        nextBtn.disabled = currentPage >= totalPages;
        pageInput.value = currentPage;
        pageInput.max = totalPages;
      }

      // 輔助函數
      function getStockClass(stock) {
        if (stock <= 0) return "stock-out";
        if (stock <= 10) return "stock-low";
        return "";
      }

      function getStatusClass(stock) {
        if (stock <= 0) return "out-of-stock";
        if (stock <= 10) return "low-stock";
        return "in-stock";
      }

      function getStatusText(stock) {
        if (stock <= 0) return "缺貨";
        if (stock <= 10) return "庫存不足";
        return "供貨中";
      }

      // 顯示同步狀態
      function showSyncStatus(type, message) {
        const syncStatus = document.getElementById("sync-status");
        const syncMessage = document.getElementById("sync-message");

        syncStatus.className = `sync-status ${type}`;
        syncMessage.textContent = message;
        syncStatus.style.display = "block";

        if (type !== "syncing") {
          setTimeout(() => {
            syncStatus.style.display = "none";
          }, 2000);
        }
      }

      // 顯示訊息
      function showMessage(text, type = "success") {
        const existingMessage = document.querySelector(".message");
        if (existingMessage) {
          existingMessage.remove();
        }

        const message = document.createElement("div");
        message.className = `message ${type}`;
        message.textContent = text;
        document.body.appendChild(message);

        setTimeout(() => {
          if (message.parentNode) {
            message.remove();
          }
        }, 3000);
      }
    </script>
  </body>
</html>
