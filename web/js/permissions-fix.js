// 權限系統修復方案
// 這個檔案包含了修正後的權限顯示邏輯

// 修正版的 updateNavigationTabs 函數
function updateNavigationTabsFixed(isAdmin, isUser) {
  console.log('updateNavigationTabs 被調用:', { isAdmin, isUser });

  // 不使用 setTimeout，直接更新
  const productTab = document.querySelector('[data-tab="products"]');
  const cartTab = document.querySelector('[data-tab="cart"]');
  const ordersTab = document.querySelector('[data-tab="orders"]');
  const profileTab = document.querySelector('[data-tab="profile"]');
  const promotionsTab = document.querySelector('[data-tab="promotions-view"]');
  const adminTab = document.querySelector('[data-tab="admin"]');

  console.log('找到的標籤:', { 
    productTab: !!productTab, 
    cartTab: !!cartTab, 
    ordersTab: !!ordersTab, 
    profileTab: !!profileTab, 
    promotionsTab: !!promotionsTab, 
    adminTab: !!adminTab 
  });

  // 定義顯示/隱藏函數
  function showTab(tab, text) {
    if (!tab) return;
    tab.style.display = '';  // 使用預設的 display 值，而不是強制設定 inline-block
    tab.style.visibility = 'visible';
    tab.classList.remove('hidden');  // 移除可能的 hidden 類別
    if (text) tab.textContent = text;
  }

  function hideTab(tab) {
    if (!tab) return;
    tab.style.display = 'none';
    tab.style.visibility = 'hidden';
    tab.classList.add('hidden');  // 添加 hidden 類別
  }

  if (isAdmin) {
    // 管理員界面
    console.log('設定管理員界面...');
    showTab(productTab, "📦 產品管理");
    hideTab(cartTab);  // 管理員不需要購物車
    showTab(ordersTab, "📋 訂單管理");
    showTab(profileTab);
    hideTab(promotionsTab);  // 管理員不需要訊息標籤，在系統管理中管理
    showTab(adminTab, "⚙️ 系統管理");
    console.log('管理員界面設定完成');
  } else {
    // 一般用戶界面
    console.log('設定一般用戶界面...');
    showTab(productTab, "🛒 產品採購");
    showTab(cartTab, "🛒 購物車");
    showTab(ordersTab, "📋 我的訂單");
    showTab(profileTab);
    showTab(promotionsTab, "📢 促銷訊息");
    hideTab(adminTab);  // 一般用戶無系統管理權限
    console.log('一般用戶界面設定完成');
  }

  // 觸發重繪以確保變更生效
  const navTabs = document.querySelector('.nav-tabs');
  if (navTabs) {
    navTabs.style.display = 'none';
    navTabs.offsetHeight; // 觸發重繪
    navTabs.style.display = '';
  }
}

// 改進版的 updateUIBasedOnPermissions 函數
function updateUIBasedOnPermissionsFixed() {
  if (!currentUser || !currentUser.permissions) {
    console.error('無法更新 UI：缺少用戶權限資料');
    return;
  }

  const permissions = currentUser.permissions;
  const isAdmin = permissions && permissions.role_name === 'admin';
  const isUser = !isAdmin;

  console.log('權限檢查結果:', {
    username: currentUser.username,
    role: permissions.role_name,
    isAdmin: isAdmin,
    isUser: isUser,
    permissionCount: permissions.permissions?.length || 0
  });

  // 使用修正版的函數更新導航標籤
  updateNavigationTabsFixed(isAdmin, isUser);

  // 更新用戶信息顯示
  updateUserRoleDisplay(permissions, isAdmin);

  // 根據權限調整功能訪問
  updateFeatureAccess(permissions, isAdmin, isUser);

  // 更新管理員標籤內容
  if (isAdmin) {
    updateAdminTabContent(permissions);
  }

  // 添加診斷資訊到 console
  console.log('UI 更新完成，當前標籤狀態:');
  document.querySelectorAll('.nav-tab').forEach(tab => {
    const dataTab = tab.getAttribute('data-tab');
    const display = window.getComputedStyle(tab).display;
    const visibility = window.getComputedStyle(tab).visibility;
    console.log(`  ${dataTab}: display=${display}, visibility=${visibility}`);
  });
}

// 診斷函數：檢查所有標籤的當前狀態
function diagnoseTabs() {
  console.log('=== 標籤診斷開始 ===');
  
  const tabs = [
    'products', 'cart', 'orders', 'profile', 'promotions-view', 'admin'
  ];
  
  tabs.forEach(tabName => {
    const tab = document.querySelector(`[data-tab="${tabName}"]`);
    if (tab) {
      const computed = window.getComputedStyle(tab);
      console.log(`${tabName}:`, {
        display: computed.display,
        visibility: computed.visibility,
        innerHTML: tab.textContent,
        classList: Array.from(tab.classList),
        inlineStyle: {
          display: tab.style.display,
          visibility: tab.style.visibility
        }
      });
    } else {
      console.log(`${tabName}: 元素不存在`);
    }
  });
  
  console.log('=== 標籤診斷結束 ===');
}

// 修復函數：強制重置所有標籤並重新應用權限
function applyPermissionsFix() {
  console.log('開始應用權限修復...');
  
  // 先重置所有標籤的樣式
  document.querySelectorAll('.nav-tab').forEach(tab => {
    tab.style.display = '';
    tab.style.visibility = '';
    tab.classList.remove('hidden');
  });
  
  // 重新應用權限設定
  if (currentUser && currentUser.permissions) {
    updateUIBasedOnPermissionsFixed();
    console.log('權限修復應用完成');
  } else {
    console.error('無法應用修復：缺少用戶資料');
  }
}

// 監聽函數：監控標籤變化
function monitorTabChanges() {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && 
          (mutation.attributeName === 'style' || mutation.attributeName === 'class')) {
        const tab = mutation.target;
        if (tab.classList.contains('nav-tab')) {
          const dataTab = tab.getAttribute('data-tab');
          console.log(`標籤變化檢測: ${dataTab}`, {
            display: tab.style.display,
            visibility: tab.style.visibility,
            classList: Array.from(tab.classList)
          });
        }
      }
    });
  });

  // 開始監控所有導航標籤
  document.querySelectorAll('.nav-tab').forEach(tab => {
    observer.observe(tab, {
      attributes: true,
      attributeFilter: ['style', 'class']
    });
  });

  console.log('標籤變化監控已啟動');
  return observer;
}

// 導出函數供外部使用
if (typeof window !== 'undefined') {
  window.permissionsFix = {
    updateNavigationTabsFixed,
    updateUIBasedOnPermissionsFixed,
    diagnoseTabs,
    applyPermissionsFix,
    monitorTabChanges
  };
  
  console.log('權限修復模組已載入。可用函數:');
  console.log('- permissionsFix.diagnoseTabs() : 診斷標籤狀態');
  console.log('- permissionsFix.applyPermissionsFix() : 應用修復');
  console.log('- permissionsFix.monitorTabChanges() : 啟動變化監控');
}
