<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格寬度一致性修正測試</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4a9eff;
        }
        .test-description {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            color: #1565c0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🔧 表格寬度一致性修正測試</h1>
        
        <div class="test-description">
            <h3>修正內容：</h3>
            <ul>
                <li>✅ 移除標題的 white-space: nowrap 限制</li>
                <li>✅ 為表格內容添加 overflow: auto 讓各自可以捲動</li>
                <li>✅ 確保標題和內容使用相同的 grid-template-columns</li>
                <li>✅ 整理重複的CSS到相近位置</li>
                <li>✅ 使用 text-overflow: ellipsis 處理溢出文字</li>
            </ul>
        </div>

        <h2>測試案例：包含長文字的產品表格</h2>
        
        <div class="products-grid-container">
            <div class="products-grid-header">
                <div class="grid-header-item">健保資訊</div>
                <div class="grid-header-item">品名 / 成分</div>
                <div class="grid-header-item">規格</div>
                <div class="grid-header-item">單價</div>
                <div class="grid-header-item">數量</div>
                <div class="grid-header-item">功能</div>
                <div class="grid-header-item">狀態</div>
            </div>
            <div class="products-grid-body">
                <!-- 測試案例 1: 正常長度文字 -->
                <div class="product-card">
                    <div class="nhi-info" data-label="健保資訊">
                        <div class="drug-icon">💊</div>
                        <div class="nhi-details">
                            <div class="nhi-code">A123456789</div>
                            <div class="nhi-level">健保價: 25.5</div>
                        </div>
                    </div>
                    <div class="product-name" data-label="品名/成分">
                        <div class="product-title">普通藥品名稱</div>
                        <div class="product-ingredients">主要成分說明</div>
                    </div>
                    <div class="product-dosage" data-label="規格">
                        <div class="dosage-form">錠劑 10mg</div>
                    </div>
                    <div class="product-price" data-label="單價">
                        <div class="price-value">50</div>
                        <div class="price-unit">/元</div>
                    </div>
                    <div class="product-quantity" data-label="數量">
                        <input type="number" class="quantity-input" value="1" min="1">
                        <div class="quantity-label">訂量</div>
                    </div>
                    <div class="product-actions" data-label="功能">
                        <button style="background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 3px;">+</button>
                        <button style="background: #ffc107; color: white; border: none; padding: 5px 10px; border-radius: 3px;">♥</button>
                    </div>
                    <div class="product-status" data-label="狀態">
                        <span style="color: #28a745;">有庫存</span>
                    </div>
                </div>

                <!-- 測試案例 2: 超長文字測試 -->
                <div class="product-card">
                    <div class="nhi-info" data-label="健保資訊">
                        <div class="drug-icon">💊</div>
                        <div class="nhi-details">
                            <div class="nhi-code">B987654321VERYLONGCODE</div>
                            <div class="nhi-level">健保價: 125.75</div>
                        </div>
                    </div>
                    <div class="product-name" data-label="品名/成分">
                        <div class="product-title">超級長的藥品名稱測試案例用來檢驗文字換行效果</div>
                        <div class="product-ingredients">非常詳細的成分說明包含多種化學成分名稱</div>
                    </div>
                    <div class="product-dosage" data-label="規格">
                        <div class="dosage-form">膠囊劑型 50mg 長效釋放型</div>
                    </div>
                    <div class="product-price" data-label="單價">
                        <div class="price-value">1250</div>
                        <div class="price-unit">/元</div>
                    </div>
                    <div class="product-quantity" data-label="數量">
                        <input type="number" class="quantity-input" value="1" min="1">
                        <div class="quantity-label">訂量</div>
                    </div>
                    <div class="product-actions" data-label="功能">
                        <button style="background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 3px;">+</button>
                        <button style="background: #ffc107; color: white; border: none; padding: 5px 10px; border-radius: 3px;">♥</button>
                    </div>
                    <div class="product-status" data-label="狀態">
                        <span style="color: #dc3545;">庫存不足需要補貨</span>
                    </div>
                </div>

                <!-- 測試案例 3: 極端長文字測試 -->
                <div class="product-card">
                    <div class="nhi-info" data-label="健保資訊">
                        <div class="drug-icon">💊</div>
                        <div class="nhi-details">
                            <div class="nhi-code">C456789123456789</div>
                            <div class="nhi-level">健保價: 999.99</div>
                        </div>
                    </div>
                    <div class="product-name" data-label="品名/成分">
                        <div class="product-title">這是一個極端長的藥品名稱用來測試在最極端情況下文字換行和表格寬度一致性的表現</div>
                        <div class="product-ingredients">包含acetylsalicylicacid、paracetamol、caffeine等多種長名稱化學成分的複合製劑</div>
                    </div>
                    <div class="product-dosage" data-label="規格">
                        <div class="dosage-form">特殊劑型長效緩釋膠囊 100mg/capsule</div>
                    </div>
                    <div class="product-price" data-label="單價">
                        <div class="price-value">9999</div>
                        <div class="price-unit">/元</div>
                    </div>
                    <div class="product-quantity" data-label="數量">
                        <input type="number" class="quantity-input" value="1" min="1">
                        <div class="quantity-label">訂量</div>
                    </div>
                    <div class="product-actions" data-label="功能">
                        <button style="background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 3px;">+</button>
                        <button style="background: #ffc107; color: white; border: none; padding: 5px 10px; border-radius: 3px;">♥</button>
                    </div>
                    <div class="product-status" data-label="狀態">
                        <span style="color: #6c757d;">暫時缺貨預計下週到貨</span>
                    </div>
                </div>
            </div>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #d4edda; border-radius: 5px; color: #155724;">
            <h3>✅ 修正效果檢查點：</h3>
            <ol>
                <li>表格標題和內容的欄位寬度應該完全對齊</li>
                <li>長文字應該在欄位內使用 overflow: auto 可以捲動查看</li>
                <li>所有欄位都應該保持固定寬度，不會被內容撐開</li>
                <li>文字溢出時顯示省略號（...）</li>
            </ol>
        </div>
    </div>
</body>
</html>
