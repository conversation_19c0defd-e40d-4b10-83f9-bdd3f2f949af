#!/bin/bash

echo "🧹 清理不需要的 SQL 檔案"
echo "========================="

# 創建備份目錄
BACKUP_DIR="sql_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📦 創建備份目錄: $BACKUP_DIR"

# 定義要清理的 SQL 檔案
declare -a MIGRATION_SQL_FILES=(
    "add_message_fields.sql"           # 已經在 migrations 中處理
    "setup_database.sql"               # 一次性設置，已完成
    "sync_nhi_prices.sql"              # 一次性同步，已完成
)

declare -a ADMIN_SETUP_FILES=(
    "set_admin_user.sql"               # 一次性設置管理員
    "set_admin_user_role.sql"          # 一次性設置角色
    "update_user_role.sql"             # 一次性更新角色
    "fix_permissions.sql"              # 一次性修復權限
)

declare -a CHECK_FILES=(
    "check_user_roles.sql"             # 檢查腳本，已完成檢查
    "test_price_logic.sql"             # 測試腳本，已完成測試
)

# 函數：備份並刪除檔案
backup_and_remove_sql() {
    local file="$1"
    local category="$2"
    
    if [ -f "$file" ]; then
        echo "  📄 備份並刪除: $file"
        cp "$file" "$BACKUP_DIR/"
        rm "$file"
        return 0
    else
        echo "  ⚠️  檔案不存在: $file"
        return 1
    fi
}

# 清理遷移相關 SQL 檔案
echo ""
echo "🔄 清理遷移相關 SQL 檔案..."
removed_count=0
for file in "${MIGRATION_SQL_FILES[@]}"; do
    if backup_and_remove_sql "$file" "migration"; then
        ((removed_count++))
    fi
done
echo "   清理了 $removed_count 個遷移 SQL 檔案"

# 清理管理員設置檔案
echo ""
echo "👤 清理管理員設置 SQL 檔案..."
removed_count=0
for file in "${ADMIN_SETUP_FILES[@]}"; do
    if backup_and_remove_sql "$file" "admin_setup"; then
        ((removed_count++))
    fi
done
echo "   清理了 $removed_count 個管理員設置檔案"

# 清理檢查檔案
echo ""
echo "🔍 清理檢查和測試 SQL 檔案..."
removed_count=0
for file in "${CHECK_FILES[@]}"; do
    if backup_and_remove_sql "$file" "check"; then
        ((removed_count++))
    fi
done
echo "   清理了 $removed_count 個檢查檔案"

# 清理 trash 目錄中的 SQL 檔案
echo ""
echo "🗑️  清理 trash 目錄中的 SQL 檔案..."
if [ -d "trash" ]; then
    sql_files_in_trash=$(find trash -name "*.sql" | wc -l)
    if [ "$sql_files_in_trash" -gt 0 ]; then
        echo "  📁 備份 trash 目錄中的 SQL 檔案..."
        mkdir -p "$BACKUP_DIR/trash"
        find trash -name "*.sql" -exec cp {} "$BACKUP_DIR/trash/" \;
        find trash -name "*.sql" -delete
        echo "   清理了 $sql_files_in_trash 個 trash 中的 SQL 檔案"
    else
        echo "   trash 目錄中沒有 SQL 檔案"
    fi
else
    echo "   trash 目錄不存在"
fi

echo ""
echo "✅ SQL 檔案清理完成！"
echo ""
echo "📊 清理總結："
echo "   - 所有檔案已備份到: $BACKUP_DIR"
echo "   - 保留了 migrations/ 目錄中的重要遷移檔案"
echo ""
echo "🗂️  保留的重要 SQL 檔案："
echo "   - migrations/*.sql (資料庫遷移檔案)"
echo "   - 任何在 src/ 中引用的 SQL 檔案"
echo ""
echo "⚠️  如果確認不需要備份檔案，可以執行："
echo "   rm -rf $BACKUP_DIR"

# 顯示剩餘的 SQL 檔案
echo ""
echo "📋 剩餘的 SQL 檔案："
find . -name "*.sql" -not -path "./$BACKUP_DIR/*" | sort