#!/bin/bash

echo "🧹 快速清理明顯不需要的檔案"
echo "============================="

# 創建備份目錄
BACKUP_DIR="quick_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📦 創建備份目錄: $BACKUP_DIR"

# 明顯的臨時/測試檔案
declare -a FILES_TO_REMOVE=(
    # 測試檔案
    "test_*.py"
    "simple_test.py"
    
    # 遷移相關（已完成）
    "migrate_to_neon.py"
    "test_migration_success.py"
    "complete_reset.py"
    
    # 檢查腳本（一次性使用）
    "check_*.py"
    
    # 修復腳本（已完成）
    "fix_*.py"
    
    # Excel 處理（已完成）
    "process_*.py"
    "merge_files.py"
    
    # 匯入腳本（已完成）
    "import_*.py"
    "continue_import.py"
    
    # 日誌檔案
    "*.log"
    
    # Excel 檔案（原始資料）
    "*.xlsx"
    
    # JSON 備份
    "sqlite_backup_*.json"
    "migration_data.json"
    
    # 臨時 HTML
    "debug_*.html"
    "tmp_*.html"
    "test_registration.html"
    
    # Web 測試檔案
    "web/debug_*.html"
    "web/test*.html"
    "web/fix_*.html"
    "web/direct_debug.html"
)

removed_count=0

for pattern in "${FILES_TO_REMOVE[@]}"; do
    for file in $pattern; do
        if [ -f "$file" ]; then
            echo "  🗑️  移除: $file"
            cp "$file" "$BACKUP_DIR/" 2>/dev/null
            rm "$file"
            ((removed_count++))
        fi
    done
done

echo ""
echo "✅ 快速清理完成！"
echo "   - 清理了 $removed_count 個檔案"
echo "   - 備份位置: $BACKUP_DIR"
echo ""
echo "🔍 如果要查看清理了什麼："
echo "   ls -la $BACKUP_DIR"
echo ""
echo "⚠️  如果確認不需要備份："
echo "   rm -rf $BACKUP_DIR"