# Python 檔案清理分析報告

## 📊 檔案分類分析

### 🔴 可以安全刪除的檔案

#### 1. 遷移相關檔案（已完成）
- `migrate_to_neon.py` - 資料庫遷移腳本（已完成）
- `simple_neon_migrate.py` - 簡化遷移腳本
- `fixed_migrate_to_neon.py` - 修復版遷移腳本
- `run_nhi_migration.py` - 健保資料遷移
- `execute_migration.py` - 執行遷移
- `test_migration_success.py` - 遷移測試（已完成）

#### 2. 資料庫檢查檔案（一次性使用）
- `check_database_structure.py` - 檢查資料庫結構
- `check_date_columns.py` - 檢查日期欄位
- `check_import_count.py` - 檢查匯入數量
- `check_merge.py` - 檢查合併結果
- `check_nhi_relationship.py` - 檢查健保關聯
- `test_nhi_code_structure.py` - 測試健保代碼結構

#### 3. Excel 處理檔案（已完成）
- `process_excel.py` - 處理 Excel 檔案
- `process_1_2.py` - 處理特定檔案
- `process_2.py` - 處理檔案版本2
- `process_2_fixed.py` - 修復版處理
- `merge_files.py` - 合併檔案

#### 4. 健保資料匯入檔案（已完成）
- `import_nhi_prices.py` - 匯入健保價格
- `import_nhi_prices_full.py` - 完整匯入
- `import_nhi_prices_test.py` - 測試匯入
- `import_nhi_prices (與 userdeMac-mini-3.local 衝突的複本 2025-08-11).py` - 衝突複本
- `continue_import.py` - 繼續匯入
- `import_store.py` - 匯入商店資料

#### 5. 資料庫修復檔案（已完成）
- `fix_nhi_code.py` - 修復健保代碼
- `fix_nhi_code_v2.py` - 修復版本2
- `fix_nhi_code_v3.py` - 修復版本3
- `complete_reset.py` - 完全重置

#### 6. 測試檔案（開發用）
- `test_admin_orders.py` - 測試管理員訂單
- `test_date_api.py` - 測試日期 API
- `test_date_format.py` - 測試日期格式
- `test_filter_fix.py` - 測試篩選修復
- `test_filter_improvements.py` - 測試篩選改進
- `test_fixes.py` - 測試修復
- `test_new_features.py` - 測試新功能
- `test_nhi_prices.py` - 測試健保價格
- `test_price_changes.py` - 測試價格變更
- `test_price_sync.py` - 測試價格同步
- `test_stats_fix.py` - 測試統計修復
- `simple_test.py` - 簡單測試

### 🟡 其他可清理的檔案

#### Excel 檔案（原始資料，已處理）
- `1.xlsx`, `1_2.xlsx`, `1_3.xlsx`
- `2.xlsx`, `2_3.xlsx`, `2_processed.xlsx`
- `4.xlsx`
- `nhi.xlsx`, `nhi_filtered.xlsx`, `nhi_filtered_correct.xlsx`, `nhi_final_filtered.xlsx`
- `sotre.xlsx`

#### 日誌檔案（可定期清理）
- `app.log`, `pharmacy.log`, `server.log`, `system.log`, `migration.log`

#### 備份檔案
- `sqlite_backup_20250806_093337.json`
- `sqlite_backup_20250806_093717.json`
- `migration_data.json`

#### 臨時 HTML 檔案
- `debug_admin_orders.html`, `debug_admin.html`
- `test_registration.html`
- `tmp_rovodev_test_f5_fix.html`, `tmp_rovodev_test_permissions.html`
- `web/debug_*.html`, `web/test*.html`, `web/fix_*.html`

### 🟢 應該保留的檔案

#### 核心專案檔案
- `Cargo.toml`, `Cargo.lock` - Rust 專案配置
- `src/` - 原始碼目錄
- `web/` - 前端檔案（除了測試檔案）
- `migrations/` - 資料庫遷移檔案

#### 配置檔案
- `.env*` - 環境變數配置
- `docker-compose.yml`, `Dockerfile` - 容器化配置

#### 文件
- `README.md` - 專案說明
- `DEPLOYMENT.md` - 部署指南
- `doc/` - 文件目錄

#### 腳本
- `deploy.sh` - 部署腳本
- `start*.sh`, `dev.sh` - 啟動腳本

## 🚀 建議的清理步驟

### 步驟 1：快速清理（推薦）
```bash
./quick_cleanup.sh
```
這會清理最明顯不需要的檔案，並創建備份。

### 步驟 2：完整清理（可選）
```bash
./cleanup_python_files.sh
```
這會進行更徹底的清理。

### 步驟 3：驗證系統正常
```bash
cargo run --release
```
確保清理後系統仍能正常運行。

### 步驟 4：刪除備份（確認無誤後）
```bash
# 查看備份內容
ls -la quick_backup_*

# 確認無誤後刪除
rm -rf quick_backup_*
```

## 📈 預期效果

清理後專案結構會更清晰：
- 減少約 30-40 個不必要的檔案
- 專案目錄更整潔
- 減少混淆和維護負擔
- 保留所有重要的功能和配置

## ⚠️ 注意事項

1. **備份重要**：所有清理腳本都會先創建備份
2. **測試驗證**：清理後請測試系統功能
3. **漸進式清理**：建議先用 `quick_cleanup.sh`
4. **保留選項**：如果不確定，可以先保留檔案