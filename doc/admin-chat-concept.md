# 管理員聊天管理介面設計概念

## 功能概述
管理員可以查看所有用戶的聊天訊息，並進行回覆。

## 介面設計

### 1. 聊天列表頁面
```
┌─────────────────────────────────────┐
│ 💬 客服聊天管理                      │
├─────────────────────────────────────┤
│ 🔴 未讀 (3)  📋 全部  ✅ 已回覆      │
├─────────────────────────────────────┤
│ 👤 藥局A                            │
│    最新訊息: 請問這個產品有庫存嗎？    │
│    時間: 2025-01-13 15:30  🔴 未讀   │
├─────────────────────────────────────┤
│ 👤 藥局B                            │
│    最新訊息: 謝謝您的回覆             │
│    時間: 2025-01-13 14:20  ✅ 已讀   │
└─────────────────────────────────────┘
```

### 2. 聊天對話頁面
```
┌─────────────────────────────────────┐
│ ← 返回  👤 藥局A  📞 聯絡資訊         │
├─────────────────────────────────────┤
│                                     │
│ 👤 請問這個產品有庫存嗎？             │
│    15:30                           │
│                                     │
│ 👤 我需要訂購50盒                    │
│    15:32                           │
│                                     │
│                   目前有庫存100盒 👨‍💼 │
│                              15:35 │
│                                     │
│                   可以為您保留50盒 👨‍💼 │
│                              15:35 │
│                                     │
├─────────────────────────────────────┤
│ [輸入回覆訊息...]           [發送] │
└─────────────────────────────────────┘
```

## 資料結構

### 聊天訊息
```javascript
{
  id: 1234567890,
  user_id: "pharmacy_a",
  user_name: "藥局A",
  message: "請問這個產品有庫存嗎？",
  type: "user", // 'user' 或 'admin'
  created_at: "2025-01-13T15:30:00Z",
  status: "sent", // 'sent', 'read', 'replied'
  admin_reply: null // 管理員回覆的訊息ID
}
```

### 聊天會話
```javascript
{
  user_id: "pharmacy_a",
  user_name: "藥局A",
  last_message: "請問這個產品有庫存嗎？",
  last_message_time: "2025-01-13T15:30:00Z",
  unread_count: 2,
  status: "unread" // 'unread', 'replied', 'closed'
}
```

## 管理員功能

1. **查看聊天列表** - 顯示所有用戶的聊天會話
2. **篩選功能** - 未讀、全部、已回覆
3. **即時通知** - 新訊息提醒
4. **快速回覆** - 常用回覆範本
5. **用戶資訊** - 查看用戶基本資料
6. **訊息搜尋** - 搜尋歷史訊息
7. **標記狀態** - 標記為已讀、已回覆等

## 實作建議

### 前端
- 在系統管理頁面新增「客服聊天」標籤
- 使用 WebSocket 或定期輪詢獲取新訊息
- 響應式設計，支援手機和電腦

### 後端
- 建立聊天訊息資料表
- 提供 API 接口：
  - GET /api/admin/chats - 獲取聊天列表
  - GET /api/admin/chats/{user_id} - 獲取特定用戶聊天記錄
  - POST /api/admin/chats/{user_id}/reply - 回覆用戶訊息
  - PUT /api/admin/chats/{user_id}/status - 更新聊天狀態

### 通知系統
- 新訊息即時通知管理員
- 用戶收到回覆時的通知
- 郵件通知（可選）