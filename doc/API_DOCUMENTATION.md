# 藥局採購系統 API 文檔

## 概述

藥局採購系統提供完整的 RESTful API，支援藥品管理、訂單處理、使用者認證、通知和備份功能。

## 基礎資訊

- **Base URL**: `http://localhost:8080`
- **認證方式**: JWT Bearer <PERSON>
- **內容類型**: `application/json`

## 認證

### 註冊使用者
```http
POST /api/auth/register
```

**請求體**:
```json
{
  "username": "pharmacy_user",
  "email": "<EMAIL>",
  "password": "secure_password",
  "pharmacy_name": "仁心藥局",
  "phone": "0912345678",
  "line_user_id": "U1234567890abcdef"
}
```

**回應**:
```json
{
  "message": "User registered successfully",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 使用者登入
```http
POST /api/auth/login
```

**請求體**:
```json
{
  "username": "pharmacy_user",
  "password": "secure_password"
}
```

**回應**:
```json
{
  "message": "Login successful",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "username": "pharmacy_user",
    "email": "<EMAIL>",
    "pharmacy_name": "仁心藥局"
  }
}
```

## 產品管理

### 取得產品清單
```http
GET /api/products?page=1&limit=10&search=aspirin
```

**查詢參數**:
- `page`: 頁碼 (預設: 1)
- `limit`: 每頁數量 (預設: 10)
- `search`: 搜尋關鍵字
- `manufacturer`: 製造商篩選
- `min_price`: 最低價格
- `max_price`: 最高價格

**回應**:
```json
{
  "products": [
    {
      "id": 1,
      "nhi_code": "A001234567",
      "name": "阿斯匹林 100mg",
      "manufacturer": "台鹽生技",
      "unit": "盒",
      "unit_price": 150.00,
      "stock_quantity": 50,
      "description": "解熱鎮痛藥",
      "is_active": true,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 100,
  "page": 1,
  "limit": 10
}
```

### 建立產品
```http
POST /api/products
Authorization: Bearer <token>
```

**請求體**:
```json
{
  "nhi_code": "A001234567",
  "name": "阿斯匹林 100mg",
  "manufacturer": "台鹽生技",
  "unit": "盒",
  "unit_price": 150.00,
  "stock_quantity": 50,
  "description": "解熱鎮痛藥"
}
```

### 更新產品
```http
PUT /api/products/{id}
Authorization: Bearer <token>
```

### 刪除產品
```http
DELETE /api/products/{id}
Authorization: Bearer <token>
```

### 更新庫存
```http
PUT /api/products/{id}/stock
Authorization: Bearer <token>
```

**請求體**:
```json
{
  "quantity": 10
}
```

### 匯入產品 (Excel)
```http
POST /api/products/import/excel
Authorization: Bearer <token>
Content-Type: multipart/form-data
```

**表單資料**:
- `file`: Excel 檔案

### 匯入產品 (CSV)
```http
POST /api/products/import/csv
Authorization: Bearer <token>
Content-Type: multipart/form-data
```

**表單資料**:
- `file`: CSV 檔案

## 訂單管理

### 建立訂單
```http
POST /api/orders
Authorization: Bearer <token>
```

**請求體**:
```json
{
  "items": [
    {
      "product_id": 1,
      "quantity": 5
    },
    {
      "product_id": 2,
      "quantity": 3
    }
  ],
  "notes": "急件，請盡快處理"
}
```

**回應**:
```json
{
  "message": "Order created successfully",
  "order": {
    "id": 1,
    "order_number": "ORD2024010112000001",
    "user_id": 1,
    "status": "pending",
    "total_amount": 750.00,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

### 取得訂單清單
```http
GET /api/orders?status=pending&start_date=2024-01-01&end_date=2024-01-31
Authorization: Bearer <token>
```

**查詢參數**:
- `status`: 訂單狀態 (pending, confirmed, processing, shipped, delivered, cancelled)
- `start_date`: 開始日期
- `end_date`: 結束日期
- `limit`: 每頁數量
- `offset`: 偏移量

### 取得訂單詳情
```http
GET /api/orders/{id}
Authorization: Bearer <token>
```

**回應**:
```json
{
  "order": {
    "order": {
      "id": 1,
      "order_number": "ORD2024010112000001",
      "user_id": 1,
      "status": "pending",
      "total_amount": 750.00,
      "created_at": "2024-01-01T12:00:00Z",
      "updated_at": "2024-01-01T12:00:00Z"
    },
    "items": [
      {
        "item": {
          "id": 1,
          "order_id": 1,
          "product_id": 1,
          "quantity": 5,
          "unit_price": 150.00,
          "subtotal": 750.00
        },
        "product_name": "阿斯匹林 100mg",
        "product_nhi_code": "A001234567"
      }
    ]
  }
}
```

### 更新訂單狀態
```http
PUT /api/orders/{id}/status
Authorization: Bearer <token>
```

**請求體**:
```json
{
  "status": "confirmed"
}
```

## 備份管理

### 建立備份
```http
POST /api/backup/create
Authorization: Bearer <token>
```

**回應**:
```json
{
  "message": "Backup created successfully",
  "backup": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "file_path": "./backups/pharmacy_backup_20240101_120000_550e8400.db",
    "file_size": 1048576,
    "created_at": "2024-01-01T12:00:00Z",
    "cloud_url": "https://storage.googleapis.com/bucket/pharmacy_backup_20240101_120000_550e8400.db"
  }
}
```

### 取得備份清單
```http
GET /api/backup/list
Authorization: Bearer <token>
```

### 清理舊備份
```http
POST /api/backup/cleanup
Authorization: Bearer <token>
```

### 排程備份
```http
POST /api/backup/schedule
Authorization: Bearer <token>
```

## 通知管理

### 發送 Email 通知
```http
POST /api/notifications/email
Authorization: Bearer <token>
```

**請求體**:
```json
{
  "to": "<EMAIL>",
  "subject": "訂單確認通知",
  "body": "您的訂單已成功建立..."
}
```

### 發送 Line 通知
```http
POST /api/notifications/line
Authorization: Bearer <token>
```

**請求體**:
```json
{
  "to": "U1234567890abcdef",
  "message": "您的訂單已成功建立..."
}
```

### 測試通知
```http
POST /api/notifications/test
Authorization: Bearer <token>
```

## 錯誤處理

所有 API 端點都使用統一的錯誤回應格式：

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "reason": "Invalid email format"
    }
  }
}
```

### 錯誤代碼

- `VALIDATION_ERROR`: 驗證錯誤
- `AUTHENTICATION_ERROR`: 認證錯誤
- `AUTHORIZATION_ERROR`: 授權錯誤
- `NOT_FOUND`: 資源不存在
- `DATABASE_ERROR`: 資料庫錯誤
- `FILE_PROCESSING_ERROR`: 檔案處理錯誤
- `NOTIFICATION_ERROR`: 通知錯誤
- `CLOUD_STORAGE_ERROR`: 雲端儲存錯誤
- `CONFIGURATION_ERROR`: 配置錯誤

## 狀態碼

- `200 OK`: 請求成功
- `201 Created`: 資源建立成功
- `400 Bad Request`: 請求格式錯誤
- `401 Unauthorized`: 未認證
- `403 Forbidden`: 無權限
- `404 Not Found`: 資源不存在
- `422 Unprocessable Entity`: 驗證失敗
- `500 Internal Server Error`: 伺服器錯誤

## 速率限制

- 認證端點: 每分鐘 5 次
- 其他端點: 每分鐘 100 次

## 版本控制

目前 API 版本為 v1，未來版本將透過 URL 路徑區分：
- 當前: `/api/...`
- 未來: `/api/v2/...`

## 範例程式碼

### JavaScript (Fetch API)
```javascript
// 登入
const loginResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    username: 'pharmacy_user',
    password: 'secure_password'
  })
});

const { token } = await loginResponse.json();

// 建立訂單
const orderResponse = await fetch('/api/orders', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    items: [
      { product_id: 1, quantity: 5 }
    ],
    notes: '急件'
  })
});
```

### Python (requests)
```python
import requests

# 登入
login_response = requests.post('http://localhost:8080/api/auth/login', json={
    'username': 'pharmacy_user',
    'password': 'secure_password'
})

token = login_response.json()['token']

# 建立訂單
order_response = requests.post(
    'http://localhost:8080/api/orders',
    headers={'Authorization': f'Bearer {token}'},
    json={
        'items': [{'product_id': 1, 'quantity': 5}],
        'notes': '急件'
    }
)
```

### cURL
```bash
# 登入
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "pharmacy_user", "password": "secure_password"}'

# 建立訂單
curl -X POST http://localhost:8080/api/orders \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"items": [{"product_id": 1, "quantity": 5}], "notes": "急件"}'
``` 