# 管理員帳號排除安全措施

## 🔒 安全要求

**重要：管理員帳號絕對不能出現在用戶審核名單中**

## 🛡️ 實施的安全措施

### 1. 後端 API 層面保護

#### 調試 API (`src/api/debug_users.rs`)
```sql
SELECT 
    id, username, email, pharmacy_name, 
    status, created_at, submitted_at
FROM users 
WHERE username NOT IN ('admin', 'super_admin', 'root', 'administrator', 'system')
ORDER BY created_at DESC
LIMIT 50
```

#### 用戶審核 API (`src/api/user_approval.rs`)
```sql
SELECT 
    id, username, email, pharmacy_name, contact_person, 
    phone, mobile, institution_code, address, 
    COALESCE(submitted_at, created_at) as submitted_at,
    COALESCE(status, 'pending') as status
FROM users 
WHERE COALESCE(status, 'pending') = 'pending'
AND username NOT IN ('admin', 'super_admin', 'root', 'administrator', 'system')
ORDER BY COALESCE(submitted_at, created_at) DESC
```

### 2. 前端 JavaScript 層面保護

#### 雙重過濾機制 (`web/js/app.js`)
```javascript
// 管理員帳號列表（不應該出現在審核名單中）
const adminUsernames = ['admin', 'super_admin', 'root', 'administrator', 'system'];

// 只顯示真正的待審核用戶，並排除管理員帳號
const pendingUsers = response.data.filter(user => 
  user.status === 'pending' && 
  !adminUsernames.includes(user.username.toLowerCase())
);
```

## 🔍 排除的管理員帳號類型

系統會自動排除以下用戶名：
- `admin`
- `super_admin` 
- `root`
- `administrator`
- `system`

## ✅ 驗證測試

### 測試命令
```bash
# 檢查待審核用戶（應該不包含管理員）
curl -s http://localhost:8080/api/debug/users | jq '.data | map(select(.status == "pending")) | .[] | {id, username, status}'

# 驗證管理員帳號被排除（應該返回空結果）
curl -s http://localhost:8080/api/debug/users | jq '.data | .[] | select(.username == "admin")'
```

### 預期結果
- ✅ 只顯示真正的待審核用戶（pending_user1, pending_user2, pending_user3）
- ✅ 管理員帳號（admin）完全不出現在任何列表中
- ✅ 前端界面只顯示非管理員的待審核用戶

## 🚨 安全注意事項

1. **多層防護**：同時在後端 SQL 查詢和前端 JavaScript 中實施過濾
2. **用戶名檢查**：基於用戶名進行排除，涵蓋常見的管理員帳號名稱
3. **大小寫不敏感**：前端使用 `toLowerCase()` 確保大小寫變體也被排除
4. **未來擴展**：如果需要基於角色的檢查，可以在有 `user_roles` 表後添加

## 📋 維護清單

- [ ] 定期檢查是否有新的管理員帳號類型需要添加到排除列表
- [ ] 在添加新的用戶查詢 API 時，確保包含管理員排除邏輯
- [ ] 測試各種邊界情況（大小寫、特殊字符等）
- [ ] 考慮實施基於角色的更精確的權限檢查

## 🎯 測試結果

✅ **2025-08-12 測試通過**
- 管理員帳號成功被排除
- 只有 3 個真正的待審核用戶顯示
- 前端和後端雙重保護正常工作

---

**重要提醒：這是一個關鍵的安全措施，任何修改用戶查詢邏輯的代碼都必須保持這個排除機制！**