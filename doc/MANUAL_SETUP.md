# 手動設置 favorites 表

由於編譯問題，我們需要先手動創建 favorites 表，然後再編譯代碼。

## 方法 1: 使用資料庫管理工具

如果你有 pgAdmin、DBeaver 或其他資料庫管理工具，請執行以下 SQL：

```sql
-- 創建 favorites 表
CREATE TABLE IF NOT EXISTS favorites (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, product_id)
);

-- 創建索引
CREATE INDEX IF NOT EXISTS idx_favorites_user_id ON favorites(user_id);
CREATE INDEX IF NOT EXISTS idx_favorites_product_id ON favorites(product_id);
CREATE INDEX IF NOT EXISTS idx_favorites_created_at ON favorites(created_at);

-- 添加註釋
COMMENT ON TABLE favorites IS '用戶最愛產品表';
COMMENT ON COLUMN favorites.id IS '主鍵ID';
COMMENT ON COLUMN favorites.user_id IS '用戶ID，關聯users表';
COMMENT ON COLUMN favorites.product_id IS '產品ID，關聯products表';
COMMENT ON COLUMN favorites.created_at IS '收藏時間';
```

## 方法 2: 使用線上資料庫控制台

如果你使用的是 Neon、Supabase 或其他雲端資料庫：

1. 登入你的資料庫控制台
2. 找到 SQL 編輯器或查詢工具
3. 複製貼上上面的 SQL 代碼
4. 執行

## 方法 3: 使用 psql 命令行（如果已安裝）

```bash
# 如果你有 psql 命令行工具
psql $DATABASE_URL -f create_table_only.sql
```

## 驗證表創建成功

執行以下查詢來驗證：

```sql
-- 檢查表是否存在
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' AND table_name = 'favorites';

-- 檢查表結構
SELECT column_name, data_type FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'favorites'
ORDER BY ordinal_position;
```

## 創建表後

表創建成功後，你就可以：

1. 重新編譯項目：`cargo build`
2. 啟動服務器
3. 使用我的最愛功能

## 如果遇到問題

- 確保你有創建表的權限
- 檢查資料庫連線是否正常
- 確認 DATABASE_URL 設置正確

創建表後，sqlx 的編譯時檢查就會通過，代碼就能正常編譯了。