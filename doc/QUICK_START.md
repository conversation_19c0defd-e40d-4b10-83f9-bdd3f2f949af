# 快速開始 - 我的最愛功能

## 🚀 一鍵執行遷移

### 選項 1: 自動化腳本（推薦）

```bash
# Linux/macOS
./run_migration.sh

# Windows
run_migration.bat
```

### 選項 2: 手動執行 Rust 工具

```bash
cargo run --bin create_favorites_table
```

### 選項 3: 直接執行 SQL

```bash
psql $DATABASE_URL -f create_favorites_table.sql
```

## ✅ 驗證安裝

```bash
# 測試遷移結果
./test_favorites_migration.sh

# 或手動檢查
psql $DATABASE_URL -c "\d favorites"
```

## 🎯 完成後的功能

1. **收藏產品** - 點擊 ❤️ 按鈕
2. **查看最愛** - 導航到「❤️ 我的最愛」頁面
3. **管理最愛** - 移除或加入購物車
4. **數據持久化** - 資料保存在資料庫中

## 🔧 故障排除

- **連線問題**: 檢查 `.env` 中的 `DATABASE_URL`
- **權限問題**: 確保資料庫用戶有 CREATE 權限
- **表已存在**: 工具會自動跳過，無需擔心

## 📞 需要幫助？

查看完整的 [MIGRATION_GUIDE.md](MIGRATION_GUIDE.md) 獲取詳細說明。