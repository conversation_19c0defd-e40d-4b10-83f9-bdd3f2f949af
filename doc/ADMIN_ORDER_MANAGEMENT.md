# 管理員訂單管理功能

## 🎯 功能概述

為管理員用戶提供了增強的訂單管理界面，與普通用戶的訂單歷史頁面有明顯區別。

## ✨ 新增功能

### 1. **管理員專用界面**
- 頁面標題顯示為「訂單管理（管理員）」
- 普通用戶仍顯示「訂單歷史」
- 管理員可以看到所有用戶的訂單

### 2. **訂單篩選功能**
管理員可以根據以下條件篩選訂單：
- **訂單狀態**：待處理、已確認、處理中、已出貨、已送達、已取消
- **日期範圍**：開始日期和結束日期
- **快速操作**：篩選和清除篩選按鈕

### 3. **訂單統計信息**
實時顯示以下統計數據：
- 總訂單數量
- 各狀態訂單數量（待處理、已確認、處理中、已送達）
- 訂單總金額

### 4. **訂單狀態管理**
管理員可以直接在訂單列表中：
- 點擊「確認」按鈕將訂單狀態改為已確認
- 點擊「處理中」按鈕將訂單狀態改為處理中
- 點擊「已送達」按鈕將訂單狀態改為已送達
- 點擊「取消」按鈕將訂單狀態改為已取消

### 5. **增強的訂單顯示**
- 管理員訂單項目有特殊的藍色左邊框
- 顯示客戶信息（藥局名稱和用戶名）
- 狀態顯示為彩色徽章，更易識別
- 每個訂單都有快速操作按鈕

## 🔧 技術實現

### 前端改進
1. **HTML結構**：
   - 添加了管理員控制面板
   - 篩選器和統計信息區域
   - 響應式設計支持

2. **CSS樣式**：
   - 管理員專用樣式類
   - 狀態徽章顏色系統
   - 響應式布局

3. **JavaScript功能**：
   - 動態界面切換（管理員 vs 普通用戶）
   - 篩選功能實現
   - 統計信息計算
   - 訂單狀態更新

### 後端改進
1. **API端點**：
   - 保持現有的 `/api/orders/all` 端點（管理員專用）
   - 增強的 `/api/orders/:id/status` 端點支持管理員權限

2. **權限控制**：
   - 管理員可以更新任何訂單的狀態
   - 普通用戶只能更新自己的訂單
   - 基於 `can_view_all_orders()` 權限檢查

3. **服務層**：
   - 新增 `update_order_status_admin()` 方法
   - 管理員狀態更新有更寬鬆的業務規則

## 📱 用戶體驗

### 管理員視圖
```
訂單管理（管理員）
┌─────────────────────────────────────────────────────────┐
│ 篩選器: [狀態▼] [開始日期] [結束日期] [篩選] [清除]      │
│ 統計: 總訂單:50 待處理:5 已確認:10 處理中:15 已送達:20  │
└─────────────────────────────────────────────────────────┘

📋 訂單 #ORD-001 [已確認] NT$ 1,200
👤 客戶：測試藥局 (testuser)
📅 下單時間：2025-08-07 10:30:00
[確認] [處理中] [已送達] [取消]
```

### 普通用戶視圖
```
訂單歷史

📋 訂單 #ORD-001 [已確認] NT$ 1,200
📅 下單時間：2025-08-07 10:30:00
```

## 🎨 視覺設計

### 狀態徽章顏色
- **待處理**：黃色背景 (#fff3cd)
- **已確認**：綠色背景 (#d4edda)
- **處理中**：藍色背景 (#cce5ff)
- **已出貨**：灰色背景 (#e2e3e5)
- **已送達**：青色背景 (#d1ecf1)
- **已取消**：紅色背景 (#f8d7da)

### 管理員訂單項目
- 左邊框：藍色 (#007bff)
- 背景：淺灰色 (#f8f9fa)
- 客戶信息：藍色背景框 (#e3f2fd)

## 🔒 權限和安全

### 權限檢查
- 前端：基於 `currentUser.permissions.role_name`
- 後端：基於 `AuthContext.can_view_all_orders()`

### 安全措施
- 管理員狀態更新仍有基本業務邏輯檢查
- 防止不合理的狀態轉換（如已送達改回待處理）
- 所有API調用都需要有效的JWT令牌

## 📊 API端點

### 現有端點
- `GET /api/orders` - 用戶自己的訂單
- `GET /api/orders/all` - 所有訂單（管理員專用）
- `PUT /api/orders/:id/status` - 更新訂單狀態（增強支持管理員）

### 查詢參數支持
```
GET /api/orders/all?status=pending&start_date=2025-08-01&end_date=2025-08-07
```

## 🚀 使用方法

### 管理員操作流程
1. 登錄管理員帳號
2. 點擊「訂單管理」標籤
3. 查看訂單統計和列表
4. 使用篩選器查找特定訂單
5. 點擊狀態按鈕更新訂單狀態
6. 系統自動刷新列表和統計

### 響應式支持
- 桌面：完整功能和橫向布局
- 平板：調整布局，保持功能完整
- 手機：垂直堆疊，簡化顯示

## 🔄 未來擴展

可以考慮添加的功能：
- 批量操作（批量更新狀態）
- 訂單搜索（按訂單號、客戶名稱）
- 導出功能（Excel、PDF）
- 訂單詳細編輯
- 訂單備註管理
- 客戶聯繫功能

## 📝 注意事項

1. **權限依賴**：功能依賴現有的角色權限系統
2. **數據一致性**：狀態更新會立即反映在統計中
3. **性能考慮**：大量訂單時可能需要分頁優化
4. **瀏覽器兼容**：使用現代CSS特性，建議使用較新瀏覽器

這個實現為管理員提供了強大而直觀的訂單管理工具，同時保持了與普通用戶界面的清晰區分。