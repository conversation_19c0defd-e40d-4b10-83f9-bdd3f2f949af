# 資料庫遷移指南

## 概述
本指南將幫助您為我的最愛功能創建必要的資料庫表格。

## 前置條件

1. **確保已安裝 Rust 和 Cargo**
2. **確保資料庫連線正常**
3. **確保 .env 檔案已正確設置**

## 執行遷移

### 方法一：使用自動化腳本

#### Linux/macOS:
```bash
./run_migration.sh
```

#### Windows:
```cmd
run_migration.bat
```

### 方法二：手動執行

1. **編譯遷移工具**
```bash
cargo build --bin create_favorites_table
```

2. **執行遷移**
```bash
cargo run --bin create_favorites_table
```

### 方法三：直接使用 SQL

如果您偏好直接執行 SQL，可以使用以下命令：

```sql
-- 創建 favorites 表
CREATE TABLE IF NOT EXISTS favorites (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 確保同一用戶不能重複收藏同一產品
    UNIQUE(user_id, product_id)
);

-- 創建索引
CREATE INDEX IF NOT EXISTS idx_favorites_user_id ON favorites(user_id);
CREATE INDEX IF NOT EXISTS idx_favorites_product_id ON favorites(product_id);
CREATE INDEX IF NOT EXISTS idx_favorites_created_at ON favorites(created_at);

-- 添加註釋
COMMENT ON TABLE favorites IS '用戶最愛產品表';
COMMENT ON COLUMN favorites.id IS '主鍵ID';
COMMENT ON COLUMN favorites.user_id IS '用戶ID，關聯users表';
COMMENT ON COLUMN favorites.product_id IS '產品ID，關聯products表';
COMMENT ON COLUMN favorites.created_at IS '收藏時間';

-- 添加外鍵約束（如果 users 和 products 表存在）
ALTER TABLE favorites 
ADD CONSTRAINT fk_favorites_user_id 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE favorites 
ADD CONSTRAINT fk_favorites_product_id 
FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE;
```

## 驗證遷移結果

遷移完成後，您可以使用以下 SQL 查詢來驗證：

```sql
-- 檢查表是否存在
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' AND table_name = 'favorites';

-- 檢查表結構
\d favorites

-- 檢查索引
SELECT indexname FROM pg_indexes 
WHERE tablename = 'favorites' AND schemaname = 'public';

-- 檢查約束
SELECT constraint_name, constraint_type FROM information_schema.table_constraints 
WHERE table_schema = 'public' AND table_name = 'favorites';
```

## 故障排除

### 常見問題

1. **連線失敗**
   - 檢查 DATABASE_URL 是否正確
   - 確保資料庫服務正在運行
   - 檢查網路連線

2. **權限不足**
   - 確保資料庫用戶有創建表格的權限
   - 檢查是否有 CREATE、ALTER 權限

3. **表已存在**
   - 遷移工具會自動檢查並跳過已存在的表
   - 如需重新創建，請先手動刪除表

4. **外鍵約束失敗**
   - 確保 users 和 products 表存在
   - 檢查參照的欄位類型是否匹配

### 手動清理（如果需要）

```sql
-- 刪除 favorites 表（謹慎使用！）
DROP TABLE IF EXISTS favorites CASCADE;

-- 刪除相關索引（如果表已刪除，索引會自動刪除）
DROP INDEX IF EXISTS idx_favorites_user_id;
DROP INDEX IF EXISTS idx_favorites_product_id;
DROP INDEX IF EXISTS idx_favorites_created_at;
```

## 遷移後的功能

遷移完成後，您將可以使用以下功能：

1. **收藏產品** - 點擊產品的愛心按鈕
2. **查看我的最愛** - 在導航列點擊「❤️ 我的最愛」
3. **管理最愛** - 在最愛頁面中移除或加入購物車
4. **清空最愛** - 一鍵清空所有收藏

## API 端點

遷移完成後，以下 API 端點將可用：

- `POST /api/favorites/toggle` - 切換收藏狀態
- `GET /api/favorites/` - 獲取用戶最愛列表
- `GET /api/favorites/check/:product_id` - 檢查單個產品收藏狀態
- `DELETE /api/favorites/clear` - 清空所有最愛
- `GET /api/favorites/stats` - 獲取收藏統計
- `GET /api/favorites/popular` - 獲取熱門產品

## 支援

如果遇到問題，請檢查：

1. 伺服器日誌
2. 資料庫連線狀態
3. 環境變數設置
4. 權限配置

或聯繫技術支援團隊。