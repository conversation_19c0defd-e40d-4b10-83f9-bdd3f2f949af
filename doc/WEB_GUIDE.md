# 藥品採購系統 Web 介面使用指南

## 快速開始

1. **啟動系統**
   ```bash
   ./dev.sh
   # 或
   cargo run --bin pharmacy-system
   ```

2. **開啟瀏覽器**
   - 訪問: http://localhost:8080/
   - 建議使用 Chrome、Firefox 或 Safari

## 功能介紹

### 🔐 使用者認證

#### 註冊新帳號
1. 點擊「註冊新帳號」連結
2. 填寫必要資訊：
   - 使用者名稱
   - 電子郵件
   - 密碼
   - 藥局名稱
   - 電話號碼（可選）
3. 點擊「註冊」按鈕

#### 登入系統
1. 輸入使用者名稱和密碼
2. 點擊「登入」按鈕
3. 成功後會自動跳轉到主介面

### 📦 產品管理

#### 瀏覽產品
- 登入後預設顯示產品清單
- 每個產品卡片顯示：
  - 產品名稱
  - 健保代碼
  - 製造商
  - 庫存數量
  - 單價

#### 搜尋產品
1. 在搜尋框輸入關鍵字
2. 點擊「搜尋」按鈕或按 Enter
3. 系統會顯示符合條件的產品

#### 加入購物車
1. 在產品卡片中調整數量
2. 點擊「加入購物車」按鈕
3. 系統會顯示成功訊息

### 🛒 購物車管理

#### 查看購物車
1. 點擊「購物車」標籤
2. 查看已加入的商品和總金額

#### 調整商品數量
- 使用「+」「-」按鈕調整數量
- 點擊「移除」刪除商品

#### 結帳
1. 確認購物車內容
2. 點擊「結帳」按鈕
3. 系統會建立訂單並清空購物車

#### 清空購物車
- 點擊「清空購物車」按鈕
- 確認後會移除所有商品

### 📋 訂單管理

#### 查看訂單歷史
1. 點擊「訂單管理」標籤
2. 查看所有歷史訂單
3. 每個訂單顯示：
   - 訂單編號
   - 建立時間
   - 訂單狀態
   - 總金額

### 👤 個人資料

#### 更新個人資料
1. 點擊「個人資料」標籤
2. 修改以下資訊：
   - 藥局名稱
   - 電話號碼
   - 電子郵件通知設定
3. 點擊「更新資料」按鈕

## 系統狀態

### 訊息提示
- 🟢 綠色訊息：操作成功
- 🔴 紅色訊息：操作失敗或錯誤

### 載入指示器
- 系統處理請求時會顯示載入動畫
- 請耐心等待操作完成

## 故障排除

### 常見問題

1. **無法載入頁面**
   - 確認伺服器是否正在運行
   - 檢查 http://localhost:8080/health

2. **登入失敗**
   - 檢查使用者名稱和密碼是否正確
   - 確認帳號是否已註冊

3. **產品載入失敗**
   - 檢查網路連線
   - 確認 API 服務正常運行

4. **購物車操作失敗**
   - 檢查產品庫存是否足夠
   - 確認登入狀態是否有效

### 開發者工具
- 按 F12 開啟瀏覽器開發者工具
- 查看 Console 標籤的錯誤訊息
- 檢查 Network 標籤的 API 請求狀態

## 技術規格

### 瀏覽器支援
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### 響應式設計
- 支援桌面和行動裝置
- 自動適應不同螢幕尺寸

### 安全性
- JWT Token 認證
- 自動 Token 過期處理
- HTTPS 支援（生產環境）

## API 整合

Web 介面使用以下 API 端點：

- `POST /api/auth/register` - 使用者註冊
- `POST /api/auth/login` - 使用者登入
- `GET /api/auth/me` - 取得使用者資訊
- `GET /api/products` - 取得產品清單
- `GET /api/cart` - 取得購物車內容
- `POST /api/cart` - 加入購物車
- `POST /api/orders/cart` - 從購物車建立訂單
- `GET /api/orders` - 取得訂單清單

## 支援

如有問題或建議，請：
1. 檢查系統日誌
2. 查看 API 文檔
3. 執行測試腳本確認系統狀態