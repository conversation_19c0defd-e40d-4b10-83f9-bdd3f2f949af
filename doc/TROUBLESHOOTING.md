# 故障排除指南

## 問題：ERROR: relation "favorites" does not exist

這個錯誤表示 favorites 表還沒有成功創建。讓我們逐步解決：

### 步驟 1: 檢查資料庫連線

首先確認你能連接到資料庫：

```sql
SELECT current_user, current_database();
```

### 步驟 2: 檢查現有表

查看資料庫中有哪些表：

```sql
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';
```

### 步驟 3: 檢查權限

確認你有創建表的權限：

```sql
SELECT has_table_privilege(current_user, 'public', 'CREATE');
```

### 步驟 4: 使用不同方法創建表

#### 方法 A: 使用安全腳本

執行 `create_favorites_safe.sql`：

```bash
# 如果你有 psql
psql $DATABASE_URL -f create_favorites_safe.sql

# 或者複製內容到資料庫管理工具中執行
```

#### 方法 B: 使用 Node.js（如果已安裝）

```bash
# 安裝依賴
npm install pg dotenv

# 執行腳本
node create_table.js
```

#### 方法 C: 使用 Python（如果已安裝）

```bash
# 安裝依賴
pip install psycopg2-binary python-dotenv

# 執行腳本
python create_table.py
```

#### 方法 D: 手動在資料庫控制台執行

如果你使用 Neon、Supabase 等雲端資料庫：

1. 登入控制台
2. 找到 SQL 編輯器
3. 執行以下 SQL：

```sql
-- 創建表
CREATE TABLE favorites (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, product_id)
);

-- 創建索引
CREATE INDEX idx_favorites_user_id ON favorites(user_id);
CREATE INDEX idx_favorites_product_id ON favorites(product_id);
CREATE INDEX idx_favorites_created_at ON favorites(created_at);
```

### 步驟 5: 驗證創建成功

執行以下查詢確認表已創建：

```sql
-- 檢查表是否存在
SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'favorites'
);

-- 檢查表結構
\d favorites
-- 或者
SELECT column_name, data_type FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'favorites';
```

### 常見問題解決

#### 問題 1: 權限不足
```
ERROR: permission denied for schema public
```

**解決方案：**
- 聯繫資料庫管理員
- 或使用有足夠權限的用戶

#### 問題 2: 連線失敗
```
FATAL: password authentication failed
```

**解決方案：**
- 檢查 DATABASE_URL 是否正確
- 確認用戶名和密碼

#### 問題 3: 資料庫不存在
```
FATAL: database "xxx" does not exist
```

**解決方案：**
- 檢查資料庫名稱是否正確
- 確認資料庫已創建

### 成功標誌

當你看到以下輸出時，表示成功：

```
CREATE TABLE
CREATE INDEX
CREATE INDEX  
CREATE INDEX
```

或者查詢返回：

```
 table_name 
------------
 favorites
(1 row)
```

### 下一步

表創建成功後：

1. 重新編譯 Rust 項目：`cargo build`
2. 啟動服務器：`cargo run`
3. 測試我的最愛功能

如果還有問題，請提供具體的錯誤訊息！