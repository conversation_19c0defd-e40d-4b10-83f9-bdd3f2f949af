// Node.js 腳本來創建 favorites 表
const { Client } = require('pg');
require('dotenv').config();

async function createFavoritesTable() {
    const client = new Client({
        connectionString: process.env.DATABASE_URL
    });

    try {
        console.log('🔌 連接到資料庫...');
        await client.connect();
        console.log('✅ 資料庫連接成功');

        // 檢查現有表
        console.log('📋 檢查現有表...');
        const tablesResult = await client.query(`
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name
        `);
        console.log('現有表:', tablesResult.rows.map(row => row.table_name));

        // 檢查 favorites 表是否存在
        const favoritesExists = await client.query(`
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'favorites'
            )
        `);

        if (favoritesExists.rows[0].exists) {
            console.log('ℹ️  favorites 表已存在');
            return;
        }

        console.log('🔨 創建 favorites 表...');

        // 創建表
        await client.query(`
            CREATE TABLE favorites (
                id BIGSERIAL PRIMARY KEY,
                user_id BIGINT NOT NULL,
                product_id BIGINT NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                UNIQUE(user_id, product_id)
            )
        `);
        console.log('✅ favorites 表創建成功');

        // 創建索引
        console.log('🔍 創建索引...');
        await client.query('CREATE INDEX idx_favorites_user_id ON favorites(user_id)');
        await client.query('CREATE INDEX idx_favorites_product_id ON favorites(product_id)');
        await client.query('CREATE INDEX idx_favorites_created_at ON favorites(created_at)');
        console.log('✅ 索引創建成功');

        // 添加註釋
        console.log('📝 添加註釋...');
        await client.query("COMMENT ON TABLE favorites IS '用戶最愛產品表'");
        await client.query("COMMENT ON COLUMN favorites.id IS '主鍵ID'");
        await client.query("COMMENT ON COLUMN favorites.user_id IS '用戶ID'");
        await client.query("COMMENT ON COLUMN favorites.product_id IS '產品ID'");
        await client.query("COMMENT ON COLUMN favorites.created_at IS '收藏時間'");
        console.log('✅ 註釋添加成功');

        // 驗證
        console.log('🔍 驗證表創建...');
        const verification = await client.query(`
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_schema = 'public' AND table_name = 'favorites'
            ORDER BY ordinal_position
        `);
        console.log('表結構:', verification.rows);

        console.log('🎉 favorites 表創建完成！');

    } catch (error) {
        console.error('❌ 錯誤:', error.message);
        console.error('詳細錯誤:', error);
    } finally {
        await client.end();
        console.log('🔌 資料庫連接已關閉');
    }
}

// 執行
createFavoritesTable().catch(console.error);