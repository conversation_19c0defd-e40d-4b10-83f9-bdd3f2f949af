-- 清除聊天訊息資料表的 SQL 腳本

-- 1. 顯示當前的聊天訊息
SELECT 
    id,
    user_name,
    message,
    message_type,
    created_at
FROM chat_messages 
ORDER BY created_at DESC;

-- 2. 清除所有聊天訊息
DELETE FROM chat_messages WHERE 1=1;

-- 3. 重置自動遞增 ID
ALTER SEQUENCE chat_messages_id_seq RESTART WITH 1;

-- 4. 驗證清除結果
SELECT 
    'chat_messages' as table_name, 
    COUNT(*) as remaining_records 
FROM chat_messages;

-- 5. 如果需要，也可以清除其他可能相關的資料表
-- 檢查是否有其他聊天相關的資料表
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE '%chat%' 
OR table_name LIKE '%message%' 
OR table_name LIKE '%contact%';