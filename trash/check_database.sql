-- 檢查資料庫連線和現有表
SELECT 'Database connection successful' as status;

-- 列出所有現有的表
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- 檢查是否有 users 和 products 表
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'users') 
        THEN 'users table exists' 
        ELSE 'users table does not exist' 
    END as users_status,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'products') 
        THEN 'products table exists' 
        ELSE 'products table does not exist' 
    END as products_status;