-- 檢查用戶權限設置
SELECT 'Users Table:' as info;
SELECT id, username, role_id FROM users WHERE username = 'admin';

SELECT 'Roles Table:' as info;
SELECT * FROM roles;

SELECT 'Permissions Table:' as info;
SELECT * FROM permissions WHERE name LIKE 'orders%';

SELECT 'Role Permissions:' as info;
SELECT rp.role_id, r.name as role_name, p.name as permission_name
FROM role_permissions rp
JOIN roles r ON rp.role_id = r.id
JOIN permissions p ON rp.permission_id = p.id
WHERE p.name LIKE 'orders%';