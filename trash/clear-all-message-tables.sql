-- 清除所有訊息相關資料表的正確 SQL 腳本
-- 根據實際的資料表結構

-- 1. 清除促銷訊息系統
-- 先清除閱讀記錄（有外鍵約束）
DELETE FROM promotion_reads WHERE 1=1;
-- 再清除促銷訊息
DELETE FROM promotions WHERE 1=1;

-- 2. 清除一般訊息系統
-- 先清除閱讀狀態（有外鍵約束）
DELETE FROM message_reads WHERE 1=1;
-- 再清除訊息
DELETE FROM messages WHERE 1=1;

-- 3. 清除聊天訊息
DELETE FROM chat_messages WHERE 1=1;

-- 4. 清除留言板（你已經清空，但再確認一次）
DELETE FROM messageboard WHERE 1=1;

-- 5. 重置自動遞增 ID
ALTER SEQUENCE promotions_id_seq RESTART WITH 1;
ALTER SEQUENCE promotion_reads_id_seq RESTART WITH 1;
ALTER SEQUENCE messages_id_seq RESTART WITH 1;
ALTER SEQUENCE message_reads_id_seq RESTART WITH 1;
ALTER SEQUENCE chat_messages_id_seq RESTART WITH 1;
ALTER SEQUENCE messageboard_id_seq RESTART WITH 1;

-- 6. 驗證清除結果
SELECT 
    'promotions' as table_name, 
    COUNT(*) as remaining_records 
FROM promotions
UNION ALL
SELECT 'promotion_reads', COUNT(*) FROM promotion_reads
UNION ALL
SELECT 'messages', COUNT(*) FROM messages
UNION ALL
SELECT 'message_reads', COUNT(*) FROM message_reads
UNION ALL
SELECT 'chat_messages', COUNT(*) FROM chat_messages
UNION ALL
SELECT 'messageboard', COUNT(*) FROM messageboard;