-- 清除所有訊息相關資料的 SQL 腳本
-- 請根據你的實際資料表名稱調整

-- 1. 清除促銷訊息相關資料表
-- 如果有 promotions 資料表
TRUNCATE TABLE IF EXISTS promotions;
DELETE FROM promotions WHERE 1=1;

-- 如果有 promotion_messages 資料表
TRUNCATE TABLE IF EXISTS promotion_messages;
DELETE FROM promotion_messages WHERE 1=1;

-- 如果有 user_promotions 資料表（用戶讀取記錄）
TRUNCATE TABLE IF EXISTS user_promotions;
DELETE FROM user_promotions WHERE 1=1;

-- 2. 清除聯絡留言相關資料表
-- 如果有 contact_messages 資料表
TRUNCATE TABLE IF EXISTS contact_messages;
DELETE FROM contact_messages WHERE 1=1;

-- 如果有 contact_replies 資料表
TRUNCATE TABLE IF EXISTS contact_replies;
DELETE FROM contact_replies WHERE 1=1;

-- 3. 清除聊天訊息相關資料表
-- 如果有 chat_messages 資料表
TRUNCATE TABLE IF EXISTS chat_messages;
DELETE FROM chat_messages WHERE 1=1;

-- 如果有 chats 資料表
TRUNCATE TABLE IF EXISTS chats;
DELETE FROM chats WHERE 1=1;

-- 4. 清除一般訊息資料表
-- 你已經清空的 messageboard 資料表
TRUNCATE TABLE IF EXISTS messageboard;
DELETE FROM messageboard WHERE 1=1;

-- 如果有 messages 資料表
TRUNCATE TABLE IF EXISTS messages;
DELETE FROM messages WHERE 1=1;

-- 如果有 user_messages 資料表
TRUNCATE TABLE IF EXISTS user_messages;
DELETE FROM user_messages WHERE 1=1;

-- 5. 清除訊息讀取狀態相關資料表
-- 如果有 message_reads 資料表
TRUNCATE TABLE IF EXISTS message_reads;
DELETE FROM message_reads WHERE 1=1;

-- 如果有 user_message_status 資料表
TRUNCATE TABLE IF EXISTS user_message_status;
DELETE FROM user_message_status WHERE 1=1;

-- 6. 重置自動遞增 ID（如果需要）
-- ALTER TABLE promotions AUTO_INCREMENT = 1;
-- ALTER TABLE contact_messages AUTO_INCREMENT = 1;
-- ALTER TABLE chat_messages AUTO_INCREMENT = 1;
-- ALTER TABLE messages AUTO_INCREMENT = 1;

-- 7. 顯示清除結果
SELECT 'promotions' as table_name, COUNT(*) as remaining_records FROM promotions
UNION ALL
SELECT 'contact_messages', COUNT(*) FROM contact_messages
UNION ALL
SELECT 'chat_messages', COUNT(*) FROM chat_messages
UNION ALL
SELECT 'messages', COUNT(*) FROM messages
UNION ALL
SELECT 'messageboard', COUNT(*) FROM messageboard;