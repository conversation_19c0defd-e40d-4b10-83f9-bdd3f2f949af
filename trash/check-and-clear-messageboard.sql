-- 檢查和清除 messageboard 資料表的完整腳本

-- 1. 檢查當前 messageboard 資料表的內容
SELECT 
    id,
    user_id,
    subject,
    message,
    status,
    admin_reply,
    created_at,
    updated_at
FROM messageboard 
ORDER BY created_at DESC;

-- 2. 檢查資料表結構
\d messageboard;

-- 3. 檢查是否有觸發器或其他自動插入機制
SELECT 
    trigger_name,
    event_manipulation,
    action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'messageboard';

-- 4. 檢查是否有外鍵約束
SELECT
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND (tc.table_name='messageboard' OR ccu.table_name='messageboard');

-- 5. 完全清空 messageboard 資料表
DELETE FROM messageboard WHERE 1=1;

-- 6. 重置自動遞增序列
ALTER SEQUENCE messageboard_id_seq RESTART WITH 1;

-- 7. 驗證清除結果
SELECT 
    'messageboard' as table_name,
    COUNT(*) as remaining_records 
FROM messageboard;

-- 8. 檢查相關的其他資料表是否也有聯絡訊息
-- 檢查是否有其他可能存儲聯絡訊息的資料表
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND (
    table_name LIKE '%contact%' 
    OR table_name LIKE '%message%' 
    OR table_name LIKE '%chat%'
    OR table_name LIKE '%board%'
)
ORDER BY table_name;

-- 9. 如果有其他相關資料表，也一併檢查
-- 這裡列出可能的資料表，根據實際情況調整

-- 檢查 contact_messages（如果存在）
-- SELECT COUNT(*) as contact_messages_count FROM contact_messages;

-- 檢查 user_messages（如果存在）  
-- SELECT COUNT(*) as user_messages_count FROM user_messages;

-- 10. 最後再次確認 messageboard 已清空
SELECT 
    'Final check - messageboard records:' as description,
    COUNT(*) as count 
FROM messageboard;