#!/bin/bash

echo "🏥 啟動藥品採購系統 (開發模式)..."

# 檢查 .env 檔案是否存在
if [ ! -f .env ]; then
    echo "❌ 找不到 .env 檔案，請先複製 .env.example 並設定環境變數"
    echo "   cp .env.example .env"
    echo "   然後編輯 .env 檔案設定你的配置"
    exit 1
fi

# 載入環境變數
export $(cat .env | grep -v '^#' | xargs)

echo "🚀 啟動開發伺服器 (自動重載)..."
echo "   🌐 Web 介面: http://localhost:${PORT:-8080}/"
echo "   📡 API 端點: http://localhost:${PORT:-8080}/api"
echo "   ❤️  健康檢查: http://localhost:${PORT:-8080}/health"
echo "   檔案變更時會自動重新編譯"
echo "   按 Ctrl+C 停止伺服器"
echo ""

# 使用 cargo watch 進行自動重載 (需要安裝: cargo install cargo-watch)
if command -v cargo-watch &> /dev/null; then
    cargo watch -x run
else
    echo "💡 提示: 安裝 cargo-watch 可以實現自動重載"
    echo "   cargo install cargo-watch"
    echo ""
    cargo run
fi