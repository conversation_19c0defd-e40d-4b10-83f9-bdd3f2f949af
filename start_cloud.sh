#!/bin/bash

echo "🚀 啟動藥品採購系統 (雲端資料庫版本)..."

# 載入環境變數
if [ -f .env ]; then
    export $(cat .env | grep -v '#' | xargs)
    echo "✅ 已載入 .env 環境變數"
else
    echo "⚠️  未找到 .env 文件，使用預設環境變數"
    export DATABASE_URL="postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require"
    export JWT_SECRET="your-super-secret-jwt-key-here-for-development"
    export PORT=8080
    export RUST_LOG=info
fi

echo "🔗 資料庫連接: Neon Cloud Database"
echo "🌐 伺服器埠號: $PORT"
echo "📊 日誌等級: $RUST_LOG"
echo ""

# 啟動服務器
echo "🔄 啟動開發服務器..."
cargo run --bin pharmacy-system