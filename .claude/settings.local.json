{"permissions": {"allow": ["Bash(cargo check:*)", "Bash(cargo fix:*)", "Bash(cargo test:*)", "Bash(ls:*)", "Bash(cargo run:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(chmod:*)", "Bash(sqlite3:*)", "Bash(cargo build:*)", "<PERSON><PERSON>(pkill:*)", "Bash(pgrep:*)", "Bash(rustc:*)", "Bash(./test_rounding)", "Bash(rm:*)", "Bash(rg:*)", "Bash(find:*)", "Bash(node:*)", "Bash(./test_backend_rounding.sh:*)", "Bash(./test_order_notes.sh:*)", "Bash(grep:*)", "<PERSON><PERSON>(python3:*)", "Bash(pip3 install:*)", "Bash(cp:*)", "<PERSON><PERSON>(claude doctor)", "Bash(claude --version)", "Bash(./test_compile.sh)", "Bash(RUST_LOG=debug cargo run --bin pharmacy-system)", "Bash(timeout 10 cargo run:*)", "Bash(PORT=8081 cargo run --bin pharmacy-system)", "Bash(RUST_LOG=debug PORT=8081 cargo run --bin pharmacy-system)", "<PERSON><PERSON>(touch:*)", "Bash(./start.sh:*)", "Bash(PORT=8080 cargo run --bin pharmacy-system)", "Bash(RUST_LOG=debug PORT=8080 cargo run --bin pharmacy-system)", "Bash(RUST_LOG=trace,sqlx=debug PORT=8080 cargo run --bin pharmacy-system)", "<PERSON><PERSON>(sed:*)", "Bash(RUST_LOG=info PORT=8080 cargo run --bin pharmacy-system)", "<PERSON><PERSON>(timeout:*)", "Bash(RUST_LOG=trace PORT=8080 cargo run --bin pharmacy-system)", "Bash(RUST_LOG=info PORT=8080 ./target/debug/pharmacy-system)", "Bash(psql:*)", "<PERSON>sh(--max-time 2)", "Bash(cargo clean:*)", "Bash(RUST_LOG=debug timeout 10 cargo run --bin pharmacy-system)", "<PERSON><PERSON>(cat:*)", "Bash(RUST_LOG=info PORT=8080 timeout 10 cargo run --bin pharmacy-system)", "Bash(PORT=8080 timeout 10 cargo run --bin pharmacy-system)", "Bash(PORT=8080 ./target/debug/pharmacy-system)", "Bash(kill:*)", "Bash(PORT=8080 timeout 5 cargo run --bin pharmacy-system)", "Bash(PGPASSWORD=$DATABASE_PASSWORD:-password psql -h $DATABASE_HOST:-localhost -p $DATABASE_PORT:-5432 -U $DATABASE_USER:-happyorder -d $DATABASE_NAME:-happyorder -c \"\\d nhi_prices\")", "Bash(RUST_LOG=info PORT=8080 timeout 10 ./target/debug/pharmacy-system)", "Bash(claude migrate-installer:*)", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(true)", "Bash(PGPASSWORD=password psql:*)", "Bash(RUST_LOG=info PORT=8080 timeout 5 cargo run --bin pharmacy-system)", "Bash(RUST_LOG=info PORT=8081 timeout 10 cargo run --bin pharmacy-system)", "Bash(PORT=8081 ./target/debug/pharmacy-system)", "Bash(brew list:*)", "Bash(brew services:*)", "Bash(/opt/homebrew/bin/psql postgresql://happyorder:password@localhost:5432/happyorder -c \"SELECT COUNT(*) FROM users;\")", "Bash(DATABASE_URL=\"postgresql://happyorder:password@localhost:5432/happyorder\" RUST_LOG=debug cargo run --bin pharmacy-system)", "Bash(RUST_LOG=info PORT=8080 timeout 30 cargo run --bin pharmacy-system)", "Bash(DATABASE_URL=\"postgresql://happyorder:password@localhost:5432/happyorder\" sqlx migrate run)", "Bash(DATABASE_URL=\"postgresql://happyorder:password@localhost:5432/happyorder\" RUST_LOG=info cargo run --bin pharmacy-system)", "Bash(DATABASE_URL=\"postgresql://happyorder:password@localhost:5432/happyorder\" RUST_LOG=debug PORT=8080 timeout 15 cargo run --bin pharmacy-system)", "Bash(DATABASE_URL=\"postgresql://happyorder:password@localhost:5432/happyorder\" RUST_LOG=info PORT=8080 timeout 5 cargo run --bin pharmacy-system)", "Bash(DATABASE_URL=\"postgres://happyorder:password@localhost:5432/happyorder\" sqlx migrate run)", "Bash(DATABASE_URL=\"postgresql://postgres:@localhost:5432/postgres\" RUST_LOG=info PORT=8080 timeout 10 cargo run --bin pharmacy-system)", "<PERSON><PERSON>(createdb:*)", "Bash(DATABASE_URL=\"postgresql://happyorder:password@localhost:5432/happyorder\" RUST_LOG=info PORT=8080 timeout 15 cargo run --bin pharmacy-system)", "Bash(DATABASE_URL=\"postgresql://happyorder:password@localhost:5432/happyorder\" cargo run --bin update_user_role)", "Bash(./setup_db_simple.sh:*)", "Bash(brew install:*)", "Bash(export:*)", "Bash(/opt/homebrew/opt/libpq/bin/psql:*)", "Bash(DATABASE_URL=\"postgresql://happyorder:password@localhost:5432/happyorder\" RUST_LOG=info PORT=8080 timeout 10 cargo run --bin pharmacy-system)", "Bash(DATABASE_URL=\"postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require\" timeout 10 cargo run --bin pharmacy-system)", "Bash(DATABASE_URL=\"postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require\" RUST_LOG=info PORT=8080 timeout 10 cargo run --bin pharmacy-system)", "Bash(DATABASE_URL=\"postgresql://happyorder:password@localhost:5432/happyorder\" RUST_LOG=info PORT=8080 cargo run --bin pharmacy-system)", "Bash(DATABASE_URL=\"postgresql://happyorder:password@localhost:5432/happyorder\" cargo run --bin migrate)", "Bash(PORT=8080 python3 -m http.server 8080 --directory web)", "Bash(DATABASE_URL=\"postgresql://happyorder:password@localhost:5432/happyorder\" RUST_LOG=info PORT=8080 ./target/debug/pharmacy-system)", "Bash(bunx ccusage:*)", "Bash(/opt/homebrew/bin/psql postgresql://happyorder:password@localhost:5432/happyorder -c \"\\dt\")", "Bash(DATABASE_URL=\"postgresql://happyorder:password@localhost:5432/happyorder\" RUST_LOG=info PORT=8080 timeout 30 cargo run --bin pharmacy-system)", "Bash(DATABASE_URL=\"postgresql://happyorder:password@localhost:5432/happyorder\" RUST_LOG=info PORT=8081 timeout 15 cargo run --bin pharmacy-system)", "Bash(DATABASE_URL=\"postgresql://happyorder:password@localhost:5432/happyorder\" RUST_LOG=info PORT=8081 timeout 30 cargo run --bin pharmacy-system)", "Bash(DATABASE_URL=\"postgresql://happyorder:password@localhost:5432/happyorder\" RUST_LOG=info PORT=8080 timeout 20 cargo run --bin pharmacy-system)", "Bash(DATABASE_URL=\"postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require\" RUST_LOG=info PORT=8080 timeout 15 cargo run --bin pharmacy-system)", "Bash(DATABASE_URL=\"postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require\" RUST_LOG=info PORT=8080 timeout 20 cargo run --bin pharmacy-system)", "Bash(DATABASE_URL=\"postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require\" RUST_LOG=info PORT=8080 timeout 30 cargo run --bin pharmacy-system)", "Bash(PGPASSWORD=npg_SzpN3Lj7CEnc /opt/homebrew/bin/psql -h ep-raspy-bird-a1fvavzn-pooler.ap-southeast-1.aws.neon.tech -p 5432 -U seo1515_owner -d seo1515 -c \"SELECT * FROM messageboard LIMIT 5;\")", "Bash(PGPASSWORD=npg_SzpN3Lj7CEnc psql -h ep-raspy-bird-a1fvavzn-pooler.ap-southeast-1.aws.neon.tech -p 5432 -U seo1515_owner -d seo1515 -c \"SELECT * FROM messageboard LIMIT 5;\")", "Bash(PGPASSWORD=npg_SzpN3Lj7CEnc psql -h ep-raspy-bird-a1fvavzn-pooler.ap-southeast-1.aws.neon.tech -p 5432 -U seo1515_owner -d seo1515 -c \"SELECT id, email, role_id FROM users WHERE role_id = 1 LIMIT 5;\")", "Bash(DATABASE_URL=\"postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require\" RUST_LOG=info PORT=8080 cargo run --bin pharmacy-system)", "Bash(PGPASSWORD=npg_SzpN3Lj7CEnc psql -h ep-raspy-bird-a1fvavzn-pooler.ap-southeast-1.aws.neon.tech -p 5432 -U seo1515_owner -d seo1515 -c \"SELECT id, username, email FROM users WHERE role_id = 1 LIMIT 3;\")", "Bash(/opt/homebrew/bin/psql postgresql://happyorder:password@localhost:5432/happyorder -c \"SELECT name, ingredients FROM products WHERE ingredients ILIKE ''%fenof%'' LIMIT 5;\")", "Bash(DATABASE_URL=\"postgresql://happyorder:password@localhost:5432/happyorder\" cargo run --bin test_search)", "Bash(DATABASE_URL=\"postgresql://happyorder:password@localhost:5432/happyorder\" cargo run --bin test_search_issue)", "Bash(DATABASE_URL=\"postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require\" cargo run --bin check_products)", "Bash(DATABASE_URL=\"postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require\" cargo run --bin test_search_issue)", "Bash(DATABASE_URL=\"postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require\" RUST_LOG=info PORT=8081 timeout 15 cargo run --bin pharmacy-system)", "Bash(PGPASSWORD=npg_SzpN3Lj7CEnc psql -h ep-raspy-bird-a1fvavzn-pooler.ap-southeast-1.aws.neon.tech -p 5432 -U seo1515_owner -d seo1515 -c \"SELECT id, subject, message, admin_reply FROM messageboard WHERE message LIKE ''%你再嗎%'' ORDER BY id DESC LIMIT 3;\")", "<PERSON><PERSON>(mv:*)", "Bash(PGPASSWORD=npg_SzpN3Lj7CEnc /opt/homebrew/Cellar/libpq/17.5/bin/psql -h ep-raspy-bird-a1fvavzn-pooler.ap-southeast-1.aws.neon.tech -p 5432 -U seo1515_owner -d seo1515 -c \"SELECT column_name, data_type FROM information_schema.columns WHERE table_name = ''products'' ORDER BY ordinal_position;\")", "Bash(DATABASE_URL=\"postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require\" RUST_LOG=debug PORT=8080 cargo run --bin pharmacy-system)", "Bash(DATABASE_URL=\"postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require\" RUST_LOG=debug PORT=8080 timeout 10 cargo run --bin pharmacy-system)", "Bash(DATABASE_URL=\"postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require\" PORT=8080 cargo run --bin pharmacy-system)", "Bash(PGPASSWORD=npg_SzpN3Lj7CEnc /opt/homebrew/Cellar/libpq/17.5/bin/psql -h ep-raspy-bird-a1fvavzn-pooler.ap-southeast-1.aws.neon.tech -p 5432 -U seo1515_owner -d seo1515 -c \"SELECT id, username, email, role_id FROM users LIMIT 5;\")", "Bash(PGPASSWORD=npg_SzpN3Lj7CEnc psql -h ep-raspy-bird-a1fvavzn-pooler.ap-southeast-1.aws.neon.tech -p 5432 -U seo1515_owner -d seo1515 -c \"SELECT id, created_at, updated_at FROM orders LIMIT 3;\")", "Bash(PGPASSWORD=npg_SzpN3Lj7CEnc psql -h ep-raspy-bird-a1fvavzn-pooler.ap-southeast-1.aws.neon.tech -p 5432 -U seo1515_owner -d seo1515 -c \"SELECT id, username, email, role_id FROM users WHERE role_id = 1 LIMIT 3;\")", "Bash(PGPASSWORD=npg_SzpN3Lj7CEnc psql -h ep-raspy-bird-a1fvavzn-pooler.ap-southeast-1.aws.neon.tech -p 5432 -U seo1515_owner -d seo1515 -c \"SELECT id, username, email, role_id FROM users LIMIT 5;\")", "Bash(PGPASSWORD=npg_SzpN3Lj7CEnc psql -h ep-raspy-bird-a1fvavzn-pooler.ap-southeast-1.aws.neon.tech -p 5432 -U seo1515_owner -d seo1515 -c \"SELECT id, order_number, user_id, status, created_at FROM orders LIMIT 3;\")", "Bash(PGPASSWORD=npg_SzpN3Lj7CEnc psql -h ep-raspy-bird-a1fvavzn-pooler.ap-southeast-1.aws.neon.tech -p 5432 -U seo1515_owner -d seo1515 -c \"SELECT id, username, email, role_id FROM users WHERE id = 3;\")", "Bash(PGPASSWORD=npg_SzpN3Lj7CEnc psql -h ep-raspy-bird-a1fvavzn-pooler.ap-southeast-1.aws.neon.tech -p 5432 -U seo1515_owner -d seo1515 -c \"SELECT id, order_number, created_at, updated_at FROM orders WHERE id = 41;\")", "Bash(DATABASE_URL=\"postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require\" RUST_LOG=info PORT=8081 timeout 10 cargo run --bin pharmacy-system)", "Bash(git add:*)", "Bash(PGPASSWORD=npg_SzpN3Lj7CEnc psql -h ep-raspy-bird-a1fvavzn-pooler.ap-southeast-1.aws.neon.tech -p 5432 -U seo1515_owner -d seo1515 -c \"SELECT id, name FROM roles ORDER BY id;\")", "Bash(PGPASSWORD=npg_SzpN3Lj7CEnc psql -h ep-raspy-bird-a1fvavzn-pooler.ap-southeast-1.aws.neon.tech -p 5432 -U seo1515_owner -d seo1515 -c \"SELECT column_name, data_type FROM information_schema.columns WHERE table_name = ''users'' ORDER BY ordinal_position;\")"], "deny": [], "defaultMode": "acceptEdits"}}