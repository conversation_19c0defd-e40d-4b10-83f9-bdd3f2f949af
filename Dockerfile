# 使用官方 Rust 映像作為建置階段
FROM rust:1.75 as builder

# 設定工作目錄
WORKDIR /usr/src/app

# 複製 Cargo.toml 和 Cargo.lock
COPY Cargo.toml Cargo.lock ./

# 建立一個虛假的 main.rs 來快取依賴項目
RUN mkdir src && echo "fn main() {}" > src/main.rs

# 建置依賴項目
RUN cargo build --release

# 移除虛假的 main.rs 並複製實際的源碼
RUN rm src/main.rs
COPY src ./src
COPY migrations ./migrations

# 重新建置應用程式
RUN cargo build --release

# 使用輕量級的 debian-slim 映像作為執行階段
FROM debian:bookworm-slim

# 安裝必要的系統依賴項目
RUN apt-get update && apt-get install -y \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# 建立非 root 使用者
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 設定工作目錄
WORKDIR /app

# 從建置階段複製編譯好的二進制檔案
COPY --from=builder /usr/src/app/target/release/pharmacy-system /app/pharmacy-system

# 複製遷移檔案
COPY --from=builder /usr/src/app/migrations /app/migrations

# 變更檔案擁有者
RUN chown -R appuser:appuser /app

# 切換到非 root 使用者
USER appuser

# 暴露端口
EXPOSE 8080

# 設定健康檢查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 啟動應用程式
CMD ["./pharmacy-system"] 