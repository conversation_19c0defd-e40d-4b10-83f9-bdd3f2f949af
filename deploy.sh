#!/bin/bash

# 部署腳本
echo "開始部署藥房管理系統..."

# 設定變數
DEPLOY_ENV=${1:-production}  # 預設為 production
BUILD_DIR="dist"
WEB_DIR="web"

echo "部署環境: $DEPLOY_ENV"

# 創建建置目錄
mkdir -p $BUILD_DIR

# 複製檔案
echo "複製檔案..."
cp -r $WEB_DIR/* $BUILD_DIR/

# 根據環境更新配置
case $DEPLOY_ENV in
  "development")
    echo "設定開發環境..."
    sed -i '' 's/window\.CONFIG = window\.AppConfig\.getCurrentConfig();/window.CONFIG = window.AppConfig.development;/' $BUILD_DIR/js/config.js
    ;;
  "staging")
    echo "設定測試環境..."
    sed -i '' 's/window\.CONFIG = window\.AppConfig\.getCurrentConfig();/window.CONFIG = window.AppConfig.staging;/' $BUILD_DIR/js/config.js
    ;;
  "production")
    echo "設定生產環境..."
    sed -i '' 's/window\.CONFIG = window\.AppConfig\.getCurrentConfig();/window.CONFIG = window.AppConfig.production;/' $BUILD_DIR/js/config.js
    ;;
esac

echo "部署完成！檔案位於 $BUILD_DIR 目錄"
echo ""
echo "使用方法："
echo "  開發環境: ./deploy.sh development"
echo "  測試環境: ./deploy.sh staging"  
echo "  生產環境: ./deploy.sh production"
echo ""
echo "生產環境將部署到: https://order.53617503.xyz"