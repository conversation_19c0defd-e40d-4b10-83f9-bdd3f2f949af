  [2m2025-08-11T09:47:57.625256Z[0m [32m INFO[0m [1;32mpharmacy_system::logging[0m[32m: [32m日誌系統初始化完成, [1;32mservice_name[0m[32m: "pharmacy-system", [1;32mservice_version[0m[32m: "0.1.0", [1;32mlog_level[0m[32m: INFO, [1;32mjson_format[0m[32m: false[0m

  [2m2025-08-11T09:47:57.625430Z[0m [32m INFO[0m [1;32mpharmacy_system::database[0m[32m: [32mInitializing database with configuration[0m

  [2m2025-08-11T09:47:57.625435Z[0m [32m INFO[0m [1;32mpharmacy_system::database::connection[0m[32m: [32mCreating database connection manager[0m

  [2m2025-08-11T09:47:58.071474Z[0m [32m INFO[0m [1;32mpharmacy_system::database::connection[0m[32m: [32mDatabase connection manager created successfully[0m

  [2m2025-08-11T09:47:58.071522Z[0m [32m INFO[0m [1;32mpharmacy_system::database[0m[32m: [32mStarting database initialization...[0m

  [2m2025-08-11T09:47:58.071537Z[0m [32m INFO[0m [1;32mpharmacy_system::database::migration[0m[32m: [32mStarting database migrations...[0m

  [2m2025-08-11T09:47:58.071546Z[0m [32m INFO[0m [1;32mpharmacy_system::database::migration[0m[32m: [32mSkipping migrations - database structure already exists[0m

  [2m2025-08-11T09:47:58.250859Z[0m [32m INFO[0m [1;32mpharmacy_system::database::migration[0m[32m: [32mDatabase structure verified - users table exists[0m

  [2m2025-08-11T09:47:58.250885Z[0m [32m INFO[0m [1;32mpharmacy_system::database::migration[0m[32m: [32mDatabase migrations completed successfully[0m

  [2m2025-08-11T09:47:58.807614Z[0m [32m INFO[0m [1;32mpharmacy_system::database[0m[32m: [32mDatabase initialization completed[0m

  [2m2025-08-11T09:47:58.807661Z[0m [32m INFO[0m [1;32mpharmacy_system::database[0m[32m: [32mDatabase initialization completed successfully[0m

  [2m2025-08-11T09:47:58.807714Z[0m [32m INFO[0m [1;32mpharmacy_system[0m[32m: [32mCreating backup service...[0m

  [2m2025-08-11T09:47:58.807746Z[0m [33m WARN[0m [1;33mpharmacy_system::services::backup[0m[33m: [33mNo GCP credentials provided. Backups will be local only.[0m

  [2m2025-08-11T09:47:58.808155Z[0m [32m INFO[0m [1;32mpharmacy_system[0m[32m: [32mBackup service created successfully[0m

  [2m2025-08-11T09:47:58.808177Z[0m [32m INFO[0m [1;32mpharmacy_system[0m[32m: [32mCreating notification service...[0m

  [2m2025-08-11T09:47:58.812532Z[0m [32m INFO[0m [1;32mpharmacy_system[0m[32m: [32mNotification service created successfully[0m

  [2m2025-08-11T09:47:58.815134Z[0m [32m INFO[0m [1;32mpharmacy_system[0m[32m: [32mAPI routes configured:[0m

  [2m2025-08-11T09:47:58.815145Z[0m [32m INFO[0m [1;32mpharmacy_system[0m[32m: [32m  - / (API info)[0m

  [2m2025-08-11T09:47:58.815150Z[0m [32m INFO[0m [1;32mpharmacy_system[0m[32m: [32m  - /health (Health check)[0m

  [2m2025-08-11T09:47:58.815154Z[0m [32m INFO[0m [1;32mpharmacy_system[0m[32m: [32m  - /api/auth/* (Authentication)[0m

  [2m2025-08-11T09:47:58.815158Z[0m [32m INFO[0m [1;32mpharmacy_system[0m[32m: [32m  - /api/products/* (Product management)[0m

  [2m2025-08-11T09:47:58.815162Z[0m [32m INFO[0m [1;32mpharmacy_system[0m[32m: [32m  - /api/orders/* (Order management)[0m

  [2m2025-08-11T09:47:58.815166Z[0m [32m INFO[0m [1;32mpharmacy_system[0m[32m: [32m  - /api/cart/* (Cart management)[0m

  [2m2025-08-11T09:47:58.815169Z[0m [32m INFO[0m [1;32mpharmacy_system[0m[32m: [32m  - /api/backup/* (Backup operations)[0m

  [2m2025-08-11T09:47:58.815173Z[0m [32m INFO[0m [1;32mpharmacy_system[0m[32m: [32m  - /api/notifications/* (Notification services)[0m

  [2m2025-08-11T09:47:58.815177Z[0m [32m INFO[0m [1;32mpharmacy_system[0m[32m: [32mServer starting on 0.0.0.0:8080[0m

  [2m2025-08-11T09:47:58.815266Z[0m [32m INFO[0m [1;32mpharmacy_system[0m[32m: [32mServer bound to address successfully, now serving requests...[0m

  [2m2025-08-11T09:47:58.815272Z[0m [32m INFO[0m [1;32mpharmacy_system[0m[32m: [32m🚀 Pharmacy System is now running on http://0.0.0.0:8080[0m

  [2m2025-08-11T09:48:37.295472Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /health, [1;32muser_agent[0m[32m: curl/8.7.1, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 750398ee-a69d-473a-9e01-ff719565283a[0m

  [2m2025-08-11T09:48:37.295661Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /health, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:48:37.295923Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 289.167µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /health, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:48:37.295942Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/health", [1mrequest_id[0m: "750398ee-a69d-473a-9e01-ff719565283a", [1mduration_ms[0m: 0, [1mstatus_code[0m: 200

  [2m2025-08-11T09:48:37.295949Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 6.21µs, [1;32mtime.idle[0m[32m: 666ns[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/health", [1mrequest_id[0m: "750398ee-a69d-473a-9e01-ff719565283a", [1mduration_ms[0m: 0, [1mstatus_code[0m: 200

  [2m2025-08-11T09:48:37.295960Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求成功完成, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /health, [1;32mstatus[0m[32m: 200 OK, [1;32mduration_ms[0m[32m: 0, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 750398ee-a69d-473a-9e01-ff719565283a[0m

  [2m2025-08-11T09:48:37.295977Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 272µs, [1;32mtime.idle[0m[32m: 46.2µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /health, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:17.495810Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 624e0706-e8e9-4dc8-bca3-8af4ebaf53b5[0m

  [2m2025-08-11T09:49:17.495990Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:17.496373Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 402.833µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:17.496390Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/", [1mrequest_id[0m: "624e0706-e8e9-4dc8-bca3-8af4ebaf53b5", [1mduration_ms[0m: 0, [1mstatus_code[0m: 304

  [2m2025-08-11T09:49:17.496395Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 5.38µs, [1;32mtime.idle[0m[32m: 500ns[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/", [1mrequest_id[0m: "624e0706-e8e9-4dc8-bca3-8af4ebaf53b5", [1mduration_ms[0m: 0, [1mstatus_code[0m: 304

  [2m2025-08-11T09:49:17.496415Z[0m [31mERROR[0m [1;31mpharmacy_system::api::middleware[0m[31m: [31mHTTP 請求伺服器錯誤, [1;31mmethod[0m[31m: GET, [1;31muri[0m[31m: /, [1;31mstatus[0m[31m: 304 Not Modified, [1;31mduration_ms[0m[31m: 0, [1;31muser_id[0m[31m: None, [1;31mrequest_id[0m[31m: 624e0706-e8e9-4dc8-bca3-8af4ebaf53b5[0m

  [2m2025-08-11T09:49:17.496426Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 188µs, [1;32mtime.idle[0m[32m: 250µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:17.562567Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /.well-known/appspecific/com.chrome.devtools.json, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 545e15de-4b09-40fe-ba3b-29633387cfff[0m

  [2m2025-08-11T09:49:17.562604Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /.well-known/appspecific/com.chrome.devtools.json, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:17.562786Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 189.916µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /.well-known/appspecific/com.chrome.devtools.json, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:17.562867Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/.well-known/appspecific/com.chrome.devtools.json", [1mrequest_id[0m: "545e15de-4b09-40fe-ba3b-29633387cfff", [1mduration_ms[0m: 0, [1mstatus_code[0m: 404

  [2m2025-08-11T09:49:17.562901Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 32.2µs, [1;32mtime.idle[0m[32m: 708ns[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/.well-known/appspecific/com.chrome.devtools.json", [1mrequest_id[0m: "545e15de-4b09-40fe-ba3b-29633387cfff", [1mduration_ms[0m: 0, [1mstatus_code[0m: 404

  [2m2025-08-11T09:49:17.562933Z[0m [33m WARN[0m [1;33mpharmacy_system::api::middleware[0m[33m: [33mHTTP 請求客戶端錯誤, [1;33mmethod[0m[33m: GET, [1;33muri[0m[33m: /.well-known/appspecific/com.chrome.devtools.json, [1;33mstatus[0m[33m: 404 Not Found, [1;33mduration_ms[0m[33m: 0, [1;33muser_id[0m[33m: None, [1;33mrequest_id[0m[33m: 545e15de-4b09-40fe-ba3b-29633387cfff[0m

  [2m2025-08-11T09:49:17.562953Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 105µs, [1;32mtime.idle[0m[32m: 247µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /.well-known/appspecific/com.chrome.devtools.json, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:17.565988Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /css/style.css?v=20250809-address, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 8f95a912-023f-4a44-a650-d5dd9435cc32[0m

  [2m2025-08-11T09:49:17.566087Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /css/style.css?v=20250809-address, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:17.566501Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /js/app.js?v=20250811-deleterole, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: bec10b3b-e82d-4269-bcfc-2fb4161f8950[0m

  [2m2025-08-11T09:49:17.566512Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /js/app.js?v=20250811-deleterole, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:17.566904Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 822.042µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /css/style.css?v=20250809-address, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:17.566912Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 401.25µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /js/app.js?v=20250811-deleterole, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:17.566919Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/css/style.css", [1mrequest_id[0m: "8f95a912-023f-4a44-a650-d5dd9435cc32", [1mduration_ms[0m: 0, [1mstatus_code[0m: 304

  [2m2025-08-11T09:49:17.566928Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/js/app.js", [1mrequest_id[0m: "bec10b3b-e82d-4269-bcfc-2fb4161f8950", [1mduration_ms[0m: 0, [1mstatus_code[0m: 304

  [2m2025-08-11T09:49:17.566926Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 6.75µs, [1;32mtime.idle[0m[32m: 459ns[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/css/style.css", [1mrequest_id[0m: "8f95a912-023f-4a44-a650-d5dd9435cc32", [1mduration_ms[0m: 0, [1mstatus_code[0m: 304

  [2m2025-08-11T09:49:17.566944Z[0m [31mERROR[0m [1;31mpharmacy_system::api::middleware[0m[31m: [31mHTTP 請求伺服器錯誤, [1;31mmethod[0m[31m: GET, [1;31muri[0m[31m: /css/style.css?v=20250809-address, [1;31mstatus[0m[31m: 304 Not Modified, [1;31mduration_ms[0m[31m: 0, [1;31muser_id[0m[31m: None, [1;31mrequest_id[0m[31m: 8f95a912-023f-4a44-a650-d5dd9435cc32[0m

  [2m2025-08-11T09:49:17.566954Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 123µs, [1;32mtime.idle[0m[32m: 746µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /css/style.css?v=20250809-address, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:17.566935Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 7.08µs, [1;32mtime.idle[0m[32m: 458ns[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/js/app.js", [1mrequest_id[0m: "bec10b3b-e82d-4269-bcfc-2fb4161f8950", [1mduration_ms[0m: 0, [1mstatus_code[0m: 304

  [2m2025-08-11T09:49:17.567026Z[0m [31mERROR[0m [1;31mpharmacy_system::api::middleware[0m[31m: [31mHTTP 請求伺服器錯誤, [1;31mmethod[0m[31m: GET, [1;31muri[0m[31m: /js/app.js?v=20250811-deleterole, [1;31mstatus[0m[31m: 304 Not Modified, [1;31mduration_ms[0m[31m: 0, [1;31muser_id[0m[31m: None, [1;31mrequest_id[0m[31m: bec10b3b-e82d-4269-bcfc-2fb4161f8950[0m

  [2m2025-08-11T09:49:17.567035Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 62.8µs, [1;32mtime.idle[0m[32m: 461µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /js/app.js?v=20250811-deleterole, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:25.122695Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: POST, [1;32muri[0m[32m: /api/auth/login, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 2675272f-98d5-4bad-a224-c8deec6a9ba1[0m

  [2m2025-08-11T09:49:25.123090Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: POST, [1muri[0m: /api/auth/login, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:25.758422Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 635.426708ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: POST, [1muri[0m: /api/auth/login, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:25.758525Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "POST", [1mpath[0m: "/api/auth/login", [1mrequest_id[0m: "2675272f-98d5-4bad-a224-c8deec6a9ba1", [1mduration_ms[0m: 635, [1mstatus_code[0m: 200

  [2m2025-08-11T09:49:25.758543Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 18.0µs, [1;32mtime.idle[0m[32m: 2.83µs[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "POST", [1mpath[0m: "/api/auth/login", [1mrequest_id[0m: "2675272f-98d5-4bad-a224-c8deec6a9ba1", [1mduration_ms[0m: 635, [1mstatus_code[0m: 200

  [2m2025-08-11T09:49:25.758561Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求成功完成, [1;32mmethod[0m[32m: POST, [1;32muri[0m[32m: /api/auth/login, [1;32mstatus[0m[32m: 200 OK, [1;32mduration_ms[0m[32m: 635, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 2675272f-98d5-4bad-a224-c8deec6a9ba1[0m

  [2m2025-08-11T09:49:25.758606Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 250ms, [1;32mtime.idle[0m[32m: 386ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: POST, [1muri[0m: /api/auth/login, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:25.770436Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /api/products?page=1&limit=10, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 8bf26414-afd9-4982-bf85-0e87afa111c4[0m

  [2m2025-08-11T09:49:25.770723Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/products?page=1&limit=10, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:25.952640Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 181.925375ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/products?page=1&limit=10, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:25.952710Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/api/products", [1mrequest_id[0m: "8bf26414-afd9-4982-bf85-0e87afa111c4", [1mduration_ms[0m: 182, [1mstatus_code[0m: 200

  [2m2025-08-11T09:49:25.952726Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 15.3µs, [1;32mtime.idle[0m[32m: 2.17µs[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/api/products", [1mrequest_id[0m: "8bf26414-afd9-4982-bf85-0e87afa111c4", [1mduration_ms[0m: 182, [1mstatus_code[0m: 200

  [2m2025-08-11T09:49:25.952740Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求成功完成, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /api/products?page=1&limit=10, [1;32mstatus[0m[32m: 200 OK, [1;32mduration_ms[0m[32m: 182, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 8bf26414-afd9-4982-bf85-0e87afa111c4[0m

  [2m2025-08-11T09:49:25.952769Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 986µs, [1;32mtime.idle[0m[32m: 181ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/products?page=1&limit=10, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:28.869696Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /api/admin/roles, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 4163fa20-d396-4255-9d7e-338f6e834b37[0m

  [2m2025-08-11T09:49:28.870098Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/admin/roles, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:29.006282Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 136.211166ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/admin/roles, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:29.006366Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/api/admin/roles", [1mrequest_id[0m: "4163fa20-d396-4255-9d7e-338f6e834b37", [1mduration_ms[0m: 136, [1mstatus_code[0m: 200

  [2m2025-08-11T09:49:29.006383Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 16.3µs, [1;32mtime.idle[0m[32m: 2.50µs[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/api/admin/roles", [1mrequest_id[0m: "4163fa20-d396-4255-9d7e-338f6e834b37", [1mduration_ms[0m: 136, [1mstatus_code[0m: 200

  [2m2025-08-11T09:49:29.006426Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求成功完成, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /api/admin/roles, [1;32mstatus[0m[32m: 200 OK, [1;32mduration_ms[0m[32m: 136, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 4163fa20-d396-4255-9d7e-338f6e834b37[0m

  [2m2025-08-11T09:49:29.006460Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 859µs, [1;32mtime.idle[0m[32m: 136ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/admin/roles, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:31.759067Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: DELETE, [1;32muri[0m[32m: /api/admin/roles/1, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 224f1c1d-e9b8-4cbe-952c-e4ecefe326ff[0m

  [2m2025-08-11T09:49:31.759680Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: DELETE, [1muri[0m: /api/admin/roles/1, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:31.940689Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 181.023584ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: DELETE, [1muri[0m: /api/admin/roles/1, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:31.940753Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "DELETE", [1mpath[0m: "/api/admin/roles/1", [1mrequest_id[0m: "224f1c1d-e9b8-4cbe-952c-e4ecefe326ff", [1mduration_ms[0m: 181, [1mstatus_code[0m: 200

  [2m2025-08-11T09:49:31.940768Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 13.8µs, [1;32mtime.idle[0m[32m: 2.17µs[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "DELETE", [1mpath[0m: "/api/admin/roles/1", [1mrequest_id[0m: "224f1c1d-e9b8-4cbe-952c-e4ecefe326ff", [1mduration_ms[0m: 181, [1mstatus_code[0m: 200

  [2m2025-08-11T09:49:31.940784Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求成功完成, [1;32mmethod[0m[32m: DELETE, [1;32muri[0m[32m: /api/admin/roles/1, [1;32mstatus[0m[32m: 200 OK, [1;32mduration_ms[0m[32m: 181, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 224f1c1d-e9b8-4cbe-952c-e4ecefe326ff[0m

  [2m2025-08-11T09:49:31.940815Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 831µs, [1;32mtime.idle[0m[32m: 180ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: DELETE, [1muri[0m: /api/admin/roles/1, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:31.953199Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /api/admin/roles, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: be8cf2e1-abee-41f5-ab5a-7cfc09f3144b[0m

  [2m2025-08-11T09:49:31.953798Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/admin/roles, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:32.090165Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 136.372041ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/admin/roles, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:32.090259Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/api/admin/roles", [1mrequest_id[0m: "be8cf2e1-abee-41f5-ab5a-7cfc09f3144b", [1mduration_ms[0m: 137, [1mstatus_code[0m: 200

  [2m2025-08-11T09:49:32.090276Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 16.3µs, [1;32mtime.idle[0m[32m: 2.79µs[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/api/admin/roles", [1mrequest_id[0m: "be8cf2e1-abee-41f5-ab5a-7cfc09f3144b", [1mduration_ms[0m: 137, [1mstatus_code[0m: 200

  [2m2025-08-11T09:49:32.090293Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求成功完成, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /api/admin/roles, [1;32mstatus[0m[32m: 200 OK, [1;32mduration_ms[0m[32m: 137, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: be8cf2e1-abee-41f5-ab5a-7cfc09f3144b[0m

  [2m2025-08-11T09:49:32.090356Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 742µs, [1;32mtime.idle[0m[32m: 136ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/admin/roles, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:35.868422Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 3e2a93ba-bce3-475a-8fdd-c7a1a3591acf[0m

  [2m2025-08-11T09:49:35.868498Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:35.868892Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 405.292µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:35.868935Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/", [1mrequest_id[0m: "3e2a93ba-bce3-475a-8fdd-c7a1a3591acf", [1mduration_ms[0m: 0, [1mstatus_code[0m: 304

  [2m2025-08-11T09:49:35.868946Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 10.1µs, [1;32mtime.idle[0m[32m: 1.67µs[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/", [1mrequest_id[0m: "3e2a93ba-bce3-475a-8fdd-c7a1a3591acf", [1mduration_ms[0m: 0, [1mstatus_code[0m: 304

  [2m2025-08-11T09:49:35.868961Z[0m [31mERROR[0m [1;31mpharmacy_system::api::middleware[0m[31m: [31mHTTP 請求伺服器錯誤, [1;31mmethod[0m[31m: GET, [1;31muri[0m[31m: /, [1;31mstatus[0m[31m: 304 Not Modified, [1;31mduration_ms[0m[31m: 0, [1;31muser_id[0m[31m: None, [1;31mrequest_id[0m[31m: 3e2a93ba-bce3-475a-8fdd-c7a1a3591acf[0m

  [2m2025-08-11T09:49:35.869034Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 197µs, [1;32mtime.idle[0m[32m: 342µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:35.882538Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /.well-known/appspecific/com.chrome.devtools.json, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 92a7a7b6-c356-42ce-a535-fa06f441ddf2[0m

  [2m2025-08-11T09:49:35.882589Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /.well-known/appspecific/com.chrome.devtools.json, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:35.882741Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 162.042µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /.well-known/appspecific/com.chrome.devtools.json, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:35.882774Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/.well-known/appspecific/com.chrome.devtools.json", [1mrequest_id[0m: "92a7a7b6-c356-42ce-a535-fa06f441ddf2", [1mduration_ms[0m: 0, [1mstatus_code[0m: 404

  [2m2025-08-11T09:49:35.882787Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 12.2µs, [1;32mtime.idle[0m[32m: 1.17µs[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/.well-known/appspecific/com.chrome.devtools.json", [1mrequest_id[0m: "92a7a7b6-c356-42ce-a535-fa06f441ddf2", [1mduration_ms[0m: 0, [1mstatus_code[0m: 404

  [2m2025-08-11T09:49:35.882802Z[0m [33m WARN[0m [1;33mpharmacy_system::api::middleware[0m[33m: [33mHTTP 請求客戶端錯誤, [1;33mmethod[0m[33m: GET, [1;33muri[0m[33m: /.well-known/appspecific/com.chrome.devtools.json, [1;33mstatus[0m[33m: 404 Not Found, [1;33mduration_ms[0m[33m: 0, [1;33muser_id[0m[33m: None, [1;33mrequest_id[0m[33m: 92a7a7b6-c356-42ce-a535-fa06f441ddf2[0m

  [2m2025-08-11T09:49:35.882823Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 121µs, [1;32mtime.idle[0m[32m: 116µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /.well-known/appspecific/com.chrome.devtools.json, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:35.897682Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /api/products?page=1&limit=10, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: cdbe542b-0037-4da3-9dee-af7e0d35ec21[0m

  [2m2025-08-11T09:49:35.897730Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/products?page=1&limit=10, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:35.898375Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /api/auth/me, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: b95d31f7-8dce-462a-815f-185344f39b07[0m

  [2m2025-08-11T09:49:35.898427Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/auth/me, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:36.017003Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 119.278209ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/products?page=1&limit=10, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:36.017070Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/api/products", [1mrequest_id[0m: "cdbe542b-0037-4da3-9dee-af7e0d35ec21", [1mduration_ms[0m: 119, [1mstatus_code[0m: 200

  [2m2025-08-11T09:49:36.017084Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 13.3µs, [1;32mtime.idle[0m[32m: 1.58µs[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/api/products", [1mrequest_id[0m: "cdbe542b-0037-4da3-9dee-af7e0d35ec21", [1mduration_ms[0m: 119, [1mstatus_code[0m: 200

  [2m2025-08-11T09:49:36.017096Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求成功完成, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /api/products?page=1&limit=10, [1;32mstatus[0m[32m: 200 OK, [1;32mduration_ms[0m[32m: 119, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: cdbe542b-0037-4da3-9dee-af7e0d35ec21[0m

  [2m2025-08-11T09:49:36.017121Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 461µs, [1;32mtime.idle[0m[32m: 119ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/products?page=1&limit=10, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:36.529298Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 630.859791ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/auth/me, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:36.529411Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/api/auth/me", [1mrequest_id[0m: "b95d31f7-8dce-462a-815f-185344f39b07", [1mduration_ms[0m: 631, [1mstatus_code[0m: 200

  [2m2025-08-11T09:49:36.529438Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 24.2µs, [1;32mtime.idle[0m[32m: 4.25µs[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/api/auth/me", [1mrequest_id[0m: "b95d31f7-8dce-462a-815f-185344f39b07", [1mduration_ms[0m: 631, [1mstatus_code[0m: 200

  [2m2025-08-11T09:49:36.529463Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求成功完成, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /api/auth/me, [1;32mstatus[0m[32m: 200 OK, [1;32mduration_ms[0m[32m: 631, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: b95d31f7-8dce-462a-815f-185344f39b07[0m

  [2m2025-08-11T09:49:36.529561Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 4.64ms, [1;32mtime.idle[0m[32m: 626ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/auth/me, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:37.651954Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /api/admin/roles, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 03c18681-f9cf-463c-bcfe-8c9eca7129af[0m

  [2m2025-08-11T09:49:37.652023Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/admin/roles, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:37.772732Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 120.71625ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/admin/roles, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:37.772784Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/api/admin/roles", [1mrequest_id[0m: "03c18681-f9cf-463c-bcfe-8c9eca7129af", [1mduration_ms[0m: 120, [1mstatus_code[0m: 200

  [2m2025-08-11T09:49:37.772796Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 11.2µs, [1;32mtime.idle[0m[32m: 1.62µs[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/api/admin/roles", [1mrequest_id[0m: "03c18681-f9cf-463c-bcfe-8c9eca7129af", [1mduration_ms[0m: 120, [1mstatus_code[0m: 200

  [2m2025-08-11T09:49:37.772810Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求成功完成, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /api/admin/roles, [1;32mstatus[0m[32m: 200 OK, [1;32mduration_ms[0m[32m: 120, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 03c18681-f9cf-463c-bcfe-8c9eca7129af[0m

  [2m2025-08-11T09:49:37.772834Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 489µs, [1;32mtime.idle[0m[32m: 120ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/admin/roles, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:50.624414Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: DELETE, [1;32muri[0m[32m: /api/admin/roles/1, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 48662303-d301-4091-82f7-84c6d58b457f[0m

  [2m2025-08-11T09:49:50.624930Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: DELETE, [1muri[0m: /api/admin/roles/1, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:50.761301Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 136.368083ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: DELETE, [1muri[0m: /api/admin/roles/1, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:50.761393Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "DELETE", [1mpath[0m: "/api/admin/roles/1", [1mrequest_id[0m: "48662303-d301-4091-82f7-84c6d58b457f", [1mduration_ms[0m: 136, [1mstatus_code[0m: 200

  [2m2025-08-11T09:49:50.761406Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 12.5µs, [1;32mtime.idle[0m[32m: 1.71µs[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "DELETE", [1mpath[0m: "/api/admin/roles/1", [1mrequest_id[0m: "48662303-d301-4091-82f7-84c6d58b457f", [1mduration_ms[0m: 136, [1mstatus_code[0m: 200

  [2m2025-08-11T09:49:50.761418Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求成功完成, [1;32mmethod[0m[32m: DELETE, [1;32muri[0m[32m: /api/admin/roles/1, [1;32mstatus[0m[32m: 200 OK, [1;32mduration_ms[0m[32m: 136, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 48662303-d301-4091-82f7-84c6d58b457f[0m

  [2m2025-08-11T09:49:50.761442Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 644µs, [1;32mtime.idle[0m[32m: 136ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: DELETE, [1muri[0m: /api/admin/roles/1, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:50.764130Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /api/admin/roles, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 53cc75f8-2743-48ac-a95b-d128dcdf69dd[0m

  [2m2025-08-11T09:49:50.764182Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/admin/roles, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:50.943822Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 179.6415ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/admin/roles, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:49:50.943913Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/api/admin/roles", [1mrequest_id[0m: "53cc75f8-2743-48ac-a95b-d128dcdf69dd", [1mduration_ms[0m: 179, [1mstatus_code[0m: 200

  [2m2025-08-11T09:49:50.943931Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 16.5µs, [1;32mtime.idle[0m[32m: 3.42µs[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/api/admin/roles", [1mrequest_id[0m: "53cc75f8-2743-48ac-a95b-d128dcdf69dd", [1mduration_ms[0m: 179, [1mstatus_code[0m: 200

  [2m2025-08-11T09:49:50.943949Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求成功完成, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /api/admin/roles, [1;32mstatus[0m[32m: 200 OK, [1;32mduration_ms[0m[32m: 179, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 53cc75f8-2743-48ac-a95b-d128dcdf69dd[0m

  [2m2025-08-11T09:49:50.943981Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 714µs, [1;32mtime.idle[0m[32m: 179ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/admin/roles, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:50:57.410962Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: baf0d182-8e50-4f07-9f06-d3a1b9a19984[0m

  [2m2025-08-11T09:50:57.411280Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:50:57.411730Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 481.25µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:50:57.411772Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/", [1mrequest_id[0m: "baf0d182-8e50-4f07-9f06-d3a1b9a19984", [1mduration_ms[0m: 0, [1mstatus_code[0m: 304

  [2m2025-08-11T09:50:57.411785Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 12.4µs, [1;32mtime.idle[0m[32m: 1.42µs[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/", [1mrequest_id[0m: "baf0d182-8e50-4f07-9f06-d3a1b9a19984", [1mduration_ms[0m: 0, [1mstatus_code[0m: 304

  [2m2025-08-11T09:50:57.411803Z[0m [31mERROR[0m [1;31mpharmacy_system::api::middleware[0m[31m: [31mHTTP 請求伺服器錯誤, [1;31mmethod[0m[31m: GET, [1;31muri[0m[31m: /, [1;31mstatus[0m[31m: 304 Not Modified, [1;31mduration_ms[0m[31m: 0, [1;31muser_id[0m[31m: None, [1;31mrequest_id[0m[31m: baf0d182-8e50-4f07-9f06-d3a1b9a19984[0m

  [2m2025-08-11T09:50:57.411831Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 261µs, [1;32mtime.idle[0m[32m: 294µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:50:57.443393Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /api/products?page=1&limit=10, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: a7e3d492-e798-45c7-ae98-a344bb8059be[0m

  [2m2025-08-11T09:50:57.443541Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/products?page=1&limit=10, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:50:57.444528Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /api/auth/me, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: efcd48a9-fc69-47b9-b57f-990cee1721b0[0m

  [2m2025-08-11T09:50:57.444756Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/auth/me, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:50:57.444969Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /.well-known/appspecific/com.chrome.devtools.json, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: bf7e11da-e059-40c2-85ac-273fcb328b04[0m

  [2m2025-08-11T09:50:57.444996Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /.well-known/appspecific/com.chrome.devtools.json, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:50:57.445141Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 153.333µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /.well-known/appspecific/com.chrome.devtools.json, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:50:57.445209Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/.well-known/appspecific/com.chrome.devtools.json", [1mrequest_id[0m: "bf7e11da-e059-40c2-85ac-273fcb328b04", [1mduration_ms[0m: 0, [1mstatus_code[0m: 404

  [2m2025-08-11T09:50:57.445278Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 68.5µs, [1;32mtime.idle[0m[32m: 1.29µs[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/.well-known/appspecific/com.chrome.devtools.json", [1mrequest_id[0m: "bf7e11da-e059-40c2-85ac-273fcb328b04", [1mduration_ms[0m: 0, [1mstatus_code[0m: 404

  [2m2025-08-11T09:50:57.445294Z[0m [33m WARN[0m [1;33mpharmacy_system::api::middleware[0m[33m: [33mHTTP 請求客戶端錯誤, [1;33mmethod[0m[33m: GET, [1;33muri[0m[33m: /.well-known/appspecific/com.chrome.devtools.json, [1;33mstatus[0m[33m: 404 Not Found, [1;33mduration_ms[0m[33m: 0, [1;33muser_id[0m[33m: None, [1;33mrequest_id[0m[33m: bf7e11da-e059-40c2-85ac-273fcb328b04[0m

  [2m2025-08-11T09:50:57.445315Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 94.6µs, [1;32mtime.idle[0m[32m: 226µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /.well-known/appspecific/com.chrome.devtools.json, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:50:57.565419Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 121.963042ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/products?page=1&limit=10, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:50:57.565481Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/api/products", [1mrequest_id[0m: "a7e3d492-e798-45c7-ae98-a344bb8059be", [1mduration_ms[0m: 122, [1mstatus_code[0m: 200

  [2m2025-08-11T09:50:57.565490Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 8.38µs, [1;32mtime.idle[0m[32m: 1.04µs[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/api/products", [1mrequest_id[0m: "a7e3d492-e798-45c7-ae98-a344bb8059be", [1mduration_ms[0m: 122, [1mstatus_code[0m: 200

  [2m2025-08-11T09:50:57.565514Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求成功完成, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /api/products?page=1&limit=10, [1;32mstatus[0m[32m: 200 OK, [1;32mduration_ms[0m[32m: 122, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: a7e3d492-e798-45c7-ae98-a344bb8059be[0m

  [2m2025-08-11T09:50:57.565532Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 949µs, [1;32mtime.idle[0m[32m: 121ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/products?page=1&limit=10, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:50:57.706848Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 262.093791ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/auth/me, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:50:57.706909Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/api/auth/me", [1mrequest_id[0m: "efcd48a9-fc69-47b9-b57f-990cee1721b0", [1mduration_ms[0m: 262, [1mstatus_code[0m: 200

  [2m2025-08-11T09:50:57.706921Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 11.9µs, [1;32mtime.idle[0m[32m: 1.50µs[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/api/auth/me", [1mrequest_id[0m: "efcd48a9-fc69-47b9-b57f-990cee1721b0", [1mduration_ms[0m: 262, [1mstatus_code[0m: 200

  [2m2025-08-11T09:50:57.706933Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求成功完成, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /api/auth/me, [1;32mstatus[0m[32m: 200 OK, [1;32mduration_ms[0m[32m: 262, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: efcd48a9-fc69-47b9-b57f-990cee1721b0[0m

  [2m2025-08-11T09:50:57.706958Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 758µs, [1;32mtime.idle[0m[32m: 261ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /api/auth/me, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:51:15.421161Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 526d8757-37e8-48c1-997d-3af7a89b6f74[0m

  [2m2025-08-11T09:51:15.421427Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:51:15.421812Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 409.5µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:51:15.421843Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/", [1mrequest_id[0m: "526d8757-37e8-48c1-997d-3af7a89b6f74", [1mduration_ms[0m: 0, [1mstatus_code[0m: 200

  [2m2025-08-11T09:51:15.421855Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 10.3µs, [1;32mtime.idle[0m[32m: 1.54µs[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/", [1mrequest_id[0m: "526d8757-37e8-48c1-997d-3af7a89b6f74", [1mduration_ms[0m: 0, [1mstatus_code[0m: 200

  [2m2025-08-11T09:51:15.421883Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求成功完成, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /, [1;32mstatus[0m[32m: 200 OK, [1;32mduration_ms[0m[32m: 0, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 526d8757-37e8-48c1-997d-3af7a89b6f74[0m

  [2m2025-08-11T09:51:15.422564Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 683µs, [1;32mtime.idle[0m[32m: 456µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:51:15.478812Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /css/style.css?v=20250809-address, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 0637a166-c59c-45bc-876b-eb6d16924b40[0m

  [2m2025-08-11T09:51:15.478946Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /css/style.css?v=20250809-address, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:51:15.479419Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /images/logo.png, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 5d2c657c-1c15-430a-bab8-9fe7018e67c2[0m

  [2m2025-08-11T09:51:15.479451Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /images/logo.png, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:51:15.479729Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 794.625µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /css/style.css?v=20250809-address, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:51:15.479767Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/css/style.css", [1mrequest_id[0m: "0637a166-c59c-45bc-876b-eb6d16924b40", [1mduration_ms[0m: 0, [1mstatus_code[0m: 200

  [2m2025-08-11T09:51:15.479779Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 10.9µs, [1;32mtime.idle[0m[32m: 1.04µs[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/css/style.css", [1mrequest_id[0m: "0637a166-c59c-45bc-876b-eb6d16924b40", [1mduration_ms[0m: 0, [1mstatus_code[0m: 200

  [2m2025-08-11T09:51:15.479795Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求成功完成, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /css/style.css?v=20250809-address, [1;32mstatus[0m[32m: 200 OK, [1;32mduration_ms[0m[32m: 0, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 0637a166-c59c-45bc-876b-eb6d16924b40[0m

  [2m2025-08-11T09:51:15.479956Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /js/app.js?v=20250811-deleterole, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 3e7709da-5afc-4b84-9f7f-a85e8300a0b7[0m

  [2m2025-08-11T09:51:15.479976Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /js/app.js?v=20250811-deleterole, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:51:15.480169Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 203µs, [1;32mtime.idle[0m[32m: 1.02ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /css/style.css?v=20250809-address, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:51:15.480364Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 392.083µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /js/app.js?v=20250811-deleterole, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:51:15.480394Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/js/app.js", [1mrequest_id[0m: "3e7709da-5afc-4b84-9f7f-a85e8300a0b7", [1mduration_ms[0m: 0, [1mstatus_code[0m: 200

  [2m2025-08-11T09:51:15.480402Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 7.54µs, [1;32mtime.idle[0m[32m: 624ns[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/js/app.js", [1mrequest_id[0m: "3e7709da-5afc-4b84-9f7f-a85e8300a0b7", [1mduration_ms[0m: 0, [1mstatus_code[0m: 200

  [2m2025-08-11T09:51:15.480411Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求成功完成, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /js/app.js?v=20250811-deleterole, [1;32mstatus[0m[32m: 200 OK, [1;32mduration_ms[0m[32m: 0, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 3e7709da-5afc-4b84-9f7f-a85e8300a0b7[0m

  [2m2025-08-11T09:51:15.480953Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 126µs, [1;32mtime.idle[0m[32m: 851µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /js/app.js?v=20250811-deleterole, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:51:15.482830Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 3.384125ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /images/logo.png, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:51:15.482860Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/images/logo.png", [1mrequest_id[0m: "5d2c657c-1c15-430a-bab8-9fe7018e67c2", [1mduration_ms[0m: 3, [1mstatus_code[0m: 200

  [2m2025-08-11T09:51:15.482866Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 5.54µs, [1;32mtime.idle[0m[32m: 625ns[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/images/logo.png", [1mrequest_id[0m: "5d2c657c-1c15-430a-bab8-9fe7018e67c2", [1mduration_ms[0m: 3, [1mstatus_code[0m: 200

  [2m2025-08-11T09:51:15.482882Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求成功完成, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /images/logo.png, [1;32mstatus[0m[32m: 200 OK, [1;32mduration_ms[0m[32m: 3, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 5d2c657c-1c15-430a-bab8-9fe7018e67c2[0m

  [2m2025-08-11T09:51:15.486012Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 3.48ms, [1;32mtime.idle[0m[32m: 3.08ms[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /images/logo.png, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:51:15.522784Z[0m [32m INFO[0m [1;32mpharmacy_system::api::middleware[0m[32m: [32mHTTP 請求開始, [1;32mmethod[0m[32m: GET, [1;32muri[0m[32m: /favicon.ico, [1;32muser_agent[0m[32m: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, [1;32mclient_ip[0m[32m: unknown, [1;32muser_id[0m[32m: None, [1;32mrequest_id[0m[32m: 9c4b10e3-5012-4e7b-9eb3-04fd13e8fe24[0m

  [2m2025-08-11T09:51:15.522818Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mstarted processing request[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /favicon.ico, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:51:15.522909Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mfinished processing request in 96.625µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /favicon.ico, [1mversion[0m: HTTP/1.1

  [2m2025-08-11T09:51:15.522922Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mAPI 請求記錄[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/favicon.ico", [1mrequest_id[0m: "9c4b10e3-5012-4e7b-9eb3-04fd13e8fe24", [1mduration_ms[0m: 0, [1mstatus_code[0m: 404

  [2m2025-08-11T09:51:15.522926Z[0m [32m INFO[0m [1;32mpharmacy_system::logging::structured_logging[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 3.54µs, [1;32mtime.idle[0m[32m: 375ns[0m
    [2;3min[0m pharmacy_system::logging::structured_logging::[1mapi_request[0m [2;3mwith[0m [1mmethod[0m: "GET", [1mpath[0m: "/favicon.ico", [1mrequest_id[0m: "9c4b10e3-5012-4e7b-9eb3-04fd13e8fe24", [1mduration_ms[0m: 0, [1mstatus_code[0m: 404

  [2m2025-08-11T09:51:15.522932Z[0m [33m WARN[0m [1;33mpharmacy_system::api::middleware[0m[33m: [33mHTTP 請求客戶端錯誤, [1;33mmethod[0m[33m: GET, [1;33muri[0m[33m: /favicon.ico, [1;33mstatus[0m[33m: 404 Not Found, [1;33mduration_ms[0m[33m: 0, [1;33muser_id[0m[33m: None, [1;33mrequest_id[0m[33m: 9c4b10e3-5012-4e7b-9eb3-04fd13e8fe24[0m

  [2m2025-08-11T09:51:15.522943Z[0m [32m INFO[0m [1;32mpharmacy_system::api[0m[32m: [32mclose, [1;32mtime.busy[0m[32m: 64.6µs, [1;32mtime.idle[0m[32m: 62.0µs[0m
    [2;3min[0m pharmacy_system::api::[1mhttp_request[0m [2;3mwith[0m [1mmethod[0m: GET, [1muri[0m: /favicon.ico, [1mversion[0m: HTTP/1.1

