-- 安全創建 favorites 表的腳本
-- 包含詳細的錯誤處理和檢查

-- 首先檢查連線
SELECT 'Starting favorites table creation...' as message;

-- 檢查當前用戶權限
SELECT current_user as current_database_user, current_database() as current_database;

-- 列出現有表
SELECT 'Current tables:' as info;
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';

-- 開始事務
BEGIN;

-- 檢查 favorites 表是否已存在
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'favorites') THEN
        RAISE NOTICE 'favorites table already exists, skipping creation';
    ELSE
        RAISE NOTICE 'Creating favorites table...';
        
        -- 創建 favorites 表
        CREATE TABLE favorites (
            id BIGSERIAL PRIMARY KEY,
            user_id BIGINT NOT NULL,
            product_id BIGINT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        RAISE NOTICE 'favorites table created successfully';
        
        -- 添加唯一約束
        ALTER TABLE favorites ADD CONSTRAINT unique_user_product UNIQUE(user_id, product_id);
        RAISE NOTICE 'Unique constraint added';
        
        -- 創建索引
        CREATE INDEX idx_favorites_user_id ON favorites(user_id);
        CREATE INDEX idx_favorites_product_id ON favorites(product_id);
        CREATE INDEX idx_favorites_created_at ON favorites(created_at);
        RAISE NOTICE 'Indexes created';
        
        -- 添加註釋
        COMMENT ON TABLE favorites IS '用戶最愛產品表';
        COMMENT ON COLUMN favorites.id IS '主鍵ID';
        COMMENT ON COLUMN favorites.user_id IS '用戶ID';
        COMMENT ON COLUMN favorites.product_id IS '產品ID';
        COMMENT ON COLUMN favorites.created_at IS '收藏時間';
        RAISE NOTICE 'Comments added';
    END IF;
END
$$;

-- 提交事務
COMMIT;

-- 驗證創建結果
SELECT 'Verification:' as info;
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'favorites';

-- 檢查表結構
SELECT 'Table structure:' as info;
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'favorites'
ORDER BY ordinal_position;

-- 檢查索引
SELECT 'Indexes:' as info;
SELECT indexname FROM pg_indexes WHERE tablename = 'favorites' AND schemaname = 'public';

SELECT 'favorites table setup completed successfully!' as final_message;