version: '3.8'

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: pharmacy_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d pharmacy_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  pharmacy-system:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=**************************************/pharmacy_db
      - JWT_SECRET=development-jwt-secret-key
      - PORT=8080
      - SMTP_HOST=smtp.gmail.com
      - SMTP_PORT=587
      - SMTP_USERNAME=<EMAIL>
      - SMTP_PASSWORD=your-app-password
      - FROM_EMAIL=<EMAIL>
      - LINE_CHANNEL_ACCESS_TOKEN=your-line-channel-access-token
      - LINE_CHANNEL_SECRET=your-line-channel-secret
      - GCP_PROJECT_ID=your-gcp-project-id
      - GCP_STORAGE_BUCKET=your-backup-bucket-name
    volumes:
      - ./data:/app/data
    depends_on:
      db:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  postgres_data: 