#!/usr/bin/env python3
# Python 腳本來創建 favorites 表
import os
import psycopg2
from dotenv import load_dotenv

def create_favorites_table():
    # 載入環境變數
    load_dotenv()
    database_url = os.getenv('DATABASE_URL')
    
    if not database_url:
        print("❌ DATABASE_URL 未設置")
        return
    
    try:
        print("🔌 連接到資料庫...")
        conn = psycopg2.connect(database_url)
        cur = conn.cursor()
        print("✅ 資料庫連接成功")
        
        # 檢查現有表
        print("📋 檢查現有表...")
        cur.execute("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name
        """)
        tables = [row[0] for row in cur.fetchall()]
        print(f"現有表: {tables}")
        
        # 檢查 favorites 表是否存在
        cur.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'favorites'
            )
        """)
        
        if cur.fetchone()[0]:
            print("ℹ️  favorites 表已存在")
            return
        
        print("🔨 創建 favorites 表...")
        
        # 創建表
        cur.execute("""
            CREATE TABLE favorites (
                id BIGSERIAL PRIMARY KEY,
                user_id BIGINT NOT NULL,
                product_id BIGINT NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                UNIQUE(user_id, product_id)
            )
        """)
        print("✅ favorites 表創建成功")
        
        # 創建索引
        print("🔍 創建索引...")
        cur.execute("CREATE INDEX idx_favorites_user_id ON favorites(user_id)")
        cur.execute("CREATE INDEX idx_favorites_product_id ON favorites(product_id)")
        cur.execute("CREATE INDEX idx_favorites_created_at ON favorites(created_at)")
        print("✅ 索引創建成功")
        
        # 添加註釋
        print("📝 添加註釋...")
        cur.execute("COMMENT ON TABLE favorites IS '用戶最愛產品表'")
        cur.execute("COMMENT ON COLUMN favorites.id IS '主鍵ID'")
        cur.execute("COMMENT ON COLUMN favorites.user_id IS '用戶ID'")
        cur.execute("COMMENT ON COLUMN favorites.product_id IS '產品ID'")
        cur.execute("COMMENT ON COLUMN favorites.created_at IS '收藏時間'")
        print("✅ 註釋添加成功")
        
        # 提交更改
        conn.commit()
        
        # 驗證
        print("🔍 驗證表創建...")
        cur.execute("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_schema = 'public' AND table_name = 'favorites'
            ORDER BY ordinal_position
        """)
        columns = cur.fetchall()
        print("表結構:")
        for col_name, col_type in columns:
            print(f"  - {col_name}: {col_type}")
        
        print("🎉 favorites 表創建完成！")
        
    except Exception as error:
        print(f"❌ 錯誤: {error}")
        if conn:
            conn.rollback()
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()
        print("🔌 資料庫連接已關閉")

if __name__ == "__main__":
    create_favorites_table()