#!/bin/bash

echo "🚀 創建 favorites 表..."

# 檢查環境變數
source .env

# 使用psql直接執行SQL
psql "$DATABASE_URL" << 'EOF'
-- 檢查表是否存在
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'favorites') THEN
        -- 創建表
        CREATE TABLE favorites (
            id BIGSERIAL PRIMARY KEY,
            user_id BIGINT NOT NULL,
            product_id BIGINT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(user_id, product_id)
        );
        
        -- 創建索引
        CREATE INDEX idx_favorites_user_id ON favorites(user_id);
        CREATE INDEX idx_favorites_product_id ON favorites(product_id);
        CREATE INDEX idx_favorites_created_at ON favorites(created_at);
        
        -- 添加註釋
        COMMENT ON TABLE favorites IS '用戶最愛產品表';
        COMMENT ON COLUMN favorites.id IS '主鍵ID';
        COMMENT ON COLUMN favorites.user_id IS '用戶ID';
        COMMENT ON COLUMN favorites.product_id IS '產品ID';
        COMMENT ON COLUMN favorites.created_at IS '收藏時間';
        
        RAISE NOTICE '✅ favorites 表創建成功';
    ELSE
        RAISE NOTICE 'ℹ️ favorites 表已存在';
    END IF;
END
$$;

-- 顯示表結構
\d favorites

-- 顯示索引
\di favorites*

EOF

echo "🎉 遷移完成！"