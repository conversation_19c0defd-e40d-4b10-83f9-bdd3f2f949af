#!/bin/bash

echo "🧹 清理不需要的 Python 檔案"
echo "================================"

# 創建備份目錄
BACKUP_DIR="python_files_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📦 創建備份目錄: $BACKUP_DIR"

# 定義要刪除的檔案類別
declare -a MIGRATION_FILES=(
    "migrate_to_neon.py"
    "simple_neon_migrate.py" 
    "fixed_migrate_to_neon.py"
    "run_nhi_migration.py"
    "execute_migration.py"
    "test_migration_success.py"
    "final_success_test.py"
    "verify_migration.py"
)

declare -a DATABASE_CHECK_FILES=(
    "check_database_structure.py"
    "check_date_columns.py"
    "check_import_count.py"
    "check_merge.py"
    "check_nhi_relationship.py"
    "test_nhi_code_structure.py"
)

declare -a EXCEL_PROCESSING_FILES=(
    "process_excel.py"
    "process_1_2.py"
    "process_2.py"
    "process_2_fixed.py"
    "merge_files.py"
)

declare -a NHI_IMPORT_FILES=(
    "import_nhi_prices.py"
    "import_nhi_prices_full.py"
    "import_nhi_prices_test.py"
    "import_nhi_prices (與 userdeMac-mini-3.local 衝突的複本 2025-08-11).py"
    "continue_import.py"
    "import_store.py"
)

declare -a DATABASE_FIX_FILES=(
    "fix_nhi_code.py"
    "fix_nhi_code_v2.py"
    "fix_nhi_code_v3.py"
    "complete_reset.py"
)

declare -a TEST_FILES=(
    "test_admin_orders.py"
    "test_date_api.py"
    "test_date_format.py"
    "test_filter_fix.py"
    "test_filter_improvements.py"
    "test_fixes.py"
    "test_new_features.py"
    "test_nhi_prices.py"
    "test_price_changes.py"
    "test_price_sync.py"
    "test_stats_fix.py"
    "simple_test.py"
)

# 函數：備份並刪除檔案
backup_and_remove() {
    local file="$1"
    local category="$2"
    
    if [ -f "$file" ]; then
        echo "  📄 備份並刪除: $file"
        cp "$file" "$BACKUP_DIR/"
        rm "$file"
        return 0
    else
        echo "  ⚠️  檔案不存在: $file"
        return 1
    fi
}

# 清理各類檔案
echo ""
echo "🔄 清理遷移相關檔案..."
removed_count=0
for file in "${MIGRATION_FILES[@]}"; do
    if backup_and_remove "$file" "migration"; then
        ((removed_count++))
    fi
done
echo "   清理了 $removed_count 個遷移檔案"

echo ""
echo "🔍 清理資料庫檢查檔案..."
removed_count=0
for file in "${DATABASE_CHECK_FILES[@]}"; do
    if backup_and_remove "$file" "database_check"; then
        ((removed_count++))
    fi
done
echo "   清理了 $removed_count 個資料庫檢查檔案"

echo ""
echo "📊 清理 Excel 處理檔案..."
removed_count=0
for file in "${EXCEL_PROCESSING_FILES[@]}"; do
    if backup_and_remove "$file" "excel_processing"; then
        ((removed_count++))
    fi
done
echo "   清理了 $removed_count 個 Excel 處理檔案"

echo ""
echo "💊 清理健保資料匯入檔案..."
removed_count=0
for file in "${NHI_IMPORT_FILES[@]}"; do
    if backup_and_remove "$file" "nhi_import"; then
        ((removed_count++))
    fi
done
echo "   清理了 $removed_count 個健保資料匯入檔案"

echo ""
echo "🔧 清理資料庫修復檔案..."
removed_count=0
for file in "${DATABASE_FIX_FILES[@]}"; do
    if backup_and_remove "$file" "database_fix"; then
        ((removed_count++))
    fi
done
echo "   清理了 $removed_count 個資料庫修復檔案"

echo ""
echo "🧪 清理測試檔案..."
removed_count=0
for file in "${TEST_FILES[@]}"; do
    if backup_and_remove "$file" "test"; then
        ((removed_count++))
    fi
done
echo "   清理了 $removed_count 個測試檔案"

# 清理其他臨時檔案
echo ""
echo "🗑️  清理其他臨時檔案..."

# Excel 檔案
declare -a EXCEL_FILES=(
    "1_2.xlsx"
    "1_3.xlsx"
    "1.xlsx"
    "2_3.xlsx"
    "2_processed.xlsx"
    "2.xlsx"
    "4.xlsx"
    "nhi_filtered_correct.xlsx"
    "nhi_filtered.xlsx"
    "nhi_final_filtered.xlsx"
    "nhi.xlsx"
    "sotre.xlsx"
)

for file in "${EXCEL_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  📊 備份並刪除 Excel: $file"
        cp "$file" "$BACKUP_DIR/"
        rm "$file"
    fi
done

# 日誌檔案
declare -a LOG_FILES=(
    "app.log"
    "pharmacy.log"
    "server.log"
    "system.log"
    "migration.log"
)

for file in "${LOG_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  📝 備份並刪除日誌: $file"
        cp "$file" "$BACKUP_DIR/"
        rm "$file"
    fi
done

# JSON 備份檔案
declare -a JSON_BACKUP_FILES=(
    "sqlite_backup_20250806_093337.json"
    "sqlite_backup_20250806_093717.json"
    "migration_data.json"
)

for file in "${JSON_BACKUP_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  💾 備份並刪除 JSON: $file"
        cp "$file" "$BACKUP_DIR/"
        rm "$file"
    fi
done

# 臨時 HTML 檔案
declare -a TEMP_HTML_FILES=(
    "debug_admin_orders.html"
    "debug_admin.html"
    "test_registration.html"
    "tmp_rovodev_test_f5_fix.html"
    "tmp_rovodev_test_permissions.html"
)

for file in "${TEMP_HTML_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  🌐 備份並刪除臨時 HTML: $file"
        cp "$file" "$BACKUP_DIR/"
        rm "$file"
    fi
done

# web 目錄下的測試檔案
declare -a WEB_TEST_FILES=(
    "web/debug_admin_tab.html"
    "web/debug_auth.html"
    "web/debug_permissions.html"
    "web/direct_debug.html"
    "web/fix_admin.html"
    "web/test_messages.html"
    "web/test.html"
)

for file in "${WEB_TEST_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  🌐 備份並刪除 Web 測試檔案: $file"
        cp "$file" "$BACKUP_DIR/"
        rm "$file"
    fi
done

echo ""
echo "✅ 清理完成！"
echo ""
echo "📊 清理總結："
echo "   - 所有檔案已備份到: $BACKUP_DIR"
echo "   - 如果需要恢復，可以從備份目錄複製"
echo ""
echo "🗂️  保留的重要檔案："
echo "   - Cargo.toml, Cargo.lock (Rust 專案檔案)"
echo "   - src/ (原始碼)"
echo "   - web/ (前端檔案)"
echo "   - migrations/ (資料庫遷移)"
echo "   - .env* (環境設定)"
echo "   - README.md, DEPLOYMENT.md (文件)"
echo ""
echo "⚠️  如果確認不需要備份檔案，可以執行："
echo "   rm -rf $BACKUP_DIR"