# 資料庫配置 - 使用雲端 Neon 資料庫
DATABASE_URL=postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-here

# 伺服器配置
PORT=8080

# Email 配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>

# Line Bot 配置
LINE_CHANNEL_ACCESS_TOKEN=your-line-channel-access-token
LINE_CHANNEL_SECRET=your-line-channel-secret

# GCP 配置
GCP_PROJECT_ID=your-gcp-project-id
GCP_STORAGE_BUCKET=your-backup-bucket-name
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json 