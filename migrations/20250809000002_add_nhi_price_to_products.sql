-- 為 products 表添加 nhi_price 欄位，並建立完整的價格同步機制

-- 步驟 1: 為 products 表添加 nhi_price 欄位
ALTER TABLE products ADD COLUMN nhi_price DECIMAL(10,2);

-- 步驟 2: 初始同步 - 將 nhi_prices.nhi_price 同步到 products.nhi_price
UPDATE products 
SET nhi_price = np.nhi_price
FROM nhi_prices np 
WHERE products.nhi_code = np.nhi_code;

-- 步驟 3: 更新同步函數，同時同步 nhi_price 和 selling_price
CREATE OR REPLACE FUNCTION sync_product_prices()
RETURNS TRIGGER AS $$
BEGIN
    -- 當 nhi_prices 表更新時，同步更新相關的 products
    IF TG_OP = 'UPDATE' THEN
        UPDATE products 
        SET nhi_price = NEW.nhi_price,
            selling_price = NEW.selling_price,
            updated_at = CURRENT_TIMESTAMP
        WHERE nhi_code = NEW.nhi_code;
    END IF;
    
    -- 當新增 nhi_prices 記錄時，同步更新相關的 products
    IF TG_OP = 'INSERT' THEN
        UPDATE products 
        SET nhi_price = NEW.nhi_price,
            selling_price = NEW.selling_price,
            updated_at = CURRENT_TIMESTAMP
        WHERE nhi_code = NEW.nhi_code;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 步驟 4: 更新反向同步函數
CREATE OR REPLACE FUNCTION sync_nhi_prices()
RETURNS TRIGGER AS $$
BEGIN
    -- 當 products 表的價格更新時，同步更新 nhi_prices
    IF TG_OP = 'UPDATE' AND (
        (OLD.nhi_price IS DISTINCT FROM NEW.nhi_price) OR 
        (OLD.selling_price IS DISTINCT FROM NEW.selling_price)
    ) THEN
        UPDATE nhi_prices 
        SET nhi_price = NEW.nhi_price,
            selling_price = NEW.selling_price,
            updated_at = CURRENT_TIMESTAMP
        WHERE nhi_code = NEW.nhi_code;
        
        -- 如果 nhi_prices 中沒有對應記錄，則創建一個
        IF NOT FOUND THEN
            INSERT INTO nhi_prices (nhi_code, nhi_price, selling_price)
            VALUES (NEW.nhi_code, NEW.nhi_price, NEW.selling_price)
            ON CONFLICT (nhi_code) DO UPDATE SET
                nhi_price = NEW.nhi_price,
                selling_price = NEW.selling_price,
                updated_at = CURRENT_TIMESTAMP;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 步驟 5: 添加索引
CREATE INDEX IF NOT EXISTS idx_products_nhi_price ON products(nhi_price);

-- 步驟 6: 添加註釋
COMMENT ON COLUMN products.nhi_price IS '健保給付價格，與 nhi_prices.nhi_price 同步';
COMMENT ON COLUMN products.selling_price IS '銷售價格，與 nhi_prices.selling_price 同步';