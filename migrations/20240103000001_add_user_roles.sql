-- 添加用戶角色系統

-- 創建角色表格
CREATE TABLE IF NOT EXISTS roles (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 創建權限表格
CREATE TABLE IF NOT EXISTS permissions (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    resource VARCHAR(50) NOT NULL, -- 資源類型，如 'products', 'orders', 'users' 等
    action VARCHAR(50) NOT NULL,   -- 操作類型，如 'create', 'read', 'update', 'delete'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 創建角色權限關聯表格
CREATE TABLE IF NOT EXISTS role_permissions (
    id BIGSERIAL PRIMARY KEY,
    role_id BIGINT NOT NULL,
    permission_id BIGINT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    UNIQUE(role_id, permission_id)
);

-- 為 users 表格添加 role_id 欄位（如果不存在）
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'role_id') THEN
        ALTER TABLE users ADD COLUMN role_id BIGINT;
    END IF;
END $$;

-- 插入預設角色（如果不存在）
INSERT INTO roles (name, description) 
SELECT 'super_admin', '超級管理員 - 擁有所有權限'
WHERE NOT EXISTS (SELECT 1 FROM roles WHERE name = 'super_admin');

INSERT INTO roles (name, description) 
SELECT 'admin', '管理員 - 可以管理產品和訂單'
WHERE NOT EXISTS (SELECT 1 FROM roles WHERE name = 'admin');

INSERT INTO roles (name, description) 
SELECT 'pharmacy', '藥局用戶 - 可以下訂單和查看自己的資料'
WHERE NOT EXISTS (SELECT 1 FROM roles WHERE name = 'pharmacy');

INSERT INTO roles (name, description) 
SELECT 'viewer', '檢視者 - 只能查看資料'
WHERE NOT EXISTS (SELECT 1 FROM roles WHERE name = 'viewer');

-- 插入基本權限
INSERT INTO permissions (name, description, resource, action) VALUES 
    -- 產品管理權限
    ('products.create', '創建產品', 'products', 'create'),
    ('products.read', '查看產品', 'products', 'read'),
    ('products.update', '更新產品', 'products', 'update'),
    ('products.delete', '刪除產品', 'products', 'delete'),
    
    -- 訂單管理權限
    ('orders.create', '創建訂單', 'orders', 'create'),
    ('orders.read', '查看訂單', 'orders', 'read'),
    ('orders.update', '更新訂單', 'orders', 'update'),
    ('orders.delete', '刪除訂單', 'orders', 'delete'),
    ('orders.read_all', '查看所有用戶訂單', 'orders', 'read_all'),
    
    -- 用戶管理權限
    ('users.create', '創建用戶', 'users', 'create'),
    ('users.read', '查看用戶', 'users', 'read'),
    ('users.update', '更新用戶', 'users', 'update'),
    ('users.delete', '刪除用戶', 'users', 'delete'),
    ('users.read_all', '查看所有用戶', 'users', 'read_all'),
    
    -- 系統管理權限
    ('system.backup', '系統備份', 'system', 'backup'),
    ('system.settings', '系統設定', 'system', 'settings'),
    ('system.logs', '查看系統日誌', 'system', 'logs'),
    
    -- 角色和權限管理
    ('roles.create', '創建角色', 'roles', 'create'),
    ('roles.read', '查看角色', 'roles', 'read'),
    ('roles.update', '更新角色', 'roles', 'update'),
    ('roles.delete', '刪除角色', 'roles', 'delete'),
    
    -- 通知權限
    ('notifications.read', '查看通知', 'notifications', 'read'),
    ('notifications.send', '發送通知', 'notifications', 'send')
ON CONFLICT (name) DO NOTHING;

-- 設置角色權限關聯

-- 超級管理員 - 擁有所有權限
INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p WHERE r.name = 'super_admin'
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 管理員 - 產品、訂單、部分用戶管理權限
INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'admin' AND p.name IN (
    'products.create', 'products.read', 'products.update', 'products.delete',
    'orders.create', 'orders.read', 'orders.update', 'orders.delete', 'orders.read_all',
    'users.read', 'users.read_all',
    'notifications.read', 'notifications.send'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 藥局用戶 - 基本訂單和產品查看權限
INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'pharmacy' AND p.name IN (
    'products.read',
    'orders.create', 'orders.read', 'orders.update',
    'notifications.read'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 檢視者 - 只能查看
INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'viewer' AND p.name IN (
    'products.read',
    'orders.read',
    'notifications.read'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 為現有用戶設置預設角色（pharmacy）
UPDATE users SET role_id = (SELECT id FROM roles WHERE name = 'pharmacy') WHERE role_id IS NULL;

-- 添加外鍵約束（如果不存在）
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'fk_users_role') THEN
        ALTER TABLE users ADD CONSTRAINT fk_users_role FOREIGN KEY (role_id) REFERENCES roles(id);
    END IF;
END $$;

-- 建立索引
CREATE INDEX IF NOT EXISTS idx_users_role_id ON users(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id ON role_permissions(permission_id);
CREATE INDEX IF NOT EXISTS idx_permissions_resource_action ON permissions(resource, action);