-- 新增用戶表格欄位以支援更完整的註冊資訊
-- 新增日期: 2025-08-09

-- 新增聯絡人欄位
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'contact_person') THEN
        ALTER TABLE users ADD COLUMN contact_person VARCHAR(100);
    END IF;
END $$;

-- 新增手機號碼欄位
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'mobile') THEN
        ALTER TABLE users ADD COLUMN mobile VARCHAR(20);
    END IF;
END $$;

-- 新增機構代號欄位
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'institution_code') THEN
        ALTER TABLE users ADD COLUMN institution_code VARCHAR(50);
    END IF;
END $$;

-- 新增聯絡地址欄位
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'address') THEN
        ALTER TABLE users ADD COLUMN address TEXT;
    END IF;
END $$;

-- 建立索引以提升查詢效能
CREATE INDEX IF NOT EXISTS idx_users_institution_code ON users(institution_code);
CREATE INDEX IF NOT EXISTS idx_users_mobile ON users(mobile);

-- 新增註解
COMMENT ON COLUMN users.contact_person IS '聯絡人姓名';
COMMENT ON COLUMN users.mobile IS '手機號碼';  
COMMENT ON COLUMN users.institution_code IS '機構代號';
COMMENT ON COLUMN users.address IS '聯絡地址';