-- 修改 nhi_prices 表以支援健保價格異動機制

-- 備份現有資料
CREATE TABLE nhi_prices_backup AS SELECT * FROM nhi_prices;

-- 刪除原有表格
DROP TABLE IF EXISTS nhi_prices CASCADE;

-- 建立新的 nhi_prices 表結構
CREATE TABLE nhi_prices (
    id SERIAL PRIMARY KEY,
    nhi_code VARCHAR(20) NOT NULL,            -- 健保代碼 (對應 products.nhi_code)
    old_nhi_price DECIMAL(10,2) NOT NULL,     -- 舊健保價格
    new_nhi_price DECIMAL(10,2) NOT NULL,     -- 新健保價格  
    effective_date DATE NOT NULL,             -- 生效日期
    selling_price DECIMAL(10,2) NOT NULL,     -- 銷售價格
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_nhi_code_date UNIQUE(nhi_code, effective_date)
);

-- 建立索引
CREATE INDEX idx_nhi_code_effective ON nhi_prices (nhi_code, effective_date DESC);
CREATE INDEX idx_effective_date ON nhi_prices (effective_date);

-- 從備份資料遷移到新結構 (假設舊資料都是當前價格，沒有異動)
INSERT INTO nhi_prices (nhi_code, old_nhi_price, new_nhi_price, effective_date, selling_price)
SELECT 
    'TEMP_' || row_number() OVER() as nhi_code,  -- 暫時的 nhi_code，稍後需要手動更新
    nhi_price as old_nhi_price,
    nhi_price as new_nhi_price,
    CURRENT_DATE as effective_date,
    selling_price
FROM nhi_prices_backup;

-- 更新 products 表的外鍵關係 (移除舊的外鍵約束)
ALTER TABLE products DROP CONSTRAINT IF EXISTS fk_products_nhi_price;

-- 注意: 需要手動將 products.nhi_code 與新的 nhi_prices.nhi_code 建立關聯
-- 這部分需要根據實際的業務邏輯來處理

-- 刪除備份表 (可選，建議先保留一段時間)
-- DROP TABLE nhi_prices_backup;