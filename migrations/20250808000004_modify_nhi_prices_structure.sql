-- 修改 nhi_prices 表結構：移除 old_nhi_price 和 new_nhi_price，新增 nhi_price

-- 新增 nhi_price 欄位
ALTER TABLE nhi_prices 
ADD COLUMN nhi_price DECIMAL(10,2);

-- 如果有資料，可以將 new_nhi_price 的值複製到 nhi_price
UPDATE nhi_prices 
SET nhi_price = new_nhi_price 
WHERE new_nhi_price IS NOT NULL;

-- 如果沒有 new_nhi_price，使用 old_nhi_price
UPDATE nhi_prices 
SET nhi_price = old_nhi_price 
WHERE nhi_price IS NULL AND old_nhi_price IS NOT NULL;

-- 設置 nhi_price 為 NOT NULL
ALTER TABLE nhi_prices 
ALTER COLUMN nhi_price SET NOT NULL;

-- 移除舊欄位
ALTER TABLE nhi_prices 
DROP COLUMN old_nhi_price;

ALTER TABLE nhi_prices 
DROP COLUMN new_nhi_price;