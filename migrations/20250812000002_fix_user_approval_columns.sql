-- 修復用戶審核系統欄位
-- 逐個添加欄位以確保 PostgreSQL 兼容性

-- 添加 status 欄位
ALTER TABLE users ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'approved';

-- 添加其他審核相關欄位
ALTER TABLE users ADD COLUMN IF NOT EXISTS approved_at TIMESTAMP;
ALTER TABLE users ADD COLUMN IF NOT EXISTS approved_by INTEGER;
ALTER TABLE users ADD COLUMN IF NOT EXISTS rejection_reason TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- 更新現有用戶的 submitted_at 為 created_at
UPDATE users 
SET submitted_at = created_at 
WHERE submitted_at IS NULL;

-- 創建索引
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_submitted_at ON users(submitted_at);

-- 創建審核日誌表（如果不存在）
CREATE TABLE IF NOT EXISTS user_approval_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    action VARCHAR(20) NOT NULL,
    reason TEXT,
    performed_by INTEGER NOT NULL,
    performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    old_status VARCHAR(20),
    new_status VARCHAR(20)
);

-- 為審核日誌創建索引
CREATE INDEX IF NOT EXISTS idx_approval_logs_user_id ON user_approval_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_approval_logs_performed_at ON user_approval_logs(performed_at);

-- 創建一些測試用的待審核用戶
INSERT INTO users (username, email, password_hash, pharmacy_name, status, submitted_at) VALUES 
    ('pending_user1', '<EMAIL>', '$2b$12$dummy_hash_for_testing', '待審核藥局1', 'pending', CURRENT_TIMESTAMP),
    ('pending_user2', '<EMAIL>', '$2b$12$dummy_hash_for_testing', '待審核藥局2', 'pending', CURRENT_TIMESTAMP),
    ('pending_user3', '<EMAIL>', '$2b$12$dummy_hash_for_testing', '待審核藥局3', 'pending', CURRENT_TIMESTAMP)
ON CONFLICT (username) DO NOTHING;