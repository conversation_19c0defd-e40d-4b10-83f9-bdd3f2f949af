-- 為訊息表格添加額外欄位
ALTER TABLE messages 
ADD COLUMN target_audience VARCHAR(50) DEFAULT 'all',
ADD COLUMN priority INTEGER DEFAULT 1,
ADD COLUMN starts_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN ends_at TIMESTAMP WITH TIME ZONE;

-- 建立索引
CREATE INDEX idx_messages_target_audience ON messages(target_audience);
CREATE INDEX idx_messages_priority ON messages(priority);
CREATE INDEX idx_messages_starts_at ON messages(starts_at);
CREATE INDEX idx_messages_ends_at ON messages(ends_at);
