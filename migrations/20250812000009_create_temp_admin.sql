-- 創建一個臨時的管理員帳號用於測試

-- 確保有 admin 角色
INSERT INTO roles (name, description) VALUES 
    ('admin', '系統管理員')
ON CONFLICT (name) DO NOTHING;

-- 創建臨時 admin 帳號（密碼是 "admin123"）
INSERT INTO users (
    username, 
    email, 
    password_hash, 
    role_id, 
    created_at, 
    updated_at, 
    is_active,
    pharmacy_name
) 
SELECT 
    'testadmin',
    '<EMAIL>',
    '$2b$12$LQv3c1yqBwuvHpS7TbuOCOFCXDaa8rjDM.6zSaOOgAgF6aLK6nEUq',  -- admin123
    r.id,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    true,
    'Test Admin Pharmacy'
FROM roles r 
WHERE r.name = 'admin'
ON CONFLICT (username) DO UPDATE SET
    password_hash = EXCLUDED.password_hash,
    role_id = EXCLUDED.role_id,
    is_active = true;

-- 確保權限存在並分配給 admin 角色
INSERT INTO permissions (name, description, resource, action) VALUES 
    ('orders.read_all', '查看所有訂單', 'orders', 'read_all')
ON CONFLICT (name) DO NOTHING;

INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id 
FROM roles r, permissions p 
WHERE r.name = 'admin' 
AND p.name = 'orders.read_all'
ON CONFLICT (role_id, permission_id) DO NOTHING;