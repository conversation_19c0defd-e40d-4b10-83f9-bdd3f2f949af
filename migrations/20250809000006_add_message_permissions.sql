-- 添加訊息管理權限

-- 插入訊息相關權限
INSERT INTO permissions (name, description, resource, action) VALUES 
    ('messages.create', '創建訊息', 'messages', 'create'),
    ('messages.read', '查看訊息', 'messages', 'read'),
    ('messages.update', '更新訊息', 'messages', 'update'),
    ('messages.delete', '刪除訊息', 'messages', 'delete'),
    ('messages.read_all', '查看所有訊息', 'messages', 'read_all')
ON CONFLICT (name) DO NOTHING;

-- 為超級管理員添加所有訊息權限
INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'super_admin' AND p.resource = 'messages'
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 為管理員添加訊息管理權限
INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'admin' AND p.name IN (
    'messages.create', 'messages.read', 'messages.update', 'messages.delete', 'messages.read_all'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 為藥局用戶添加訊息查看權限
INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'pharmacy' AND p.name IN (
    'messages.read'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 為檢視者添加訊息查看權限
INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'viewer' AND p.name IN (
    'messages.read'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;