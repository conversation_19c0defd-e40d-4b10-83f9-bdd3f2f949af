-- 暫時禁用外鍵約束來解決 migration 問題
-- 這是一個臨時解決方案，讓現有的 migration 能夠執行

-- 1. 暫時禁用外鍵約束檢查（PostgreSQL 方式）
SET session_replication_role = replica;

-- 2. 清理所有相關表的數據以確保沒有外鍵衝突
TRUNCATE TABLE cart_items RESTART IDENTITY CASCADE;
TRUNCATE TABLE order_items RESTART IDENTITY CASCADE;
TRUNCATE TABLE carts RESTART IDENTITY CASCADE;

-- 3. 重新啟用外鍵約束檢查
SET session_replication_role = DEFAULT;

-- 4. 驗證清理完成
DO $$
BEGIN
    RAISE NOTICE 'Foreign key cleanup completed. Products table can now be safely modified.';
END $$;