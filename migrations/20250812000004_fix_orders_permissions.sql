-- 修復訂單查看權限問題
-- 確保管理員能夠查看所有訂單

-- 插入 orders.read_all 權限（如果不存在）
INSERT INTO permissions (name, description, resource, action) VALUES 
    ('orders.read_all', '查看所有訂單', 'orders', 'read_all')
ON CONFLICT (name) DO NOTHING;

-- 為管理員和超級管理員角色添加查看所有訂單的權限
INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id 
FROM roles r, permissions p 
WHERE r.name IN ('admin', 'super_admin') 
AND p.name = 'orders.read_all'
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 更新現有管理員用戶確保他們有 admin 角色
UPDATE users 
SET role_id = (SELECT id FROM roles WHERE name = 'admin' LIMIT 1)
WHERE username = 'admin' AND role_id IS NULL;