-- 建立留言板表格
CREATE TABLE IF NOT EXISTS messageboard (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    subject VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 建立索引
CREATE INDEX IF NOT EXISTS idx_messageboard_user_id ON messageboard(user_id);
CREATE INDEX IF NOT EXISTS idx_messageboard_status ON messageboard(status);
CREATE INDEX IF NOT EXISTS idx_messageboard_created_at ON messageboard(created_at);