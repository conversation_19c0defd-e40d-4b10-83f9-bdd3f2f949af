-- 創建聊天訊息表
CREATE TABLE IF NOT EXISTS chat_messages (
    id BIGSERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    user_name VARCHAR(255) NOT NULL,
    admin_id VARCHAR(255),
    admin_name VA<PERSON>HA<PERSON>(255),
    message TEXT NOT NULL,
    message_type VARCHAR(10) NOT NULL CHECK (message_type IN ('user', 'admin')),
    replied BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(10) DEFAULT 'sent' CHECK (status IN ('sent', 'read', 'resolved'))
);

-- 創建索引
CREATE INDEX IF NOT EXISTS idx_chat_user_id ON chat_messages (user_id);
CREATE INDEX IF NOT EXISTS idx_chat_message_type ON chat_messages (message_type);
CREATE INDEX IF NOT EXISTS idx_chat_created_at ON chat_messages (created_at);
CREATE INDEX IF NOT EXISTS idx_chat_replied ON chat_messages (replied);
CREATE INDEX IF NOT EXISTS idx_chat_status ON chat_messages (status);
CREATE INDEX IF NOT EXISTS idx_chat_user_unreplied ON chat_messages (user_id, replied, message_type);

-- 創建更新時間觸發器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_chat_messages_updated_at BEFORE UPDATE
    ON chat_messages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入一些測試數據
INSERT INTO chat_messages (user_id, user_name, message, message_type, replied, created_at, status) VALUES
('pharmacy_001', '健康藥局', '請問這個產品有庫存嗎？', 'user', FALSE, NOW() - INTERVAL '1 hour', 'sent'),
('pharmacy_002', '康復藥局', '訂單什麼時候可以出貨？', 'user', FALSE, NOW() - INTERVAL '30 minutes', 'sent'),
('pharmacy_003', '新生藥局', '謝謝您的回覆，我了解了。', 'user', TRUE, NOW() - INTERVAL '15 minutes', 'sent');

-- 插入管理員回覆測試數據
INSERT INTO chat_messages (user_id, admin_id, admin_name, message, message_type, replied, created_at, status) VALUES
('pharmacy_003', 'admin', '客服人員', '不客氣！如果還有其他問題，隨時歡迎詢問。', 'admin', FALSE, NOW() - INTERVAL '10 minutes', 'sent');