-- 創建健保價格表
CREATE TABLE nhi_prices (
    nhi_price DECIMAL(10,2) PRIMARY KEY,
    selling_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 創建索引
CREATE INDEX idx_selling_price ON nhi_prices (selling_price);

-- 將現有產品的價格數據遷移到新表
INSERT INTO nhi_prices (nhi_price, selling_price)
SELECT DISTINCT unit_price as nhi_price, unit_price as selling_price 
FROM products 
WHERE unit_price IS NOT NULL;

-- 為products表添加nhi_price列
ALTER TABLE products ADD COLUMN nhi_price DECIMAL(10,2);

-- 將現有的unit_price值復制到nhi_price列
UPDATE products SET nhi_price = unit_price WHERE unit_price IS NOT NULL;

-- 添加外鍵約束
ALTER TABLE products ADD CONSTRAINT fk_products_nhi_price 
FOREIGN KEY (nhi_price) REFERENCES nhi_prices(nhi_price);

-- 刪除原來的price列（可選，看你要不要保留）
-- ALTER TABLE products DROP COLUMN price;