-- 確保管理員用戶有正確的權限設置

-- 確保有 admin 角色
INSERT INTO roles (name, description) VALUES 
    ('admin', '系統管理員')
ON CONFLICT (name) DO NOTHING;

-- 確保有必要的權限
INSERT INTO permissions (name, description, resource, action) VALUES 
    ('orders.read_all', '查看所有訂單', 'orders', 'read_all'),
    ('orders.read', '查看訂單', 'orders', 'read'),
    ('orders.write', '建立訂單', 'orders', 'write'),
    ('users.manage', '管理用戶', 'users', 'manage')
ON CONFLICT (name) DO NOTHING;

-- 為 admin 角色分配權限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id 
FROM roles r, permissions p 
WHERE r.name = 'admin' 
AND p.name IN ('orders.read_all', 'orders.read', 'orders.write', 'users.manage')
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 確保 admin 用戶有 admin 角色
UPDATE users 
SET role_id = (SELECT id FROM roles WHERE name = 'admin' LIMIT 1)
WHERE username = 'admin';

-- 如果 admin 用戶不存在，創建一個
INSERT INTO users (username, email, password_hash, role_id, created_at, updated_at, is_active) 
SELECT 
    'admin',
    '<EMAIL>',
    '$2b$12$dummy_hash_for_admin_user_please_change_this',
    r.id,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    true
FROM roles r 
WHERE r.name = 'admin'
ON CONFLICT (username) DO UPDATE SET
    role_id = EXCLUDED.role_id,
    is_active = true;