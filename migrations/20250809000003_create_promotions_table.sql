-- 建立促銷訊息表格
CREATE TABLE IF NOT EXISTS promotions (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    promotion_type VARCHAR(50) NOT NULL DEFAULT 'general',
    priority INTEGER NOT NULL DEFAULT 1,
    target_audience VARCHAR(50) NOT NULL DEFAULT 'all',
    is_active BOOLEAN NOT NULL DEFAULT true,
    starts_at TIMESTAMP WITH TIME ZONE,
    ends_at TIMESTAMP WITH TIME ZONE,
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 建立促銷訊息閱讀記錄表格（追蹤哪些用戶已讀過哪些訊息）
CREATE TABLE IF NOT EXISTS promotion_reads (
    id BIGSERIAL PRIMARY KEY,
    promotion_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    read_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (promotion_id) REFERENCES promotions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(promotion_id, user_id)
);

-- 創建索引提升查詢性能
CREATE INDEX idx_promotions_is_active ON promotions(is_active);
CREATE INDEX idx_promotions_created_at ON promotions(created_at);
CREATE INDEX idx_promotions_starts_at ON promotions(starts_at);
CREATE INDEX idx_promotions_ends_at ON promotions(ends_at);
CREATE INDEX idx_promotions_priority ON promotions(priority);
CREATE INDEX idx_promotion_reads_user_id ON promotion_reads(user_id);
CREATE INDEX idx_promotion_reads_promotion_id ON promotion_reads(promotion_id);

-- 插入範例促銷訊息
INSERT INTO promotions (title, content, promotion_type, priority, target_audience, is_active, starts_at, ends_at, created_by) VALUES 
('歡迎來到HappyOrder藥品供應平台！', 
 '感謝您註冊使用我們的平台。我們提供優質的藥品供應服務，幫助您的藥局更好地服務顧客。有任何問題請聯繫客服！', 
 'welcome', 
 1, 
 'all', 
 true, 
 CURRENT_TIMESTAMP, 
 CURRENT_TIMESTAMP + INTERVAL '30 days',
 1),
('本月特價優惠', 
 '本月精選藥品特價中！多款常用藥品享有特別優惠價格，數量有限，歡迎下單採購。', 
 'discount', 
 2, 
 'pharmacy', 
 true, 
 CURRENT_TIMESTAMP, 
 CURRENT_TIMESTAMP + INTERVAL '7 days',
 1);