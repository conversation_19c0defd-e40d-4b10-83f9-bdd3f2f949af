-- 簡化角色系統為三個等級：admin, pharmacy, viewer
-- 合併 super_admin 和 admin 角色

-- 首先將所有 super_admin 用戶轉移到 admin 角色
UPDATE users 
SET role_id = (SELECT id FROM roles WHERE name = 'admin')
WHERE role_id = (SELECT id FROM roles WHERE name = 'super_admin');

-- 將 super_admin 的所有權限合併到 admin 角色
INSERT INTO role_permissions (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'admin') as role_id,
    rp.permission_id
FROM role_permissions rp
WHERE rp.role_id = (SELECT id FROM roles WHERE name = 'super_admin')
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 刪除 super_admin 角色的權限關聯
DELETE FROM role_permissions 
WHERE role_id = (SELECT id FROM roles WHERE name = 'super_admin');

-- 刪除 super_admin 角色
DELETE FROM roles WHERE name = 'super_admin';

-- 同時刪除 viewer 角色，只保留三個等級：admin, pharmacy
-- 將所有 viewer 用戶轉移到 pharmacy 角色
UPDATE users 
SET role_id = (SELECT id FROM roles WHERE name = 'pharmacy')
WHERE role_id = (SELECT id FROM roles WHERE name = 'viewer');

-- 刪除 viewer 角色的權限關聯
DELETE FROM role_permissions 
WHERE role_id = (SELECT id FROM roles WHERE name = 'viewer');

-- 刪除 viewer 角色
DELETE FROM roles WHERE name = 'viewer';

-- 更新角色描述，使其更清晰
UPDATE roles SET 
    description = '管理員 - 擁有所有系統權限，可以管理產品、訂單、用戶和系統設定'
WHERE name = 'admin';

UPDATE roles SET 
    description = '藥局用戶 - 可以下訂單、查看產品和管理自己的資料'
WHERE name = 'pharmacy';

-- 確保 admin 角色擁有所有權限
INSERT INTO role_permissions (role_id, permission_id) 
SELECT 
    (SELECT id FROM roles WHERE name = 'admin') as role_id,
    p.id as permission_id
FROM permissions p
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 驗證結果
-- 顯示剩餘的角色
SELECT '=== 角色列表 ===' as info;
SELECT id, name, description FROM roles ORDER BY id;

-- 顯示每個角色的權限數量
SELECT '=== 角色權限統計 ===' as info;
SELECT 
    r.name as role_name,
    COUNT(rp.permission_id) as permission_count
FROM roles r
LEFT JOIN role_permissions rp ON r.id = rp.role_id
GROUP BY r.id, r.name
ORDER BY r.id;

-- 顯示用戶角色分布
SELECT '=== 用戶角色分布 ===' as info;
SELECT 
    r.name as role_name,
    COUNT(u.id) as user_count
FROM roles r
LEFT JOIN users u ON r.id = u.role_id
GROUP BY r.id, r.name
ORDER BY r.id;