-- 重構價格結構：將 products.unit_price 改為 selling_price，並建立與 nhi_prices 的同步機制
-- 步驟 1: 重命名 products 表的 unit_price 欄位為 selling_price
ALTER TABLE products RENAME COLUMN unit_price TO selling_price;

-- 步驟 2: 建立價格同步函數
CREATE OR REPLACE FUNCTION sync_product_prices()
RETURNS TRIGGER AS $$
BEGIN
    -- 當 nhi_prices 表的 selling_price 更新時，同步更新相關的 products
    IF TG_OP = 'UPDATE' AND OLD.selling_price != NEW.selling_price THEN
        UPDATE products 
        SET selling_price = NEW.selling_price,
            updated_at = CURRENT_TIMESTAMP
        WHERE nhi_code = NEW.nhi_code;
    END IF;
    
    -- 當新增 nhi_prices 記錄時，同步更新相關的 products
    IF TG_OP = 'INSERT' THEN
        UPDATE products 
        SET selling_price = NEW.selling_price,
            updated_at = CURRENT_TIMESTAMP
        WHERE nhi_code = NEW.nhi_code;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 步驟 3: 建立觸發器
DROP TRIGGER IF EXISTS trigger_sync_prices ON nhi_prices;
CREATE TRIGGER trigger_sync_prices
    AFTER INSERT OR UPDATE ON nhi_prices
    FOR EACH ROW
    EXECUTE FUNCTION sync_product_prices();

-- 步驟 4: 初始同步 - 將現有的 nhi_prices.selling_price 同步到 products.selling_price
UPDATE products 
SET selling_price = np.selling_price,
    updated_at = CURRENT_TIMESTAMP
FROM nhi_prices np 
WHERE products.nhi_code = np.nhi_code;

-- 步驟 5: 建立反向同步函數（當直接更新 products.selling_price 時，也更新 nhi_prices）
CREATE OR REPLACE FUNCTION sync_nhi_prices()
RETURNS TRIGGER AS $$
BEGIN
    -- 當 products 表的 selling_price 更新時，同步更新 nhi_prices
    IF TG_OP = 'UPDATE' AND OLD.selling_price != NEW.selling_price THEN
        UPDATE nhi_prices 
        SET selling_price = NEW.selling_price,
            updated_at = CURRENT_TIMESTAMP
        WHERE nhi_code = NEW.nhi_code;
        
        -- 如果 nhi_prices 中沒有對應記錄，則創建一個
        IF NOT FOUND THEN
            INSERT INTO nhi_prices (nhi_code, nhi_price, selling_price)
            VALUES (NEW.nhi_code, NEW.selling_price, NEW.selling_price)
            ON CONFLICT (nhi_code) DO UPDATE SET
                selling_price = NEW.selling_price,
                updated_at = CURRENT_TIMESTAMP;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 步驟 6: 建立反向同步觸發器
DROP TRIGGER IF EXISTS trigger_sync_nhi_prices ON products;
CREATE TRIGGER trigger_sync_nhi_prices
    AFTER UPDATE ON products
    FOR EACH ROW
    EXECUTE FUNCTION sync_nhi_prices();

-- 步驟 7: 更新索引（如果需要）
CREATE INDEX IF NOT EXISTS idx_products_selling_price ON products(selling_price);

-- 步驟 8: 添加註釋
COMMENT ON COLUMN products.selling_price IS '銷售價格，與 nhi_prices.selling_price 同步';
COMMENT ON FUNCTION sync_product_prices() IS '當 nhi_prices 更新時，自動同步 products.selling_price';
COMMENT ON FUNCTION sync_nhi_prices() IS '當 products.selling_price 更新時，自動同步 nhi_prices.selling_price';