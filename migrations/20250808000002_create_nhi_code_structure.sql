-- 創建健保價格表（使用 nhi_code 作為主鍵）
CREATE TABLE nhi_prices (
    nhi_code VARCHAR(20) PRIMARY KEY,
    nhi_price DECIMAL(10,2) NOT NULL,
    selling_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 創建索引
CREATE INDEX idx_nhi_price ON nhi_prices (nhi_price);
CREATE INDEX idx_selling_price ON nhi_prices (selling_price);

-- 將現有產品的價格數據遷移到新表，為每個不同價格分配 nhi_code
INSERT INTO nhi_prices (nhi_code, nhi_price, selling_price)
SELECT 
    'A' || LPAD(ROW_NUMBER() OVER (ORDER BY unit_price)::TEXT, 3, '0') as nhi_code,
    unit_price as nhi_price, 
    unit_price as selling_price
FROM (
    SELECT DISTINCT unit_price 
    FROM products 
    WHERE unit_price IS NOT NULL
) distinct_prices;

-- 為products表添加nhi_code列
ALTER TABLE products ADD COLUMN nhi_code VARCHAR(20);

-- 將價格對應到 nhi_code
UPDATE products 
SET nhi_code = np.nhi_code
FROM nhi_prices np
WHERE products.unit_price = np.nhi_price;

-- 添加外鍵約束
ALTER TABLE products ADD CONSTRAINT fk_products_nhi_code 
FOREIGN KEY (nhi_code) REFERENCES nhi_prices(nhi_code);