-- 添加用戶審核系統
-- 為 users 表添加審核相關欄位

ALTER TABLE users 
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS approved_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS approved_by INTEGER,
ADD COLUMN IF NOT EXISTS rejection_reason TEXT,
ADD COLUMN IF NOT EXISTS submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- 添加外鍵約束
ALTER TABLE users 
ADD CONSTRAINT fk_users_approved_by 
FOREIGN KEY (approved_by) REFERENCES users(id);

-- 創建索引以提高查詢效能
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_submitted_at ON users(submitted_at);

-- 更新現有用戶狀態（假設現有用戶都是已審核的）
UPDATE users 
SET status = 'approved', 
    approved_at = CURRENT_TIMESTAMP 
WHERE status IS NULL OR status = 'pending';

-- 添加狀態檢查約束
ALTER TABLE users 
ADD CONSTRAINT chk_user_status 
CHECK (status IN ('pending', 'approved', 'rejected'));

-- 創建審核日誌表
CREATE TABLE IF NOT EXISTS user_approval_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    action VARCHAR(20) NOT NULL, -- 'approve', 'reject', 'resubmit'
    reason TEXT,
    performed_by INTEGER NOT NULL REFERENCES users(id),
    performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    old_status VARCHAR(20),
    new_status VARCHAR(20)
);

-- 為審核日誌創建索引
CREATE INDEX IF NOT EXISTS idx_approval_logs_user_id ON user_approval_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_approval_logs_performed_at ON user_approval_logs(performed_at);

-- 插入審核相關權限
INSERT INTO permissions (name, description, resource, action) VALUES 
    ('users.approve', '審核用戶', 'users', 'approve'),
    ('users.reject', '拒絕用戶', 'users', 'reject'),
    ('users.view_pending', '查看待審核用戶', 'users', 'view_pending')
ON CONFLICT (name) DO NOTHING;

-- 為管理員角色添加審核權限
INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id 
FROM roles r, permissions p 
WHERE r.name IN ('admin', 'super_admin') 
AND p.name IN ('users.approve', 'users.reject', 'users.view_pending')
ON CONFLICT (role_id, permission_id) DO NOTHING;