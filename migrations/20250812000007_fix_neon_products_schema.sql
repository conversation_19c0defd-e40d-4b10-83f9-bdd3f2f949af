-- 修復 Neon 資料庫中 products 表的 schema
-- 添加缺少的欄位

-- 添加缺少的欄位（如果不存在）
ALTER TABLE products ADD COLUMN IF NOT EXISTS unit_price DECIMAL(10,2);
ALTER TABLE products ADD COLUMN IF NOT EXISTS stock_quantity INTEGER DEFAULT 0;
ALTER TABLE products ADD COLUMN IF NOT EXISTS description TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;
ALTER TABLE products ADD COLUMN IF NOT EXISTS ingredients TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS dosage_form VARCHAR(50);
ALTER TABLE products ADD COLUMN IF NOT EXISTS manufacturer VARCHAR(255);
ALTER TABLE products ADD COLUMN IF NOT EXISTS unit VARCHAR(20);

-- 確保必要的索引存在
CREATE INDEX IF NOT EXISTS idx_products_nhi_code ON products(nhi_code);
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);
CREATE INDEX IF NOT EXISTS idx_products_is_active ON products(is_active);

-- 更新現有產品的預設值（如果需要的話）
UPDATE products SET unit_price = 0 WHERE unit_price IS NULL;
UPDATE products SET stock_quantity = 0 WHERE stock_quantity IS NULL;
UPDATE products SET is_active = true WHERE is_active IS NULL;